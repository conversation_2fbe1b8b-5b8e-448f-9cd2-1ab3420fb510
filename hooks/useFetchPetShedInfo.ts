import { getPetShedInfo } from '@/server';
import { updateGameState } from '@/store/game';
import { useAppDispatch, useAppSelector } from './useStore';
import { IPetShedInfoList } from '@/constant/type';
import useLatest from './useLatest';

export default function useFetchPetShedInfo() {
  const dispatch = useAppDispatch();
  const petShedInfo = useAppSelector((state) => state.GameReducer.petShedInfo);
  const latestPetShedInfo = useLatest(petShedInfo);

  const fetchPetShedInfo = async () => {
    try {
      const res = await getPetShedInfo();
      if (res.data?.code === 1) {
        const data = res.data.data || [];
        const petShedInfo: Partial<IPetShedInfoList>[] = data.map((item) => ({
          positionTag: item.positionTag,
          userItemId: item.userItemId,
          userItemTag: item.userItemTag,
          waitingEndTime: item.waitingEndTime,
          petTag: item.petTag,
          slotRecordId: item._id,
          baseType: item.recordType,
          status: item.status,
          chainType: item.chainType,
        }));
        dispatch(updateGameState({ petShedInfo }));
      }
    } catch (error) {}
  };

  const updateReduxPetShed = (data: Partial<IPetShedInfoList>) => {
    const { positionTag, slotRecordId } = data;
    const originList = [...(latestPetShedInfo.current || [])];
    if (positionTag) {
      const idx = originList.findIndex((item) => item.positionTag === positionTag);
      if (idx !== -1) {
        originList[idx] = { ...originList[idx], ...data };
      } else {
        originList.push(data);
      }
    } else if (slotRecordId) {
      const idx = originList.findIndex((item) => item.slotRecordId === slotRecordId);
      if (idx !== -1) {
        originList[idx] = { ...originList[idx], ...data };
      }
    }
    dispatch(updateGameState({ petShedInfo: originList }));
  };

  return { fetchPetShedInfo, updateReduxPetShed };
}
