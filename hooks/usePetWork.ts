import { PetStatus } from '@/constant/enum';
import { useAppSelector } from './useStore';
import { petAxe, petPickAxe, petFish } from '@/server';
import toast from 'react-hot-toast';
import useUpdatePetListItem from './useUpdatePetListItem';
import { useCallback } from 'react';
import { IPetActionReport } from '@/server/index.types';
import { useNetWork } from '@/game/TS/useNetWork';

export default function usePetWork() {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const { updateGameStatePetItemData } = useUpdatePetListItem();
  const { sendPetCutTree, sendPetMining, sendPetFishing } = useNetWork();

  const getSummonPetList = useCallback(() => {
    const summonList = petList

      .filter((item) => item.petStatus !== PetStatus.REST)
      .sort((a, b) => (a.lastSummonedAt < b.lastSummonedAt ? -1 : 1));
    return summonList;
  }, [petList]);

  const getSummonPetListWithAxeFeature = useCallback(() => {
    const summonList = getSummonPetList();
    const summonListWithAxeFeature = summonList.filter((item) =>
      item.featureInfos.some((featureObj) => featureObj.feature === PetStatus.AXE)
    );
    return summonListWithAxeFeature;
  }, [petList]);

  const getSummonPetListWithPickaxeFeature = useCallback(() => {
    const summonList = getSummonPetList();
    const summonListWithAxeFeature = summonList.filter((item) =>
      item.featureInfos.some((featureObj) => featureObj.feature === PetStatus.PICKAXE)
    );
    return summonListWithAxeFeature;
  }, [petList]);

  const getSummonPetListWithFishFeature = useCallback(() => {
    const summonList = getSummonPetList();
    const summonListWithAxeFeature = summonList.filter((item) =>
      item.featureInfos.some((featureObj) => featureObj.feature === PetStatus.FISHING_POLE)
    );
    return summonListWithAxeFeature;
  }, [petList]);

  const getSummonPetListStatusInAxe = useCallback(() => {
    const summonList = getSummonPetList();
    const summonListWithAxeFeature = summonList.filter((item) => item.petStatus === PetStatus.AXE);
    return summonListWithAxeFeature;
  }, [petList]);

  const getSummonPetListStatusInPickaxe = useCallback(() => {
    const summonList = getSummonPetList();
    const summonListWithAxeFeature = summonList.filter(
      (item) => item.petStatus === PetStatus.PICKAXE
    );
    return summonListWithAxeFeature;
  }, [petList]);

  const getSummonPetListStatusInFish = useCallback(() => {
    const summonList = getSummonPetList();
    const summonListWithAxeFeature = summonList.filter(
      (item) => item.petStatus === PetStatus.FISHING_POLE
    );
    return summonListWithAxeFeature;
  }, [petList]);

  const handlePetAxe = async (
    data: { petId: string; targetId: string },
    successCallback?: (resData?: IPetActionReport) => void
  ) => {
    try {
      const res = await petAxe(data);
      if (res.data.code === 1) {
        const resData = res.data.data;
        updateGameStatePetItemData({ followList: [], targetPet: resData.userPet });
        if (successCallback) {
          successCallback(resData);
        }
      } else {
        toast.error(res.data.msg);
      }
    } catch (error) {
      console.log(error);
    }
  };
  const handlePetPickaxe = async (
    data: { petId: string; targetId: string },
    successCallback?: (resData?: IPetActionReport) => void
  ) => {
    try {
      const res = await petPickAxe(data);
      if (res.data.code === 1) {
        const resData = res.data.data;
        updateGameStatePetItemData({ followList: [], targetPet: resData.userPet });
        if (successCallback) {
          successCallback(resData);
        }
      } else {
        toast.error(res.data.msg);
      }
    } catch (error) {
      console.log(error);
    }
  };
  const handlePetFish = async (
    petId: string,
    successCallback?: (resData?: IPetActionReport) => void
  ) => {
    try {
      const res = await petFish(petId);
      if (res.data.code === 1) {
        const resData = res.data.data;
        updateGameStatePetItemData({ followList: [], targetPet: resData.userPet });
        if (successCallback) {
          successCallback(resData);
        }
      } else {
        toast.error(res.data.msg);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleSocketPetAxe = useCallback((...params: Parameters<typeof sendPetCutTree>) => {
    sendPetCutTree(...params);
  }, []);
  const handleSocketPetPickaxe = useCallback((...params: Parameters<typeof sendPetMining>) => {
    sendPetMining(...params);
  }, []);
  const handleSocketPetFishing = useCallback((...params: Parameters<typeof sendPetFishing>) => {
    sendPetFishing(...params);
  }, []);

  return {
    getSummonPetList,
    getSummonPetListWithAxeFeature,
    getSummonPetListWithPickaxeFeature,
    getSummonPetListWithFishFeature,
    getSummonPetListStatusInAxe,
    getSummonPetListStatusInPickaxe,
    getSummonPetListStatusInFish,
    handlePetAxe,
    handlePetPickaxe,
    handlePetFish,
    handleSocketPetAxe,
    handleSocketPetPickaxe,
    handleSocketPetFishing,
  };
}
