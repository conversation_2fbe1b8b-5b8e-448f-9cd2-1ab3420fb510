import { IAppState } from '@/constant/type';
import { completeFishEggTask, getFish } from '@/server';
import { setEasterEggInfo, setFishEasterEggModal, setIsFinished } from '@/store/app';
import { createParams, rsaEncrypt } from '@/utils';
import toast from 'react-hot-toast';
import { useDispatch, useSelector } from 'react-redux';
import { ClientAESCrypto, sha256 } from '@/utils/aes';
import { useCallback, useRef } from 'react';
import { useNetWork } from '@/game/TS/useNetWork';

export interface DecryptedDataJson {
  fishRecordId: string;
  currentDurability: string;
  score: number;
  tag: string;
  userItemId: string;
}

export const useFish = () => {
  const { btcAddress, userBasicInfo, axeParams, userScore } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const dispatch = useDispatch();
  const decryptedDataJsonRef = useRef<DecryptedDataJson | null>(null);
  const { sendFishingSuccess } = useNetWork();

  // 甩竿获取鱼儿
  const onGetFishing = useCallback(
    async (userItemId: string) => {
      if (!btcAddress) {
        toast.error('Connect your wallet');
        return;
      }

      try {
        const res = await getFish({ userItemId });
        const { code, data, msg } = res.data;
        if (code === 1) {
          // TODO: 对data进行AES解密
          const salt = await sha256(btcAddress);
          const key = await ClientAESCrypto.deriveKeyFromUserId(btcAddress, salt);

          if (!data || typeof data !== 'string') {
            // console.error("服务器返回的加密数据格式无效:", data);
            // toast.error("服务器返回的数据格式无效");
            return;
          }

          const decryptedData = await ClientAESCrypto.decrypt(data, key);

          if (!decryptedData) {
            // toast.error("数据解密失败");
            return;
          }

          try {
            const decryptedDataJson = JSON.parse(decryptedData) as DecryptedDataJson;
            decryptedDataJsonRef.current = decryptedDataJson;
            // 钓到鱼 id
            // toast.success("You caught a fish id = " + decryptedDataJson.tag);
            return decryptedDataJson;
          } catch (error) {
            // console.error("JSON 解析错误:", error);
            // toast.error("解密后的数据格式无效");
            return;
          }
        } else {
          // toast.error(msg);
        }
      } catch (error) {
        // console.error("钓鱼操作错误:", error);
        // toast.error(typeof error === 'string' ? error : "发生错误，请重试");
      }
    },
    [btcAddress, axeParams]
  );

  // 上报鱼儿积分
  const onSetFishScore = async () => {
    const { fishRecordId } = decryptedDataJsonRef.current || {};
    if (fishRecordId) {
      sendFishingSuccess(fishRecordId);
    }
  };

  // 完成彩蛋任务
  const onCompleteFishEggTask = async (cb?: () => void) => {
    try {
      const params = createParams(btcAddress, '/activity-fish/fish-easter-egg');
      const encrypted = rsaEncrypt(params);
      const headers = {
        sw: encrypted,
      };
      const res = await completeFishEggTask({}, headers);
      const { code, msg, data } = res?.data;
      if (code === 1) {
        // toast.success("Completed fish egg task successfully");
        const { domainInfos, isFinished } = data;
        dispatch(setIsFinished(isFinished));
        dispatch(setEasterEggInfo(domainInfos));
        if (isFinished) {
          cb?.();
          dispatch(setFishEasterEggModal(false));
        } else {
          setTimeout(() => {
            dispatch(setFishEasterEggModal(true));
          }, 300);
        }
      } else {
        toast.error(msg);
      }
    } catch (error) {
      toast.error(error as string);
    }
  };
  return {
    onGetFishing,
    userBasicInfo,
    axeParams,
    onSetFishScore,
    decryptedDataJsonRef,
    onCompleteFishEggTask,
  };
};
