import { AlkanePetTxEnum, petWeb3Orde<PERSON><PERSON>ist, petWeb3OrderQuery } from '@/server';
import { useAppSelector, useAppDispatch } from './useStore';
import { useEffect } from 'react';
import { updatePetOrder } from '@/store/petOrder';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { IPetInfoItem } from '@/constant/type';
import toast from 'react-hot-toast';
import useLatest from './useLatest';
import useFetchPetShedInfo from './useFetchPetShedInfo';

export default function useIntervalQueryPetOrder() {
  const myPlayer = GetMyPlayer();
  const btcAddress = useAppSelector((state) => state.AppReducer.btcAddress);
  const txidArr = useAppSelector((state) => state.PetOrderReducer.txidArr);

  const txidArrRef = useLatest(txidArr);

  const dispatch = useAppDispatch();
  const { fetchPetShedInfo } = useFetchPetShedInfo();

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.web3PetOrderQuery,
        async (_data: { newPetType: AlkanePetTxEnum; petInfo: IPetInfoItem }) => {
          // get new pet callback logic
          toast.success('Get New Pet');
        }
      );
    }
  }, []);

  const removeTxidFromCurrentAddressTxidArr = (txid: string) => {
    const latestOrderArr = txidArrRef.current;
    if (latestOrderArr && latestOrderArr.length > 0) {
      const newTxidArr = latestOrderArr.filter((item) => item !== txid);
      dispatch(updatePetOrder({ txidArr: newTxidArr }));
    }
  };

  const handleQueryPetOrder = async () => {
    const latestOrderArr = txidArrRef.current;
    const queryList = latestOrderArr.filter((item) => item);

    if (!queryList.length || !btcAddress) return;
    try {
      await Promise.all(
        queryList.map((item) =>
          petWeb3OrderQuery(item).then((res) => {
            if (res.data.code === 1) {
              const data = res.data.data;
              if (data.confirmed) {
                removeTxidFromCurrentAddressTxidArr(item);
                myPlayer.callAppApi(AppGameApiKey.web3PetOrderQuery, {
                  newPetType: data.txType,
                  petInfo: data.petInfo,
                });

                const slotData = data.slotRecord;
                if (slotData) {
                  const { slotRecordStatus, ...resData } = slotData;
                  myPlayer.callAppApi(AppGameApiKey.updatePetShedInfo, {
                    ...resData,
                    status: slotRecordStatus,
                  });
                } else {
                  fetchPetShedInfo();
                }
              }
            } else {
              if (res.data.code !== 10002) {
                toast.error(res.data.msg);
              }
            }
          })
        )
      );
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    const latestOrderArr = txidArr;
    const queryList = latestOrderArr?.filter((item) => item) || [];

    if (btcAddress && queryList.length > 0) {
      intervalId = setInterval(
        () => {
          handleQueryPetOrder();
        },
        1 * 30 * 1000
      );
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [btcAddress, txidArr]);

  const fetchOrderList = async (address: string) => {
    if (address) {
      try {
        const res = await petWeb3OrderList(address);
        if (res.data.code === 1) {
          const data = res.data.data;
          dispatch(updatePetOrder({ txidArr: data || [] }));
        }
      } catch (error) {
        console.error(error);
      }
    }
  };

  useEffect(() => {
    if (btcAddress) {
      fetchOrderList(btcAddress);
    }
  }, [btcAddress]);
}
