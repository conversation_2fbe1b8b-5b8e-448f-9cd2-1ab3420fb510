import { useEffect, useRef } from 'react';

type TargetType = HTMLElement | Element | Window | Document;
type TargetValue<T> = T | undefined | null;
export type BasicTarget<T extends TargetType = Element> =
  | (() => TargetValue<T>)
  | TargetValue<T>
  | React.MutableRefObject<TargetValue<T>>;

type DocumentEventKey = keyof DocumentEventMap;

function getTargetElement<T extends TargetType>(target: BasicTarget<T>, defaultElement?: T) {
  const isBrowser = !!(
    typeof window !== 'undefined' &&
    window.document &&
    window.document.createElement
  );

  if (!isBrowser) {
    return undefined;
  }

  if (!target) {
    return defaultElement;
  }

  let targetElement: TargetValue<T>;

  if (typeof target === 'function') {
    targetElement = target();
  } else if ('current' in target) {
    targetElement = target.current;
  } else {
    targetElement = target;
  }

  return targetElement;
}

function useClickAway<T extends Event = Event>(
  onClickAway: (event: T) => void,
  target: BasicTarget | BasicTarget[],
  eventName: DocumentEventKey | DocumentEventKey[] = ['mousedown', 'touchstart']
) {
  const onClickAwayRef = useRef(onClickAway);
  onClickAwayRef.current = onClickAway;

  useEffect(() => {
    const events = Array.isArray(eventName) ? eventName : [eventName];
    const targets = Array.isArray(target) ? target : [target];

    const handler = (event: Event) => {
      const eventTarget = (event as TouchEvent).touches?.[0]?.target || event.target;
      for (const t of targets) {
        const el = getTargetElement(t);
        if (!el) continue;
        if ('contains' in el && el.contains(eventTarget as Node)) {
          return;
        }
      }

      onClickAwayRef.current(event as T);
    };

    events.forEach((event) => {
      document.addEventListener(event, handler, { passive: true });
    });

    return () => {
      events.forEach((event) => {
        document.removeEventListener(event, handler);
      });
    };
  }, [target, eventName]);
}

export default useClickAway;
