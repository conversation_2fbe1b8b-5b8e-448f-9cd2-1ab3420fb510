import { ChangePetStatusRespData } from '@/server';
import { useAppDispatch, useAppSelector } from './useStore';
import useBagInventory from './useBagInventory';
import { useCallback } from 'react';
import { updateGameState } from '@/store/game';
import { IBagPetList, IPetInfoItem } from '@/constant/type';
import { PetConfig } from '@/game/Config/PetConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import useLatest from './useLatest';
import { INVENTORY_TYPE_ENUM } from '@/constant/enum';

export default function useUpdatePetListItem() {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const petListRef = useLatest(petList);
  const dispatch = useAppDispatch();
  const { getPetListData } = useBagInventory(false);
  const updateGameStatePetItemData = useCallback(
    ({
      followList,
      targetPet,
    }: {
      followList?: ChangePetStatusRespData['followList'];
      targetPet: Partial<ChangePetStatusRespData['targetPet']>;
    }) => {
      const petList = petListRef.current;

      const index = petList.findIndex((item) => item._id === targetPet._id);
      if (index === -1) {
        getPetListData();
      } else {
        const newPetList = [...petList];
        newPetList[index] = {
          ...newPetList[index],
          ...targetPet,
          bagConfigInfo: newPetList[index].bagConfigInfo,
        };
        if (followList?.length) {
          followList.forEach((item) => {
            const followSlotIdx = newPetList.findIndex((it) => it._id === item._id);
            newPetList[followSlotIdx] = {
              ...newPetList[followSlotIdx],
              followSlot: item.followSlot,
              petStatus: item.petStatus,
            };
          });
        }

        dispatch(updateGameState({ petList: newPetList }));
      }
    },
    [dispatch, getPetListData, petList]
  );

  const addNewPetIntoRedux = useCallback(
    (petItem: IPetInfoItem) => {
      const petList = petListRef.current || [];
      if (petList.findIndex((item) => item._id === petItem._id) !== -1) return;
      const newList = [...petList];
      PetConfig.getInstance().getData(petItem.tag, (configData) => {
        const name = petItem.petName || petItem.tag;
        const description = configData?.description || '';
        const icon = configData?.iconUrl || '';
        const newItem: IBagPetList = {
          ...petItem,
          bagConfigInfo: {
            name,
            description,
            icon,
            iconColor: configData?.iconColor || '',
            type: INVENTORY_TYPE_ENUM.pet,
            tag: petItem.tag,
            quality: petItem.quality,
            infoImageUrl: getCdnLink(configData.infoImageUrl || ''),
          },
        };
        newList.push(newItem);
      });
      dispatch(updateGameState({ petList: newList }));
    },
    [dispatch, petList]
  );

  return { updateGameStatePetItemData, addNewPetIntoRedux };
}
