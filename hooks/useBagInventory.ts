import { useDispatch, useSelector } from 'react-redux';
import { IAppState, IBagInventoryItem, IBagPetList, IGameState } from '../constant/type';
import { useEffect, useRef, useState } from 'react';
import { getBagInventoryList, getMaterialList, getPetList, getSyntheticsList } from '../server';
import {
  setBagInventoryList,
  setEquipmendResult,
  setMaterialList,
  setSyntheticsList,
  updateGameState,
} from '@/store/game';
import { ItemConfig, ItemType } from '@/game/Config/ItemConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { getDurabilityTips } from '@/utils/durabilityTips';
import { eventBus, EventTypes } from '@/utils/eventBus';
import { PetConfig } from '@/game/Config/PetConfig';
import { INVENTORY_TYPE_ENUM } from '@/constant/enum';

export default function useBagInventory(initGet?: boolean, forceRefresh?: boolean) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const { bagInventoryList, syntheticsList, equipmendResult, materialList } = useSelector(
    (state: { GameReducer: IGameState }) => state.GameReducer
  );

  const bagInventoryListRef = useRef<IBagInventoryItem[]>(bagInventoryList);
  bagInventoryListRef.current = bagInventoryList;

  const [isPending, setIsPending] = useState<boolean>(false);

  // 添加数据加载标记
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);
  const dispatch = useDispatch();

  // 一次性获取所有数据
  const addNewItem = async (
    userItemInfo: IBagInventoryItem,
    removeUserItemIds = [] as string[]
  ) => {
    let newList: IBagInventoryItem[] = [...bagInventoryListRef.current];
    if (removeUserItemIds.length) {
      newList = newList.filter((item) => !removeUserItemIds.includes(item.userItemId));
    }

    const isExist = !!newList.find((item) => item.userItemId === userItemInfo.userItemId);

    ItemConfig.getInstance().getData(Number(userItemInfo.tag), (data) => {
      const name = data?.name || userItemInfo.tag;
      const description = data?.description || '';
      const icon = getCdnLink(data?.icon_url || '');
      const isGlowWeapon = !!data?.effect_url;
      const quality = data?.quality || 0;
      const newItem = {
        ...userItemInfo,
        name,
        description,
        icon,
        isGlowWeapon,
        quality,
        shortcut: Number(userItemInfo.shortcut) ? userItemInfo.shortcut : '',
        isNew: true,
      };
      if (!isExist) {
        newList.push(newItem);
      }
      dispatch(setBagInventoryList([...newList]));
    });
  };

  // 一次性获取所有数据
  const updateDurability = async (userItemId: string, currentDurability: number) => {
    const newList = bagInventoryList.map((item) => {
      if (item.userItemId === userItemId) {
        return {
          ...item,
          currentDurability,
        };
      }
      return item;
    });
    dispatch(setBagInventoryList(newList));

    if (currentDurability === 0) {
      eventBus.publish(EventTypes.TASK_LIST_UPDATED, true);
    }

    // 添加耐久度提示
    if (currentDurability !== undefined) {
      getDurabilityTips(currentDurability);
    }
  };

  const removeItemFromRedux = (userItemId: string) => {
    const newList = [...bagInventoryListRef.current].filter(
      (item) => item.userItemId !== userItemId
    );
    dispatch(setBagInventoryList(newList));
  };

  const getPetListData = async () => {
    if (!btcAddress) {
      return Promise.resolve();
    }

    try {
      const res = await getPetList();
      if (res.data.code === 1) {
        const petDataList = res.data.data;
        if (petDataList && petDataList.length > 0) {
          let totalCount = petDataList.length;
          const newList: IBagPetList[] = [];
          for (let i = 0; i < petDataList.length; i++) {
            const item = petDataList[i];
            PetConfig.getInstance().getData(item.tag, (data) => {
              totalCount--;
              const name = item.petName || item.tag;
              const description = data?.description || '';
              const icon = data?.iconUrl || '';
              const newItem: IBagPetList = {
                ...item,
                bagConfigInfo: {
                  name,
                  description,
                  icon,
                  iconColor: data?.iconColor || '',
                  type: INVENTORY_TYPE_ENUM.pet,
                  tag: item.tag,
                  quality: item.quality,
                  infoImageUrl: getCdnLink(data.infoImageUrl || ''),
                },
              };
              newList.push(newItem);
              if (totalCount === 0) {
                dispatch(updateGameState({ petList: newList }));
              }
            });
          }
        } else {
          dispatch(updateGameState({ petList: [] }));
        }
      }
    } catch (error) {}
  };

  // 一次性获取所有数据
  const fetchAllData = async () => {
    setIsPending(true);

    try {
      // 并行请求两个接口
      await Promise.all([getBagInventoryListDta(), getMaterialListData(), getPetListData()]);

      // 标记数据已加载
      setDataLoaded(true);
    } catch (error) {
      console.error(error);
    } finally {
      setIsPending(false);
    }
  };

  // 获取材料列表
  const getMaterialListData = () => {
    if (!btcAddress) {
      return Promise.resolve();
    }

    setIsPending(true);

    return getMaterialList()
      .then((res) => {
        const materialList = res.data.data as IBagInventoryItem[];
        if (materialList && materialList.length > 0) {
          let totalCount = materialList.length;
          const newList: IBagInventoryItem[] = [];
          for (let i = 0; i < materialList.length; i++) {
            const item = materialList[i];
            ItemConfig.getInstance().getData(Number(item.tag), (data) => {
              totalCount--;
              const name = data?.name || item.tag;
              const description = data?.description || '';
              const icon = getCdnLink(data?.icon_url || '');
              const newItem = {
                ...item,
                name,
                description,
                icon,
              };
              newList.push(newItem);
              if (totalCount === 0) {
                dispatch(setMaterialList(newList));
              }
            });
          }
        } else {
          dispatch(setMaterialList([]));
        }
        return res;
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setIsPending(false);
      });
  };

  // 获取可合成的列表
  const getSyntheticsListData = () => {
    if (!btcAddress) {
      return Promise.resolve();
    }

    // 如果已经有数据且不强制刷新，则跳过
    // if (syntheticsList.length > 0 && !forceRefresh) {
    //   return Promise.resolve();
    // }

    setIsPending(true);

    return getSyntheticsList()
      .then((res) => {
        dispatch(setSyntheticsList(res.data.data || []));
        return res;
      })
      .finally(() => {
        setIsPending(false);
      });
  };

  // 获取背包物品和已经装备物品
  const getBagInventoryListDta = () => {
    if (!btcAddress) {
      return Promise.resolve();
    }

    setIsPending(true);

    return getBagInventoryList()
      .then((res) => {
        if (res.data.code === 1) {
          // dispatch(setBagInventoryList(packResult));
          // dispatch(setEquipmendResult(equipmedResult));
          const { packResult, equipmedResult } = res.data.data;
          const bagInventoryList = packResult as IBagInventoryItem[];
          if (bagInventoryList) {
            let totalCount = bagInventoryList.length;
            const newList: IBagInventoryItem[] = [];
            for (let i = 0; i < bagInventoryList.length; i++) {
              const item = bagInventoryList[i];

              ItemConfig.getInstance().getData(Number(item.tag), (data) => {
                totalCount--;
                const name = data?.name || item.tag;
                const description = data?.description || '';
                const icon = getCdnLink(data?.icon_url || '');
                //判断是否是发光武器
                const isGlowWeapon = !!data?.effect_url;
                const quality = data?.quality || 0;
                const newItem = {
                  ...item,
                  name,
                  description,
                  icon,
                  isGlowWeapon,
                  quality,
                };
                newList.push(newItem);
                if (totalCount === 0) {
                  dispatch(setBagInventoryList(newList));
                }
              });
            }
          } else {
            dispatch(setBagInventoryList([]));
          }
          dispatch(setEquipmendResult(equipmedResult || []));
        }
        return res;
      })
      .finally(() => {
        setIsPending(false);
      });
  };

  // 初始化时或强制刷新时加载数据
  useEffect(() => {
    if ((initGet && btcAddress && !dataLoaded) || forceRefresh) {
      fetchAllData();
    }
  }, [btcAddress, initGet, forceRefresh]);

  return {
    bagInventoryList,
    syntheticsList,
    equipmendResult,
    materialList,
    addNewItem,
    updateDurability,
    getSyntheticsListData,
    getBagInventoryListDta,
    fetchAllData,
    isPending,
    dataLoaded,
    getMaterialListData,
    removeItemFromRedux,
    getPetListData,
  };
}
