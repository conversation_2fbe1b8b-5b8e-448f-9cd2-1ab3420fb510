import { useCallback, useState } from 'react';
import { useAppDispatch, useAppSelector } from './useStore';
import { retrievePetBed, pickPetAltar } from '@/server';
import useBagInventory from './useBagInventory';
import { updateGameState } from '@/store/game';
import toast from 'react-hot-toast';
import { updateCache } from '@/store/cache';
import { updateModalState } from '@/store/modal';
import useLatest from './useLatest';

export function useRetrievePet() {
  const btcAddress = useAppSelector((state) => state.AppReducer.btcAddress);
  const isFirstGetPet = useAppSelector((state) => state.CacheReducer.isFirstGetPet);
  const { getBagInventoryListDta, getPetListData } = useBagInventory();
  const [loading, setLoading] = useState(false);
  const petShedInfo = useAppSelector((state) => state.GameReducer.petShedInfo);
  const dispatch = useAppDispatch();
  const petShedInfoRef = useLatest(petShedInfo);
  const retrievePet = useCallback(
    async (slotRecordId: string) => {
      if (!btcAddress) return;
      try {
        setLoading(true);
        const res = await retrievePetBed({ slotRecordId });
        if (res.data.code === 1) {
          getBagInventoryListDta();
          getPetListData();
          if (isFirstGetPet) {
            dispatch(
              updateModalState({
                petDescModalOpenConfig: {
                  isOpen: true,
                  confirmCallback: () => {
                    dispatch(updateCache({ isFirstGetPet: false }));
                  },
                },
              })
            );
          }

          const data = res.data.data;
          const petShedInfo = petShedInfoRef.current;
          const petShedIndex = petShedInfo.findIndex((item) => item.slotRecordId === slotRecordId);
          if (petShedIndex != -1) {
            const newPetShedInfo = [...petShedInfo];
            const originItem = newPetShedInfo[petShedIndex];
            if (data.currentDurability === 0 || !data.nextSlotRecordId) {
              newPetShedInfo.splice(petShedIndex, 1);
            } else {
              newPetShedInfo[petShedIndex] = {
                ...originItem,
                petTag: '',
                slotRecordId: data.nextSlotRecordId,
                waitingEndTime: data.waitingEndTime,
              };
            }
            dispatch(updateGameState({ petShedInfo: newPetShedInfo }));
          }
        } else {
          setLoading(false);
          toast.error(res.data.msg);
        }
      } catch (error) {
        setLoading(false);
      }
      setLoading(false);
    },
    [btcAddress, petShedInfo, isFirstGetPet]
  );

  return { retrievePet, loading };
}

export function usePickPetAltar() {
  const btcAddress = useAppSelector((state) => state.AppReducer.btcAddress);
  const { getBagInventoryListDta } = useBagInventory();
  const [loading, setLoading] = useState(false);
  const petShedInfo = useAppSelector((state) => state.GameReducer.petShedInfo);
  const petShedInfoRef = useLatest(petShedInfo);

  const dispatch = useAppDispatch();
  const handlePickPetAltar = useCallback(
    async ({ positionTag, userItemId }: Partial<{ positionTag: string; userItemId: string }>) => {
      if (!btcAddress || !positionTag || !userItemId) return;

      const petShedInfo = petShedInfoRef.current;

      try {
        setLoading(true);
        const res = await pickPetAltar({ positionTag, userItemId });
        if (res.data.code === 1) {
          getBagInventoryListDta();
          const newPetShedInfo = petShedInfo.filter((item) => item.positionTag !== positionTag);
          dispatch(updateGameState({ petShedInfo: newPetShedInfo }));
        }
      } catch (error) {
        console.log(error);
        setLoading(false);
      }
      setLoading(false);
    },
    [btcAddress, petShedInfo]
  );

  return { handlePickPetAltar, loading };
}
