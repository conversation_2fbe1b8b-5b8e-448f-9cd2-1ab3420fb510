import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from './useStore';
import useIntervalQueryPetOrder from '@/hooks/useIntervalQueryPetOrder';
import useFetchPetShedInfo from './useFetchPetShedInfo';
import { useSceneChange } from './useSceneChange';
import { ruleImageMap } from '@/components/EventRules/constant';
import { AnimationSequenceConfig } from '@/components/AnimationSequence/config';
import useBagInventory from './useBagInventory';
import { resetModalState } from '@/store/modal';

function preloadImages(images: string[]) {
  images.forEach((src) => {
    const img = new Image();
    img.src = src;
  });
}

function usePreloadEventRuleImage() {
  useEffect(() => {
    const arr = Array.from(Object.values(ruleImageMap)).filter((item) => item.preload);
    const animationArr = Array.from(Object.values(AnimationSequenceConfig));
    const allAnimationArr = animationArr.reduce((total, cur) => {
      const imgArr = Array.from(Object.values(cur)) || [];
      total.push(...imgArr);
      return total;
    }, [] as string[]);
    const groupArr = arr.map((item) => {
      const images = item.popupImages.map((it) => it.src);
      return [...images, item.bgImgSrc, item.titleBgSrc];
    });
    groupArr.forEach((arrItem) => {
      preloadImages(arrItem);
    });
    setTimeout(() => {
      preloadImages(allAnimationArr);
    }, 2000);
  }, []);
}

export default function useGameEnter() {
  const btcAddress = useAppSelector((state) => state.AppReducer.btcAddress);
  const dispatch = useAppDispatch();
  const { getMaterialListData } = useBagInventory(false, false);

  const { fetchPetShedInfo } = useFetchPetShedInfo();
  useSceneChange();
  usePreloadEventRuleImage();
  useEffect(() => {
    if (btcAddress) {
      fetchPetShedInfo();
      getMaterialListData();
      dispatch(resetModalState());
    } else {
      dispatch(resetModalState());
    }
  }, [btcAddress]);

  useIntervalQueryPetOrder();
}
