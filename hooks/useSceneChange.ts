import { useEffect, useState } from 'react';
import { useAppSelector } from './useStore';
import usePetWork from './usePetWork';
import { PetStatus } from '@/constant/enum';
import useBagInventory from './useBagInventory';
import { changePetAllFollow } from '@/server';

export function useSceneChange() {
  const { sceneType, btcAddress } = useAppSelector((state) => state.AppReducer);
  const { getSummonPetList } = usePetWork();
  const [, setCurrentScene] = useState(sceneType);
  const { getPetListData } = useBagInventory();

  const allPetFollow = async () => {
    if (btcAddress) {
      try {
        const res = await changePetAllFollow();
        if (res.data.code === 1) {
          getPetListData();
        }
      } catch (error) {}
    }
  };

  useEffect(() => {
    if (btcAddress) {
      allPetFollow();
    }
  }, [btcAddress]);

  useEffect(() => {
    setCurrentScene((prev) => {
      if (prev !== sceneType) {
        const list = getSummonPetList();
        const ids =
          list.filter((item) => item.petStatus !== PetStatus.FOLLOW).map((item) => item._id) || [];
        if (ids.length > 0) allPetFollow();
      }
      return sceneType;
    });
  }, [sceneType]);
}
