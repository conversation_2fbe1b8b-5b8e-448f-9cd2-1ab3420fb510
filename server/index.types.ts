import { IPetInfoItem } from "@/constant/type";

export interface PlayerEnergyInfo {
  energy: number;
  totalEnergy: number;
  freeTime: number;
}
export interface EnergyConsumeConfig {
  action: string;
  consume: number;
}
interface UserTaskProgress {
  id: string;
  taskId: string;
  taskType: string;
  taskTitle: string;
  taskDesc: string;
  taskRewards: TaskReward[];
  taskProgress: TaskProgress[];
  clientRefresh: string[];
  status: string;
  expireAt?: number;
}
interface TaskProgress {
  conditionType: string;
  conditionDesc: string;
  targetId?: any;
  requiredCount: number;
  currentCount: number;
}
interface TaskReward {
  itemType: string;
  itemTag: string;
  quantity: number;
}
interface RankInfo {
  activityTag: string;
  score?: number;
  rank?: number;
  rankType: string;
  rankInfo?: RankInfoItem[];
}
interface RankInfoItem {
  rank: number;
  address: string;
  score: number;
}
interface WorkbenchConfig {
  workbenchTotalCount: number;
  workbenchCurrentCount: number;
}
interface ToolConfig {
  dailyBuyTotalCount: number;
  dailyCurrentBuyCount: number;
}
interface MaterialItemList {
  tag: string;
  score: number;
}
interface ActivityInfo {
  name: string;
  activityTag: string;
  startTime: number;
  endTime: number;
  communityFlag: boolean;
  description: string;
}

export interface IUerBasicInfo {
  twitterFlag: boolean;
  refreshFlag: boolean;
  activityInfos: ActivityInfo[];
  materialItemList: MaterialItemList[];
  toolConfig: ToolConfig;
  workbenchConfig: WorkbenchConfig;
  rankInfos: RankInfo[];
  userTaskProgresses: UserTaskProgress[];
  energyConsumeConfigs: EnergyConsumeConfig[];
  playerInfo: PlayerEnergyInfo;
  [k: string]: any;
}

export interface IGetBatchNewRankingList {
  activityTag: string;
  data: {
    rankInfo: RankInfoItem[];
    score: number;
    rank: number;
  };
}

export interface IPetActionReport {
  materialInfo:{
    materialTag:string,
    quantity:number
  }
  userPet:IPetInfoItem
}