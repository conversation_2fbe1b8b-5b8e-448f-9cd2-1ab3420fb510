import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import AppReducer from './app';
import GameReducer from './game';
import EventsReducer from './events';
import SettingReducer from './setting';
import ModalReducer from './modal';
import PetOrderReducer from './petOrder';
import CacheReducer from './cache';

const persistConfig = {
  key: 'setting',
  storage,
  whitelist: ['SettingReducer', 'PetOrderReducer', 'CacheReducer'],
};

const rootReducer = combineReducers({
  AppReducer,
  GameReducer,
  EventsReducer,
  SettingReducer,
  ModalReducer,
  PetOrderReducer,
  CacheReducer,
});

const persistRootReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistRootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
