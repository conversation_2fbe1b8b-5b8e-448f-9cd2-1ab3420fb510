import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ICacheState {
  isFirstOpenCraftPet: boolean;
  isFirstGetPet: boolean;
}

const initialState: ICacheState = {
  isFirstOpenCraftPet: true,
  isFirstGetPet: true,
};

export const CacheSlice = createSlice({
  name: 'cache',
  initialState,
  reducers: {
    updateCache(state, action: PayloadAction<Partial<ICacheState>>) {
      state.isFirstOpenCraftPet = action.payload.isFirstOpenCraftPet ?? state.isFirstOpenCraftPet;
      state.isFirstGetPet = action.payload.isFirstGetPet ?? state.isFirstGetPet;
    },
  },
});
export const { updateCache } = CacheSlice.actions;
export default CacheSlice.reducer;
