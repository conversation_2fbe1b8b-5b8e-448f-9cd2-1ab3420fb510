import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';
import {
  CHARACTER_ENUM,
  IAppState,
  IPotatoTime,
  IttsBuildData,
  SCENE_TYPE,
  STORAGE_MENU_ENUM,
  SUPPORT_WALLET_ENUM,
} from '@/constant/type';
import { LoadingPageType } from '@/game/Config/DoorConfig';

// const test =
// '[{"tag":4,"interval":1446,"pause":1389},{"tag":12,"interval":1553,"pause":2393},{"tag":11,"interval":1499,"pause":1801},{"tag":10,"interval":1603,"pause":2687},{"tag":9,"interval":1487,"pause":1041},{"tag":8,"interval":1846,"pause":2679},{"tag":2,"interval":1782,"pause":1968},{"tag":6,"interval":1818,"pause":1056},{"tag":7,"interval":1714,"pause":1010},{"tag":5,"interval":1046,"pause":1315}]';

const initialState: IAppState = {
  btcWallet: SUPPORT_WALLET_ENUM.unisat, // 钱包类型
  btcAddress: '', // 钱包地址
  showConnectWallet: false, // 是否显示连接钱包按钮
  defaultInscriptionId: '', //首页进来默认形象，从登录接口获取
  pageLoadingRate: 0, //场景加载进度 小于0显示 等于100隐藏
  potatoTime: null, // 土豆时间
  sceneType: SCENE_TYPE.Room, // 场景类型
  isRunJumpLogic: false, //是否执行过跳转逻辑,
  domainOwner: false, //当前域名的持有者
  // storageMenu: STORAGE_MENU_ENUM.PATH_MENU, // 存储菜单
  storageMenu: null, // TODO: 默认不选中任何按钮
  isRecording: false, //是否正在录屏
  ttsBuildData: null, //当前域名绑定的录屏数据
  usedCharacter: CHARACTER_ENUM.character, //当前操控的角色 默认角色|宠物
  isTtsWhiteList: false, //是否有tts白名单权限
  axeParams: null, // 通过领取装备接口返回userItemId
  treeList: null, // 树木列表
  userBasicInfo: null, // 用户基础信息
  userScore: null, // 用户积分
  leftTime: undefined, // 剩余时间
  rockList: null, // 矿石列表
  menuType: '', // 排行榜菜单类型
  dogEasterEgg: null, // 狗头彩蛋
  whackAMoleEasterEgg: null, // 打地鼠彩蛋
  easterEggInfo: undefined, // 钓鱼彩蛋信息
  fishEasterEggModal: false, // 钓鱼彩蛋进度弹窗
  isFinished: false, // 钓鱼彩蛋是否完成
  menuVisible: false, // 初识菜单是否可见
  loaderType: LoadingPageType.Default, // 加载器类型
  leaderboard: null, // 当前活动排行榜统计数据
  easterEgg: null, // 彩蛋信息
  rockLeftTime: undefined, // 挖矿冷却时间
  treeLeftTime: undefined, // 砍树冷却时间
  randomEventResult: null, //随机彩蛋
  easterEggReward: null,
  unusableInfo: null, // 不可用信息
  taskInfo: null, // 任务信息
  isMobile: false,
};

export const AppSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    // 设置钱包地址
    setBtcAddress: (state, action: PayloadAction<string>) => {
      state.btcAddress = action.payload;
    },
    // 设置钱包类型
    setBtcWallet: (state, action: PayloadAction<SUPPORT_WALLET_ENUM>) => {
      state.btcWallet = action.payload;
    },
    // 设置是否显示连接钱包按钮
    setShowConnectWallet: (state, action: PayloadAction<boolean>) => {
      state.showConnectWallet = action.payload;
    },
    // 设置首页进来默认形象
    setDefaultInscriptionId: (state, action: PayloadAction<string>) => {
      state.defaultInscriptionId = action.payload;
    },
    // 设置场景加载进度
    setPageLoadingRate: (state, action: PayloadAction<number>) => {
      state.pageLoadingRate = action.payload;
    },
    // 设置土豆时间
    setPotatoTime: (state, action: PayloadAction<IPotatoTime>) => {
      state.potatoTime = action.payload;
    },
    // 设置场景类型
    setSceneType: (state, action: PayloadAction<SCENE_TYPE>) => {
      state.sceneType = action.payload;
    },
    // 设置是否执行过跳转逻辑
    setIsRunJumpLogic: (state, action: PayloadAction<boolean>) => {
      state.isRunJumpLogic = action.payload;
    },
    // 设置当前域名的持有者
    setDomainOwner: (state, action: PayloadAction<boolean>) => {
      state.domainOwner = action.payload;
    },
    // 设置存储菜单
    setStorageMenu: (state, action: PayloadAction<STORAGE_MENU_ENUM | null>) => {
      state.storageMenu = action.payload;
    },
    // 设置菜单是否可见
    setMenuVisible: (state, action: PayloadAction<boolean>) => {
      state.menuVisible = action.payload;
    },
    // 设置是否正在录屏
    setIsRecording: (state, action: PayloadAction<boolean>) => {
      state.isRecording = action.payload;
    },
    // 设置tts构建数据
    setTtsBuildData: (state, action: PayloadAction<IttsBuildData | null>) => {
      state.ttsBuildData = action.payload;
    },
    // 设置当前操控的角色
    setUsedCharacter: (state, action: PayloadAction<CHARACTER_ENUM>) => {
      state.usedCharacter = action.payload;
    },
    // 设置是否为tts白名单
    setIsTtsWhiteList: (state, action: PayloadAction<boolean>) => {
      state.isTtsWhiteList = action.payload;
    },
    /**
     * 设置斧头参数
     * @param state
     * @param action
     */
    setAxeParams: (state, action: PayloadAction<IAppState['axeParams']>) => {
      state.axeParams = action.payload;
    },
    /**
     * 设置树木列表
     * @param state
     * @param action
     */
    setTreeList: (state, action: PayloadAction<IAppState['treeList']>) => {
      state.treeList = action.payload;
    },
    /**
     * 设置用户基础信息
     * @param state
     * @param action
     */
    setUserBasicInfo: (state, action: PayloadAction<IAppState['userBasicInfo']>) => {
      state.userBasicInfo = action.payload;
    },
    /**
     * 设置用户积分
     * @param state
     * @param action
     */
    setUserScore: (state, action: PayloadAction<IAppState['userScore']>) => {
      state.userScore = action.payload;
    },
    /**
     * 设置剩余时间
     * @param state
     * @param action
     */
    setLeftTime: (state, action: PayloadAction<number>) => {
      state.leftTime = action.payload;
    },
    /**
     * 设置矿石列表
     * @param state
     * @param action
     */
    setRockList: (state, action: PayloadAction<IAppState['rockList']>) => {
      state.rockList = action.payload;
    },
    /**
     * 设置排行榜菜单类型
     * @param state
     * @param action
     */
    setMenuType: (state, action: PayloadAction<string>) => {
      state.menuType = action.payload;
    },
    /**
     * 设置狗头彩蛋
     * @param state
     * @param action
     */
    setDogEasterEgg: (state, action: PayloadAction<IAppState['dogEasterEgg']>) => {
      state.dogEasterEgg = action.payload;
    },
    /**
     * 设置打地鼠彩蛋
     * @param state
     * @param action
     */
    setWhackAMoleEasterEgg: (state, action: PayloadAction<IAppState['whackAMoleEasterEgg']>) => {
      state.whackAMoleEasterEgg = action.payload;
    },
    /**
     * 设置钓鱼彩蛋
     * @param state
     * @param action
     */
    setEasterEggInfo: (state, action: PayloadAction<IAppState['easterEggInfo']>) => {
      state.easterEggInfo = action.payload;
    },
    /**
     * 设置钓鱼彩蛋进度弹窗
     * @param state
     * @param action
     */
    setFishEasterEggModal: (state, action: PayloadAction<boolean>) => {
      state.fishEasterEggModal = action.payload;
    },
    /**
     * 设置钓鱼彩蛋是否完成
     * @param state
     * @param action
     */
    setIsFinished: (state, action: PayloadAction<boolean>) => {
      state.isFinished = action.payload;
    },
    /**
     * 设置加载器类型
     * @param state
     * @param action
     */
    setLoaderType: (state, action: PayloadAction<LoadingPageType>) => {
      state.loaderType = action.payload;
    },
    /**
     * 设置排行榜统计数据
     * @param state
     * @param action
     */
    setLeaderboard: (state, action: PayloadAction<IAppState['leaderboard']>) => {
      state.leaderboard = action.payload;
    },
    /**
     * 设置挖矿冷却时间
     * @param state
     * @param action
     */
    setRockLeftTime: (state, action: PayloadAction<number | undefined>) => {
      state.rockLeftTime = action.payload;
    },
    /**
     * 设置砍树冷却时间
     * @param state
     * @param action
     */
    setTreeLeftTime: (state, action: PayloadAction<number | undefined>) => {
      state.treeLeftTime = action.payload;
    },
    /**
     * 设置随机彩蛋
     * @param state
     * @param action
     */
    setRandomEventResult: (state, action: PayloadAction<IAppState['randomEventResult']>) => {
      state.randomEventResult = action.payload;
    },

    /**
     * 设置彩蛋奖励信息到全局以供彩蛋奖励弹窗显示
     */
    setEasterEggReward: (state, action: PayloadAction<IAppState['easterEggReward']>) => {
      state.easterEggReward = action.payload;
    },
    /**
     * 设置不可用信息
     * @param state
     * @param action
     */
    setUnusableInfo: (state, action: PayloadAction<IAppState['unusableInfo']>) => {
      state.unusableInfo = action.payload;
    },
    /**
     * 设置任务信息
     * @param state
     * @param action
     */
    setTaskInfo: (state, action: PayloadAction<IAppState['taskInfo']>) => {
      state.taskInfo = action.payload;
    },

    setIsMobile: (state, action: PayloadAction<boolean>) => {
      state.isMobile = action.payload;
    },
    /**
     * 重置状态
     * @param state
     */
    resetStates: (state) => {
      state.leftTime = undefined;
      state.axeParams = null;
      state.treeList = null;
      state.userBasicInfo = null;
      state.userScore = null;
      state.rockList = null;
      state.menuType = '';
      state.dogEasterEgg = null;
      state.whackAMoleEasterEgg = null;
      state.easterEggInfo = undefined;
      state.isFinished = false;
      state.loaderType = LoadingPageType.Default;
      state.leaderboard = null;
      state.rockLeftTime = 0;
      state.treeLeftTime = 0;
      state.unusableInfo = null;
      state.taskInfo = null;
    },
  },
});
export const {
  setBtcAddress,
  setBtcWallet,
  setShowConnectWallet,
  setDefaultInscriptionId,
  setPageLoadingRate,
  setPotatoTime,
  setSceneType,
  setIsRunJumpLogic,
  setDomainOwner,
  setStorageMenu,
  setIsRecording,
  setTtsBuildData,
  setUsedCharacter,
  setIsTtsWhiteList,
  setAxeParams,
  setTreeList,
  setUserBasicInfo,
  setUserScore,
  setLeftTime,
  resetStates,
  setRockList,
  setMenuType,
  setDogEasterEgg,
  setWhackAMoleEasterEgg,
  setEasterEggInfo,
  setFishEasterEggModal,
  setIsFinished,
  setMenuVisible,
  setLoaderType,
  setLeaderboard,
  setRockLeftTime,
  setTreeLeftTime,
  setRandomEventResult,
  setUnusableInfo,
  setTaskInfo,
  setEasterEggReward,
  setIsMobile,
} = AppSlice.actions;
export default AppSlice.reducer;
