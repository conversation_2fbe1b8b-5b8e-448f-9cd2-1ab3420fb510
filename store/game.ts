import { IBagInventoryItem, IGameState, ISyntheticItem } from '../constant/type';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState: IGameState = {
  bagInventoryList: [], //背包的所有物品
  equipmendResult: [], //已经装备到身上的物品
  syntheticsList: [], //可合成的列表
  materialList: [], //材料列表
  petShedInfo: [],
  petList: [],
};

export const GameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    updateGameState(state, action: PayloadAction<Partial<IGameState>>) {
      const { petShedInfo, petList } = action.payload;
      state.petShedInfo = petShedInfo ?? state.petShedInfo;
      state.petList = petList ?? state.petList;
    },
    setBagInventoryList(state, action: PayloadAction<IBagInventoryItem[]>) {
      state.bagInventoryList = action.payload;
    },
    setEquipmendResult(state, action: PayloadAction<IBagInventoryItem[]>) {
      state.equipmendResult = action.payload;
    },
    setSyntheticsList(state, action: PayloadAction<ISyntheticItem[]>) {
      state.syntheticsList = action.payload;
    },
    setMaterialList(state, action: PayloadAction<IBagInventoryItem[]>) {
      state.materialList = action.payload;
    },
    resetGameState(state) {
      state.bagInventoryList = [];
      state.equipmendResult = [];
      state.syntheticsList = [];
      state.materialList = [];
      state.petShedInfo = [];
      state.petList = [];
    },
  },
});
export const {
  setBagInventoryList,
  setEquipmendResult,
  setSyntheticsList,
  setMaterialList,
  resetGameState,
  updateGameState,
} = GameSlice.actions;
export default GameSlice.reducer;
