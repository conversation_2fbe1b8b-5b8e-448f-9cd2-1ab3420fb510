import BigNumber from 'bignumber.js';
import NodeRSA from 'node-rsa';
import { NETWORK_ENUM, SUPPORT_WALLET_ENUM } from '../constant/type';
// @ts-ignore
import compressImage from 'ls-compressimage';
import { APP_NETWORK, ORD_NFT_IMG_SERVER, ORD_SERVER } from '../constant';

export function toFormatAccount(account: string, left: number = 6, right: number = 6) {
  if (!account) {
    return '';
  }
  return account.slice(0, left) + '...' + account.slice(account.length - right, account.length);
}

export const satsToBtc = (sats: number | string, dp = 8) => {
  return BigNumber(sats)
    .div(10 ** 8)
    .dp(dp)
    .toString();
};

export async function getPublicKey(wallet: SUPPORT_WALLET_ENUM) {
  if (wallet === SUPPORT_WALLET_ENUM.unisat) {
    return window.unisat.getPublicKey();
  } else if (wallet === SUPPORT_WALLET_ENUM.okx) {
    if (APP_NETWORK === NETWORK_ENUM.BITCOIN_SIGNET) {
      const res = await window.okxwallet.bitcoinSignet.connect();
      return res.compressedPublicKey;
    }
    return window.okxwallet.fractalBitcoin.getPublicKey();
  }
  return '';
}

export const signPsbt = (wallet: SUPPORT_WALLET_ENUM, hex: string, account: string) => {
  if (wallet === SUPPORT_WALLET_ENUM.unisat) {
    return window.unisat.signPsbt(hex).catch((e: any) => {
      console.log('signPsbt error', e);
      return null;
    });
  } else {
    if (APP_NETWORK === NETWORK_ENUM.BITCOIN_SIGNET) {
      return window.okxwallet.bitcoinSignet
        .signPsbt(hex, { autoFinalized: true })
        .catch((e: any) => {
          console.log('signPsbt error', e);
          return null;
        });
    }
    return window.okxwallet.fractalBitcoin
      .signPsbt(hex, { autoFinalized: true })
      .catch((e: any) => {
        console.log('signPsbt error', e);
        return null;
      });
  }
};

export const getLocalSession = (address: string) => {
  const session = localStorage.getItem(`${address}_session`);
  if (session) {
    const [sessionId, expires] = session.split('_');
    return {
      sessionId,
      expires: +expires,
    };
  }
  return {
    sessionId: '',
    expires: 0,
  };
};

export const getLocalSessionId = (address: string) => {
  const session = localStorage.getItem(`${address}_session`);
  if (session) {
    const [sessionId, expires] = session.split('_');
    return sessionId;
  }
  return '';
};

export const setLocalSession = (address: string, sessionId: string, expires: number) => {
  if (!sessionId) {
    localStorage.removeItem(`${address}_session`);
    return;
  }
  localStorage.setItem(`${address}_session`, `${sessionId}_${expires}`);
};
export const compressImageUtil = (base64: string): Promise<string> =>
  new Promise((resolve) => {
    new compressImage({
      res: base64,
      justConversion: false,
      width: 300,
      //height: 200,
      quality: 0.01,
      size: 300,
      requestType: 'base64',
      responseType: 'base64',
      imgType: 'image/png',
      fileName: 'fff',
      callback: (result: any) => {
        if (result === null) {
          resolve('');
          return;
        }
        resolve(result);
      },
    });
  });
export const getLocationParams = (key: string) => {
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search);
  return params.get(key);
};
export const checkIsBtcAddress = (address: string) => {
  return /^bc1[a-zA-HJ-NP-Z0-9]{39,59}$/.test(address);
};
export const getServerLink = (inscriptionId: string) => {
  // return `${REQUEST_URL}/inscription/content/${inscriptionId}`
  return `${ORD_SERVER}/content/${inscriptionId}`;
};
export const getNFTImgLink = (inscriptionId: string) => {
  return `${ORD_NFT_IMG_SERVER || ORD_SERVER}/content/${inscriptionId}`;
};
export const checkCollectionName = (name: string) => {
  if (!name || name.length < 1 || name.length > 32) {
    return false;
  }
  return /^[A-Za-z0-9!@#$%^&*()_+={}\[\]:;"·'<>,.?\\/|`~\\-]*$/.test(name);
};
// 截图
export const exportPreviewImage = (domId: string): string => {
  const renderTarget: HTMLElement | null = document.getElementById(domId);
  if (renderTarget === null) {
    console.error('render-target not found');
    return '';
  }

  const canvas = renderTarget.getElementsByTagName('canvas')[0];
  if (canvas === null) {
    console.error('canvas not found');
    return '';
  }
  /* const canvasWidth = canvas.width;
  const canvasHeight = canvas.height;
  // 计算正方形的边长（取较小的边长）
  const squareSize = Math.min(canvasWidth, canvasHeight);
  // 计算正方形的起始位置，使其位于canvas的中心
  const offsetX = (canvasWidth - squareSize) / 2;
  const offsetY = (canvasHeight - squareSize) / 2;

   // 创建一个新的canvas用于导出
   const exportCanvas = document.createElement('canvas');
   exportCanvas.width = squareSize;
   exportCanvas.height = squareSize;
   // 获取导出canvas的上下文
   const exportCtx = exportCanvas.getContext('2d');
   // 将原canvas中间的正方形区域绘制到新canvas
   // @ts-ignore
   exportCtx.drawImage(canvas, offsetX, offsetY, squareSize, squareSize, 0, 0, squareSize, squareSize);*/
  // 导出为图片URL
  const imageURL = canvas.toDataURL('image/png');
  return imageURL;
};
export const getBrowserLangusge = () => {
  const lang = navigator.language;
  if (lang === 'zh-CN') {
    return 'zh';
  }
  if (lang === 'ja') {
    return 'ja';
  }
  if (lang === 'ko') {
    return 'ko';
  }
  return 'en';
};

interface IRsaEncryptParams {
  address: string;
  timestamp: number;
  originalUrl: string;
}

/**
 * rsa加密
 * @param data
 * address 钱包地址
 * timestamp 时间戳
 * originalUrl 原始url
 * @returns
 */
export const rsaEncrypt = (data: IRsaEncryptParams) => {
  const PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhs/BbbFpMKfAW65eVrsM
jGTU30DjBk0oI+leSud6w4mne9jt/X+VGucsjGW1gtNdc4plY5iBRbIe+q1krsfo
/pSmCXYLTxBecRyy0Mfbv7dNS1MVIFMO5wzS1T878wnqkMcEfMqyulQkDkPsopwC
Nq9C3Z6pbODAwG37+ui/r5AciQFn7OSFn/1nhHtnjle1yptNsUeDliEh31IVIQFw
2Bs/WtmfrYagFuMAzzgrNWeRspfvP5AN4sxSnXL00t+q/fMB71kGxxBKAOi0abM6
F0cY9kCAEbPhAjYDwgMfVMmKhkmb2SmN1hMo3f99G6iT6kkSCFNaa84fGR5USOWD
3wIDAQAB
-----END PUBLIC KEY-----`;
  try {
    const key = new NodeRSA();
    key.importKey(PUBLIC_KEY, 'pkcs8-public');

    key.setOptions({
      encryptionScheme: 'pkcs1_oaep',
    });
    const encrypted = key.encrypt(data, 'base64');
    return encrypted;
  } catch (error) {
    console.error('rsaEncrypt error', error);
    return '';
  }
};

// 创建请求参数
export const createParams = (address: string, originalUrl: string) => {
  return {
    address,
    timestamp: Date.now(),
    originalUrl,
  };
};

// 创建请求头
export const createHeaders = (address: string, encrypted: string, type: string) => {
  return {
    sw: encrypted,
    address,
    session: getLocalSession(address).sessionId,
    type,
  };
};

// deep seek出品
export const getCirclePoints = (
  x1: number,
  y1: number,
  r1: number,
  x2: number,
  y2: number,
  r2: number
): {
  x: number;
  y: number;
} => {
  interface Point {
    x: number;
    y: number;
  }

  function getCircleIntersections(
    x1: number,
    y1: number,
    r1: number,
    x2: number,
    y2: number,
    r2: number
  ): Point[] | null {
    // 计算两个圆心之间的距离
    const dx = x2 - x1;
    const dy = y2 - y1;
    const d = Math.sqrt(dx * dx + dy * dy);

    // 检查两个圆是否相交
    if (d > r1 + r2 || d < Math.abs(r1 - r2)) {
      return null; // 两个圆不相交
    }

    // 计算交点
    const a = (r1 * r1 - r2 * r2 + d * d) / (2 * d);
    const h = Math.sqrt(r1 * r1 - a * a);

    const xm = x1 + (a * dx) / d;
    const ym = y1 + (a * dy) / d;

    const rx = -dy * (h / d);
    const ry = dx * (h / d);

    const intersection1: Point = { x: xm + rx, y: ym + ry };
    const intersection2: Point = { x: xm - rx, y: ym - ry };

    return [intersection1, intersection2];
  }

  /**
   * 计算点 (x, y) 相对于圆心 (cx, cy) 的角度（弧度）
   */
  function getAngle(cx: number, cy: number, x: number, y: number): number {
    const dx = x - cx;
    const dy = y - cy;
    return Math.atan2(dy, dx); // 返回弧度值
  }

  /**
   * 按顺时针方向对交点排序
   */
  function sortPointsClockwise(center: Point, points: Point[]): Point[] {
    return points.sort((a, b) => {
      let angleA = getAngle(center.x, center.y, a.x, a.y);
      let angleB = getAngle(center.x, center.y, b.x, b.y);
      if (Math.abs(angleA - angleB) > Math.PI) {
        if (angleA < angleB) {
          angleA += 2 * Math.PI;
        } else {
          angleB += 2 * Math.PI;
        }
      }
      return angleB - angleA; // 按角度从小到大排序（顺时针）
    });
  }

  const intersections = getCircleIntersections(x1, y1, r1, x2, y2, r2);

  if (intersections) {
    const center: Point = { x: x2, y: y2 }; // 以第一个圆心为参考点
    const sortedIntersections = sortPointsClockwise(center, intersections);
    return sortedIntersections[0];
  } else {
    return {
      x: x1,
      y: y1,
    };
  }
};
export const getBeijingMidnightTimestamp = (): number => {
  const now = Date.now();
  const beijingOffset = 8 * 60 * 60 * 1000; // 北京时区偏移量（毫秒）
  const beijingTime = new Date(now + beijingOffset); // 调整到北京时间对应的UTC时间
  const year = beijingTime.getUTCFullYear();
  const month = beijingTime.getUTCMonth();
  const day = beijingTime.getUTCDate();
  return Date.UTC(year, month, day) - beijingOffset; // 计算北京时间0点的UTC时间戳
};
