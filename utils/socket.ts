//计算SocketEvent的md5
import { SocketEvents } from '@/game/TS/useNetWork';
import md5 from 'md5';
import { getLocalSessionId } from '@/utils/index';

const keyCache = new Map<string, string>();

function md5WithTime(session: string, str: string, time = 0): string {
  if (time === 0) {
    return md5(str + session);
  }
  return md5WithTime(session, md5(str + session), time - 1);
}

export function preCalculateMD5(btcAddress: string, times: number) {
  const session = getLocalSessionId(btcAddress);
  const list = [
    SocketEvents.PLAYER_LOGIN,
    SocketEvents.JOIN_ROOM,
    SocketEvents.LEAVE_ROOM,
    SocketEvents.GET_ROOM_LIST,
    SocketEvents.PLAYER_READY,
    SocketEvents.PLAYER_ACTION,
    SocketEvents.PLAYER_LOGIC,
    SocketEvents.PLAYER_REQUEST,
    SocketEvents.PLAYER_CHAT,
    SocketEvents.PLAYER_HEARTBEAT,
    SocketEvents.PLAYER_NOTICE,
    SocketEvents.PLAYER_KICK,
    SocketEvents.MATCH_FOUND,
    SocketEvents.WEB_NOTICE,
    SocketEvents.ERROR,
  ];
  list.forEach((item) => {
    keyCache.set(item, md5WithTime(session, item, times));
  });
}

export function getFinallyKey(key: SocketEvents) {
  return keyCache.get(key) || key;
}
