import { motion } from 'motion/react';
import styled, { css } from 'styled-components';
import Image from 'next/image';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import SvgWrapper, { SpriteSvg } from '../SvgWrapper';
import useLatest from '@/hooks/useLatest';

const containerVariants = {
  hidden: {},
  show: {
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  show: { opacity: 1, y: 0, transition: { duration: 0.4, type: 'spring', stiffness: 60 } },
};

const MaterialItemWrapper = styled(motion.div)<{ $isActive: boolean }>`
  border: ${(props) => (props.$isActive ? '0.25rem solid #fc7922' : '0.125rem solid #a58061')};
  border-radius: 1.375rem;
  background: #fff7ec;
  box-sizing: border-box;
  display: block;
  margin-bottom: 0.625rem;
  position: relative;
  box-shadow: 0 -0.25rem 0 0 rgba(0, 0, 0, 0.25) inset;
  // 右下角会有选中的伪类元素
  &::after {
    content: '';
    position: absolute;
    bottom: -0.0625rem;
    right: -0.0625rem;
    width: 2.1875rem;
    height: 2.1875rem;
    background: url('/image/submission/active.png') no-repeat center center;
    background-size: 100% 100%;
    display: ${(props) => (props.$isActive ? 'block' : 'none')};
  }
`;

const MaterialItem = styled.div<{
  $isActive: boolean;
  $minBtnDisabled: boolean;
  $plusBtnDIsabled: boolean;
}>`
  width: 100%;
  height: 7.5rem;
  border-radius: 1.25rem;
  border: none;
  box-shadow: 0 -0.25rem 0 0 rgba(0, 0, 0, 0.25) inset;
  box-sizing: border-box;
  padding: ${(props) =>
    props.$isActive ? '0.625rem 3.125rem 0.625rem 1.25rem' : '0.75rem 3.25rem 0.75rem 1.375rem'};

  display: flex;
  align-items: center;
  gap: 1.25rem;
  position: relative;
  overflow: hidden;
  .item-left {
    width: 5rem;
    height: 5rem;
    border-radius: 0.9375rem;
    /* background: #fff; */
    background: #fbf4e8;

    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border: 0.0625rem solid #cabfab;
    /* box-shadow:
      0 0.1875rem 0.5rem 0 #e5dacb,
      0 0.09375rem 0 0 #f5e7d6 inset; */

    background: #fbf4e8;
    box-shadow: 0.1875rem 0.1875rem 0.1875rem 0 #fff inset;
    position: relative;
    /* overflow: hidden; */
    &::before {
      /* content: '';
      position: absolute;
      left: 0.1875rem;
      top: 0.1875rem;
      right: 0.375rem;
      bottom: 0.375rem;
      background: #fbf4e8;
      border-radius: 1rem;
      z-index: 0;
      width: 100%;
      height: 100%; */
    }
    .item-left-img {
      position: relative;
      z-index: 1;
    }
  }

  .item-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    box-sizing: border-box;
    .counter-box {
      display: flex;
      align-items: center;
      min-width: 15rem;
      box-sizing: border-box;
      gap: 0.375rem;
    }
    .counter-btn {
      width: 3.75rem;
      height: 2.1875rem;
      border-radius: 0.625rem;
      border: none;
      background: #ff8316;
      color: #fff;
      font-size: 1.5rem;
      font-weight: bold;
      cursor: pointer;
      transition: filter 0.1s;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      // 置灰
      &:disabled {
        background: #c2b8a2;
        cursor: not-allowed;
      }
    }
    .minus {
      border-radius: 1.25rem 0.25rem 0.25rem 1.25rem;
      box-shadow: ${(props) =>
        props.$minBtnDisabled ? '0 0.25rem 0 #c2b8a2' : '0 0.25rem 0 #e07c00'};
      position: relative;
      .minus-icon {
        position: relative;
        left: 0.125rem;
      }
    }
    .plus {
      border-radius: 0.25rem 1.25rem 1.25rem 0.25rem;
      box-shadow: ${(props) =>
        props.$plusBtnDIsabled ? '0 0.25rem 0 #c2b8a2' : '0 0.25rem 0 #e07c00'};
      position: relative;
      .plus-icon {
        position: relative;
        left: -0.125rem;
      }
    }
    .counter-btn:not(:disabled):active {
      filter: brightness(0.95);
    }
    .counter-value {
      color: #fff;
      font-size: 1.25rem;
      font-weight: bold;
      border-top: 0.1875rem solid #8c8475;
      box-shadow: 0 0rem 0.25rem rgba(0, 0, 0, 0.1) inset;
      height: 2.375rem;
      width: 100%;
      background: #c2b8a2;
      border-radius: 0.625rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      top: 0.125rem;

      input:focus {
        outline: none;
      }
      &:has(input:focus) {
        border: 0.125rem solid #fc7922;
      }

      input {
        width: 100%;
        height: 100%;
        text-align: center;
        background: transparent;
        border: none;
        color: inherit;
        font-size: inherit;
        font-weight: inherit;
        outline: none;
        text-shadow:
          -0.0625rem 0.0625rem 0 #a58061,
          0.0625rem 0.0625rem 0 #a58061,
          0.0625rem -0.0625rem 0 #a58061,
          -0.0625rem -0.0625rem 0 #a58061;

        &::placeholder {
          color: rgba(255, 255, 255, 0.7);
          font-size: 1rem;
        }

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        &[type='number'] {
          -moz-appearance: textfield;
        }
      }
    }
  }
`;

const StyledMaxBox = styled.div<{ $maxBtnDisabled?: boolean }>`
  background: #fff;
  border-radius: 0.625rem;
  border: 0.0625rem solid #a58061;
  color: #a58061;
  font-size: 1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  cursor: pointer;
  box-shadow: 0 0.25rem 0 #b3afa5;
  transition:
    box-shadow 0.1s,
    transform 0.1s;
  box-sizing: border-box;
  padding: 0.25rem 0;
  ${({ $maxBtnDisabled = false }) =>
    $maxBtnDisabled
      ? css`
          background: #c2b8a2;
          cursor: not-allowed;
          box-shadow: none;
        `
      : css`
          &:hover {
            box-shadow: 0 0.125rem 0 #b3afa5;
            transform: translateY(0.125rem);
          }
        `}

  .max-value {
    font-weight: normal;
  }
`;

const StyledHearIcon = styled(SvgWrapper)`
  width: 2rem;
  height: 1.8125rem;
  flex-shrink: 0;
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(-50%, -50%);
  box-sizing: border-box;
  filter: drop-shadow(0 0.1875rem 0 #140f08);
`;

const MaterialItemComponents = ({
  item,
  inputValue,
  onInputChange,
  handleMaxClick,
  showPreferFoodIcon = false,
  minusBtnDisabled = false,
  plusBtnDisabled = false,
  maxBtnDisabled = false,
}: {
  item: any;
  inputValue: { userItemId: string; value: number };
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleMaxClick?: (...args: any[]) => void;
  showPreferFoodIcon?: boolean;
  minusBtnDisabled?: boolean;
  plusBtnDisabled?: boolean;
  maxBtnDisabled?: boolean;
}) => {
  const isActive = inputValue.value > 0;
  const minBtnDisabled = inputValue.value <= 0 || minusBtnDisabled;
  const plusDisabled = inputValue.value >= item.quantity || plusBtnDisabled;

  const onMaxValue = () => {
    if (maxBtnDisabled) return;
    // 设置最大值
    if (handleMaxClick) {
      handleMaxClick();
    } else {
      onInputChange({ target: { value: String(item.quantity) } } as any);
    }
  };

  return (
    <MaterialItem
      $isActive={isActive}
      $minBtnDisabled={minBtnDisabled}
      $plusBtnDIsabled={plusDisabled}>
      <div className="item-left">
        <Image
          src={item.icon}
          alt={item.name}
          width={70}
          height={70}
          className="item-left-img"
          style={{
            width: '4.375rem',
            height: '4.375rem',
          }}
          draggable={false}
        />
        {showPreferFoodIcon && (
          <StyledHearIcon>
            <SpriteSvg id="heart" />
          </StyledHearIcon>
        )}
      </div>
      <div className="item-right">
        <div className="counter-box">
          <button
            className="counter-btn minus"
            disabled={minBtnDisabled}
            onClick={(e) => {
              e.stopPropagation();
              onInputChange({
                target: { value: String(Math.max(0, inputValue.value - 1)) },
              } as any);
            }}>
            <span className="minus-icon">-</span>
          </button>
          <div className="counter-value">
            <input type="number" value={inputValue.value ?? 0} onChange={onInputChange} />
          </div>
          <button
            className="counter-btn plus"
            disabled={plusDisabled}
            onClick={(e) => {
              e.stopPropagation();
              onInputChange({
                target: { value: String(Math.min(item.quantity, inputValue.value + 1)) },
              } as any);
            }}>
            <span className="plus-icon">+</span>
          </button>
        </div>
        <StyledMaxBox className="max-box" onClick={onMaxValue} $maxBtnDisabled={maxBtnDisabled}>
          Max: <span className="max-value">{item.quantity.toLocaleString()}</span>
        </StyledMaxBox>
      </div>
    </MaterialItem>
  );
};

const PetFeedMaterialItemComponents = ({
  item,
  inputValue,
  onInputChange,
  handleMaxClick,
  showPreferFoodIcon = false,
  minusBtnDisabled = false,
  plusBtnDisabled = false,
  maxBtnDisabled = false,
}: {
  item: any;
  inputValue: { userItemId: string; value: number };
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleMaxClick?: (...args: any[]) => void;
  showPreferFoodIcon?: boolean;
  minusBtnDisabled?: boolean;
  plusBtnDisabled?: boolean;
  maxBtnDisabled?: boolean;
}) => {
  const isActive = inputValue.value > 0;
  const minBtnDisabled = inputValue.value <= 0 || minusBtnDisabled;
  const plusDisabled = inputValue.value >= item.quantity || plusBtnDisabled;

  const onMaxValue = () => {
    if (maxBtnDisabled) return;
    // 设置最大值
    if (handleMaxClick) {
      handleMaxClick(item.userItemId);
    } else {
      onInputChange({ target: { value: String(item.quantity) } } as any);
    }
  };

  return (
    <MaterialItem
      $isActive={isActive}
      $minBtnDisabled={minBtnDisabled}
      $plusBtnDIsabled={plusDisabled}>
      <div className="item-left">
        <Image
          src={item.icon}
          alt={item.name}
          width={70}
          height={70}
          className="item-left-img"
          style={{
            width: '4.375rem',
            height: '4.375rem',
          }}
          draggable={false}
        />
        {showPreferFoodIcon && (
          <StyledHearIcon>
            <SpriteSvg id="heart" />
          </StyledHearIcon>
        )}
        <StyledNum data-text={item.quantity.toLocaleString()}>
          {item.quantity.toLocaleString()}
        </StyledNum>
      </div>
      <div className="item-right">
        <div className="counter-box">
          <button
            className="counter-btn minus"
            disabled={minBtnDisabled}
            onClick={(e) => {
              e.stopPropagation();
              onInputChange({
                target: { value: String(Math.max(0, inputValue.value - 1)) },
              } as any);
            }}>
            <span className="minus-icon">-</span>
          </button>
          <div className="counter-value">
            <input type="number" value={inputValue.value ?? 0} onChange={onInputChange} />
          </div>
          <button
            className="counter-btn plus"
            disabled={plusDisabled}
            onClick={(e) => {
              e.stopPropagation();
              onInputChange({
                target: { value: String(Math.min(item.quantity, inputValue.value + 1)) },
              } as any);
            }}>
            <span className="plus-icon">+</span>
          </button>
        </div>
        <StyledMaxBox className="max-box" onClick={onMaxValue} $maxBtnDisabled={maxBtnDisabled}>
          Max
        </StyledMaxBox>
      </div>
    </MaterialItem>
  );
};

interface MaterialListProps {
  materialList: any[];
  onSelectedItemsChange?: (
    selectedItems: Array<{ item: any; value: number; userItemId: string }>
  ) => void;
  minusBtnDisabled?: boolean;
  maxBtnDisabled?: boolean;
  plusBtnDisabled?: boolean;
  handleMaxClick?: (...args: any[]) => void;
  petFeedConfig?: {
    showPreferFoodIcon?: boolean;
    preferFoodTagList?: string[];
    staminaLimit?: number;
    previewStamina?: number;
    currentStamina?: number;
  };
}

export interface MaterialListRef {
  removeItem: (userItemId: string) => void;
  resetAllInputValue: () => void;
  setAllInputValue: () => void;
  clearInputValue: () => void;
  // petFeedOnly
  autoMaxFeed: () => void;
}

function mergeArrayById(arr: { userItemId: string; value: number }[]) {
  const resultMap: any = {};
  for (const item of arr) {
    if (resultMap[item.userItemId]) {
      resultMap[item.userItemId] += item.value;
    } else {
      resultMap[item.userItemId] = item.value;
    }
  }
  return Object.keys(resultMap).map((userItemId) => ({
    userItemId: userItemId,
    value: resultMap[userItemId],
  }));
}

const MaterialList = forwardRef<MaterialListRef, MaterialListProps>((props, ref) => {
  const {
    materialList,
    onSelectedItemsChange,
    minusBtnDisabled = false,
    maxBtnDisabled = false,
    plusBtnDisabled = false,
    petFeedConfig = {
      preferFoodTagList: [],
      staminaLimit: 0,
      previewStamina: 0,
      showPreferFoodIcon: false,
      currentStamina: 0,
    },
  } = props;

  const {
    preferFoodTagList = [],
    staminaLimit = 0,
    previewStamina = 0,
    currentStamina = 0,
    showPreferFoodIcon = false,
  } = petFeedConfig;

  const [selectedItems, setSelectedItems] = useState<{ userItemId: string; value: number }[]>([]);
  const [inputValues, setInputValues] = useState<{ userItemId: string; value: number }[]>(() =>
    materialList.map((item) => ({ userItemId: item.userItemId, value: 0 }))
  );

  const latestInputValuesRef = useLatest(inputValues);

  const selectedDataRef = useRef<
    {
      item: any;
      userItemId: string;
      value: number;
    }[]
  >([]);

  useImperativeHandle(ref, () => ({
    clearInputValue: () => {
      setInputValues([]);
    },
    removeItem: (userItemId: string) => {
      setInputValues((prev) => {
        const next = [...prev];

        const prevIdx = next.findIndex((item) => item.userItemId === userItemId);
        if (prevIdx === -1) return next;
        next[prevIdx] = { ...next[prevIdx], value: 0 };
        return next;
      });
    },
    resetAllInputValue: () => {
      setInputValues((prev) => {
        const next = [...prev].map((item) => ({ userItemId: item.userItemId, value: 0 }));
        return next;
      });
    },
    // 全部选中，并且input设置最大值
    setAllInputValue: () => {
      setInputValues(() => {
        return materialList.map((item) => ({
          userItemId: item.userItemId,
          value: item.quantity,
        }));
      });
    },
    autoMaxFeed: () => {
      if (!showPreferFoodIcon) return;
      const sortedFoodList = materialList.toSorted((a, b) => {
        return a.stamina - b.stamina;
      });
      const staminaGap = staminaLimit - currentStamina;
      const previewStamina = selectedDataRef.current.reduce((total, cur) => {
        const curStamina = cur.item.stamina;
        const curTotal = cur.value * curStamina;
        return total + curTotal;
      }, 0);

      const latestInputValue = latestInputValuesRef.current;

      const newValueArr: { userItemId: string; value: number }[] = [];

      let newStamina = previewStamina;

      if (latestInputValue.length === 0) {
        sortedFoodList.forEach((item) => {
          if (newStamina >= staminaGap) return;
          newStamina = newStamina + item.stamina * item.quantity;
          newValueArr.push({ userItemId: item.userItemId, value: item.quantity });
        });
        newValueArr.forEach((item) => {
          changeInputNewValue(item.userItemId, item.value);
        });
      } else {
        sortedFoodList.forEach((item) => {
          if (newStamina >= staminaGap) return;
          const curValue = latestInputValue.find((_item) => _item.userItemId === item.userItemId);
          let leftQuantity = 0;
          if (curValue) {
            leftQuantity = item.quantity - curValue.value;
          } else {
            leftQuantity = item.quantity;
          }
          const nextStaminaGap = staminaGap - newStamina;
          const num = Math.min(Math.ceil(nextStaminaGap / item.stamina), leftQuantity);
          newStamina = newStamina + item.stamina * num;
          newValueArr.push({ userItemId: item.userItemId, value: num });
        });
        const combineArr = mergeArrayById([...latestInputValue, ...newValueArr]);
        combineArr.forEach((item) => {
          changeInputNewValue(item.userItemId, item.value);
        });
      }
    },
  }));

  useEffect(() => {
    setInputValues((prev) => {
      const prevInputIds = prev.map((item) => item.userItemId);
      const newInputIds = materialList.map((item) => item.userItemId);
      const isSame = newInputIds.every((item) => prevInputIds.includes(item));
      if (isSame && prevInputIds.length === newInputIds.length) return prev;
      const newArr = materialList.map((item) => {
        const prevItem = prev.find((_prev) => _prev.userItemId === item.userItemId);
        return {
          userItemId: item.userItemId,
          value: prevItem ? prevItem.value : 0,
        };
      });
      return newArr;
    });
  }, [materialList]);

  useEffect(() => {
    setSelectedItems((prev) => {
      const prevArr = prev.length > 0 ? [...prev] : [];
      const nextIds = prev.map((item) => item.userItemId);
      inputValues.forEach((item) => {
        if (item.value > 0 && !nextIds.includes(item.userItemId)) {
          prevArr.push(item);
        }
      });
      const next = prevArr
        .map((item) => {
          const inputVal = inputValues.find((_item) => _item.userItemId === item.userItemId);
          if (inputVal) {
            return {
              userItemId: item.userItemId,
              value: inputVal.value,
            };
          }
          return item;
        })
        .filter((item) => item.value);

      return next;
    });
  }, [inputValues]);

  useEffect(() => {
    if (onSelectedItemsChange) {
      const selectedItemsData = selectedItems
        .map((item) => {
          return {
            ...item,
            item: materialList.find((_item) => _item.userItemId === item.userItemId),
          };
        })
        .filter((item) => item.value && item.item);
      selectedDataRef.current = selectedItemsData;
      onSelectedItemsChange(selectedItemsData);
    }
  }, [selectedItems, materialList, onSelectedItemsChange]);

  const changeInputNewValue = (userItemId: string, num: number) => {
    const item = materialList.find((_item) => _item.userItemId === userItemId);

    setInputValues((prev) => {
      const next = [...prev];
      const prevIdx = next.findIndex((item) => item.userItemId === userItemId);

      const isAdd = prevIdx === -1 ? true : next[prevIdx].value - num < 0 ? true : false;

      const previewStamina = selectedDataRef.current.reduce((total, cur) => {
        const curStamina = cur.item.stamina;
        const curTotal = cur.value * curStamina;
        return total + curTotal;
      }, 0);

      if (showPreferFoodIcon) {
        const staminaGap = staminaLimit - currentStamina;
        const previewStaminaGap = staminaGap - previewStamina;
        const isFull = previewStaminaGap <= 0;
        const limitValue = Math.ceil(previewStaminaGap / item.stamina);
        if (isFull) {
          if (isAdd) {
            num = next[prevIdx].value;
          }
        } else {
          if (isAdd) {
            const lastNum = next[prevIdx]?.value || 0;
            const currentEnterDiffNum = lastNum === 0 ? num : num - lastNum;
            const nextStamina = previewStamina + currentEnterDiffNum * item.stamina;
            if (nextStamina >= staminaLimit) {
              num = Math.min((next[prevIdx]?.value || 0) + limitValue, item.quantity);
            }
          }
        }
      }
      if (prevIdx === -1) {
        next.push({ userItemId: userItemId, value: num });
      } else {
        next[prevIdx] = { userItemId: userItemId, value: num };
      }
      return next;
    });
  };

  const handleInputChange = (userItemId: string, value: string) => {
    let num = parseInt(value.replace(/\D/g, ''), 10) || 0;
    const itemMaxQuantity =
      materialList.find((item) => item.userItemId === userItemId)?.quantity || 0;
    if (num > itemMaxQuantity) num = itemMaxQuantity;

    changeInputNewValue(userItemId, num);
  };

  const handleMaxClick = (userItemId: string) => {
    const item = materialList.find((_item) => _item.userItemId === userItemId);
    if (!item) return;

    const itemMaxQuantity =
      materialList.find((item) => item.userItemId === userItemId)?.quantity || 0;
    changeInputNewValue(userItemId, itemMaxQuantity);
  };

  if (materialList.length === 0) {
    return (
      <motion.div
        className="material-list-box"
        variants={containerVariants}
        initial="hidden"
        animate="show">
        <StyledEmptyList>
          <p>You don't have any food to feed your pet</p>
        </StyledEmptyList>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="material-list-box"
      variants={containerVariants}
      initial="hidden"
      animate="show">
      {materialList.map((item, index) => {
        const userItemId = item.userItemId;
        const itemInputValue = inputValues.find((val) => val.userItemId === userItemId) || {
          userItemId,
          value: 0,
        };
        const isActive = itemInputValue.value > 0;
        const isPreferFood = preferFoodTagList.includes(item.tag);
        return (
          <MaterialItemWrapper
            key={index}
            className={`material-item-border${isActive ? ' active' : ''}`}
            variants={itemVariants}
            $isActive={isActive}>
            {showPreferFoodIcon ? (
              <PetFeedMaterialItemComponents
                item={item}
                inputValue={itemInputValue}
                onInputChange={(e) => handleInputChange(userItemId, e.target.value)}
                showPreferFoodIcon={showPreferFoodIcon && isPreferFood}
                minusBtnDisabled={minusBtnDisabled}
                maxBtnDisabled={maxBtnDisabled}
                plusBtnDisabled={plusBtnDisabled}
                handleMaxClick={handleMaxClick}
              />
            ) : (
              <MaterialItemComponents
                item={item}
                inputValue={itemInputValue}
                onInputChange={(e) => handleInputChange(userItemId, e.target.value)}
                showPreferFoodIcon={showPreferFoodIcon && isPreferFood}
              />
            )}
          </MaterialItemWrapper>
        );
      })}
    </motion.div>
  );
});

MaterialList.displayName = 'MaterialList';

export default MaterialList;

const StyledEmptyList = styled.div`
  color: #542d00;
  text-align: center;
  font-family: 'JetBrains Mono';
  font-size: 1rem;
  font-style: normal;
  font-weight: 700;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  & > p {
    margin: 0;
    width: 15.8125rem;
    white-space: break-spaces;
  }
`;

const StyledNum = styled.span`
  color: #fff;
  text-align: right;
  text-shadow: 0 0 0.0458125rem rgba(0, 0, 0, 0.25);
  font-family: 'JetBrains Mono';
  font-size: 0.6875rem;
  font-style: normal;
  font-weight: 400;
  text-transform: capitalize;
  z-index: 1;
  position: absolute;
  bottom: 0.125rem;
  right: 0.5rem;
  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 0.125rem #a58061;
    z-index: -1;
    left: 0;
  }
`;
