import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import Dialog from '@/commons/Dialog';
import { Button, ModalContent, ModalWrapper } from './SubmissionV2Modal';
import Image from 'next/image';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import MaterialList, { MaterialListRef } from './MaterialList';
import CommunityInfo, { COMMUNITY_RANK_IMAGES } from './CommunityInfo';
import SlotList, { SlotListRef } from './SlotList';
import useBagInventory from '@/hooks/useBagInventory';
import TotalScoreValue, { TotalScoreValueRef } from './TotalScoreValue';
import CommunityText, { CommunityTextRef } from './communityText';
import { getTaskList, submitMaterial } from '@/server';
import { createParams, rsaEncrypt } from '@/utils';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import toast from 'react-hot-toast';
import { setLeaderboard } from '@/store/app';
import confetti from 'canvas-confetti';
import { createHeartEmoji } from '@/utils/creatConfettiText';
import { useTaskContext } from '@/contexts/TaskContext';
import LocalLoading from '@/components/LoadingContent';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';

// 加载dayjs的duration插件
dayjs.extend(duration);

export interface SubmissionV2Ref {
  // Future methods can be added here
  open: (communityType: keyof typeof COMMUNITY_RANK_IMAGES) => void;
  close: () => void;
}

interface SubmissionV2Props {
  // Future props can be added here
  onClose?: () => void;
}

// eslint-disable-next-line react/display-name
const SubmissionV2 = forwardRef<SubmissionV2Ref, SubmissionV2Props>((props, ref) => {
  const { onClose } = props;
  const [isOpen, setIsOpen] = useState(false);
  const materialListRef = useRef<MaterialListRef>(null);
  const slotListRef = useRef<SlotListRef>(null);
  const totalScoreValueRef = useRef<TotalScoreValueRef>(null);
  const communityTextRef = useRef<CommunityTextRef>(null);
  const [communityType, setCommunityType] = useState<
    keyof typeof COMMUNITY_RANK_IMAGES | undefined
  >(undefined);
  const [btnLoading, setBtnLoading] = useState(false);
  const currentSelectedItems = useRef<Array<{ item: any; value: number; userItemId: string }>>([]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const dispatch = useDispatch();
  const { syncTaskProgress, incentivesConfig } = useTaskContext();
  const [isLoading, setIsLoading] = useState(false);

  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  /**
   * materialList： 材料列表数据
   */
  const { materialList, getMaterialListData } = useBagInventory(false, false);

  useImperativeHandle(ref, () => ({
    open: async (communityType: keyof typeof COMMUNITY_RANK_IMAGES) => {
      setIsOpen(true);
      setIsLoading(true);
      try {
        await getMaterialListData();
        setCommunityType(communityType);
      } catch (error) {
        console.log('error=======', error);
      } finally {
        setIsLoading(false);
      }
    },
    close: () => setIsOpen(false),
  }));

  const onSelectedItemsChange = (
    selectedItems: Array<{ item: any; value: number; userItemId: string }>
  ) => {
    slotListRef.current?.setSelectedItems(selectedItems);
    totalScoreValueRef.current?.setSelectedItems(selectedItems);
    currentSelectedItems.current = selectedItems;
  };

  const onRemove = (userItemId: string) => {
    materialListRef.current?.removeItem(userItemId);
  };

  const onSubmit = async (token?: string) => {
    if (currentSelectedItems.current.length === 0) {
      return;
    }
    if (!btcAddress) {
      return;
    }
    try {
      setBtnLoading(true);
      const params = createParams(btcAddress, `/activity-rank/cmi`);
      console.log('current time', params.timestamp);

      const encrypted = rsaEncrypt(params);
      const newParams = {
        tick: communityType,
        topN: 25,
        resourceList: currentSelectedItems.current.map((item) => ({
          tag: item.item.tag,
          quantity: item.value,
        })),
      };
      const headers = {
        sw: encrypted,
        // 'x-captcha-token': token ? token : '',
      };
      const res = await submitMaterial(newParams, headers);
      const { code, msg, data } = res.data;
      if (code === 1) {
        dispatch(setLeaderboard(data));
        confetti({
          particleCount: 200, // 粒子数量
          spread: 200, // 粒子扩散范围
          origin: { y: 0.6 }, // 粒子发射位置
          zIndex: 2000, // 层级
          ticks: 300,
          shapes: [createHeartEmoji()],
          scalar: 1.2,
        });
        toast.success('Submit Success!');
        setIsOpen(false);
        onClose?.();
        const result = await getTaskList();
        if (result.data.code === 1) {
          const list = result.data.data;
          syncTaskProgress(list, incentivesConfig);
        }
      } else if (code === 125) {
        // setIsModalOpen(true);
        // token && onVerify(token);
      } else {
        toast.error(msg);
      }
    } catch (error) {
      console.log('error=======', error);
    } finally {
      setBtnLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      KeyPressUtil.setEnable(false);
    } else {
      KeyPressUtil.setEnable(true);
    }
  }, [isOpen]);

  return (
    <>
      <Dialog isOpen={isOpen} width="59.375rem" height="34.375rem">
        <ModalWrapper style={{ minHeight: '34.375rem' }}>
          {isOpen && (
            <ModalContent
              onClick={() => {
                return false;
              }}>
              <Image
                src="/image/submit.png"
                alt=""
                className="history-title"
                width={290}
                height={60}
                style={{
                  width: '18.125rem',
                  height: '3.75rem',
                }}
              />
              <Image
                src="/image/basic/close.png"
                alt=""
                onClick={() => {
                  setIsOpen(false);
                  onClose?.();
                  materialListRef.current?.resetAllInputValue();
                }}
                className="close-btn"
                width={48}
                height={48}
                style={{
                  width: '3rem',
                  height: '3rem',
                }}
              />
              <div className="content">
                {isLoading ? (
                  <div
                    style={{
                      width: '100%',
                      minHeight: '31.25rem',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <LocalLoading />
                  </div>
                ) : (
                  <>
                    <div className="content-left">
                      <MaterialList
                        ref={materialListRef}
                        materialList={materialList}
                        onSelectedItemsChange={onSelectedItemsChange}
                      />
                    </div>
                    <div className="content-right">
                      <CommunityInfo communityType={communityType} />
                      <CommunityText communityType={communityType} ref={communityTextRef} />
                      <div className="submit-resources">
                        <span className="title">Submit Resources</span>
                        <span className="line" />
                      </div>
                      <div className="submit-resources-buttons">
                        <button
                          className="all-materials"
                          onClick={() => {
                            materialListRef.current?.setAllInputValue();
                          }}>
                          All Materials
                        </button>
                        <button
                          className="clear-all"
                          onClick={() => {
                            materialListRef.current?.resetAllInputValue();
                            totalScoreValueRef.current?.reset();
                          }}>
                          Clear All
                        </button>
                      </div>
                      {/* 选中材料后会将选中的信息填充到这里的槽位列表 */}
                      <div className="slot-list">
                        <SlotList
                          materialList={materialList}
                          onRemove={onRemove}
                          ref={slotListRef}
                        />
                      </div>
                      {/* 计算分数 */}
                      <div className="score-box">
                        <span className="score-title">You will receive </span>
                        <TotalScoreValue
                          ref={totalScoreValueRef}
                          getTotalScore={(val) => {
                            communityTextRef.current?.setTotalScore(val);
                          }}
                        />
                      </div>
                      {/* 取消 - 提交 */}
                      <div className="submit-buttons">
                        <Button
                          className="cancel"
                          onClick={() => {
                            setIsOpen(false);
                            onClose?.();
                          }}>
                          Cancel
                        </Button>
                        <Button
                          className="confirm"
                          onClick={() => {
                            onSubmit();
                          }}
                          disabled={btnLoading}>
                          {btnLoading ? 'Submitting...' : 'Submit'}
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </ModalContent>
          )}
        </ModalWrapper>
      </Dialog>
      {/* <CapWidgetModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onVerify={onVerify}
        isFooter={false}
      /> */}
    </>
  );
});

export default SubmissionV2;
