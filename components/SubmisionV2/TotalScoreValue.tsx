import { ConfigManager } from '@/game/Config/ConfigManager';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

export interface TotalScoreValueRef {
  setSelectedItems: (items: Array<{ item: any; value: number; userItemId: string }>) => void;
  reset: () => void;
}

interface TotalScoreValueProps {
  getTotalScore: (totalScore: number) => void;
}

const TotalScoreValue = forwardRef<TotalScoreValueRef, TotalScoreValueProps>((props, ref) => {
  const { getTotalScore } = props;
  const [totalScore, setTotalScore] = useState('0');
  const scoreConfigRef = useRef<{ [key: string]: number }>({});

  useEffect(() => {
    ConfigManager.getInstance().getData((data) => {
      scoreConfigRef.current = data.material_score_config;
    });
  }, []);

  useImperativeHandle(ref, () => ({
    setSelectedItems: (items: Array<{ item: any; value: number; userItemId: string }>) => {
      if (items.length) {
        const resourcesData = {
          wood: 0,
          stone: 0,
          fish: 0,
        };
        // 用于跟踪不同种类鱼的map
        const fishMap = new Map();

        let totalPoints = 0;

        // 遍历数据查找木头、石头和鱼
        items.forEach((i) => {
          const tagScore =
            scoreConfigRef.current[i.item.tag as keyof typeof scoreConfigRef.current] || 0;
          const quantity = i.value || 0;
          const itemScore = tagScore * quantity;
          totalPoints += itemScore;

          if (i.item.parentType === 'Wood') {
            resourcesData.wood = quantity;
          } else if (i.item.parentType === 'Stone') {
            resourcesData.stone = quantity;
          } else if (i.item.parentType === 'Fish') {
            // 使用鱼的name作为key，累加不同种类鱼的数量
            const fishName = i.item.name;
            // 计算这条鱼的积分
            const fishScore = tagScore * quantity;
            if (fishMap.has(fishName)) {
              fishMap.set(fishName, fishMap.get(fishName) + fishScore);
            } else {
              fishMap.set(fishName, fishScore);
            }
          }
        });
        // 计算所有种类鱼的总数量
        resourcesData.fish = Array.from(fishMap.values()).reduce(
          (total, score) => total + score,
          0
        );

        setTotalScore(totalPoints.toString());
        getTotalScore?.(totalPoints);
      } else {
        setTotalScore('0');
        getTotalScore?.(0);
      }
    },
    reset: () => {
      getTotalScore?.(0);
      setTotalScore('0');
    },
  }));
  return <span className="score-value">{totalScore}</span>;
});

TotalScoreValue.displayName = 'TotalScoreValue';

export default TotalScoreValue;
