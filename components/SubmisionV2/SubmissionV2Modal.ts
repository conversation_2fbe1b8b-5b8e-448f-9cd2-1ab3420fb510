import { motion } from 'motion/react';
import styled from 'styled-components';

export const ModalContent = styled(motion.div)`
  /* overflow-y: auto; */
  transform-origin: center bottom; /* 设置变换原点为底部中心，更符合弹跳效果 */
  border-radius: 2rem;
  border: 0.25rem solid #ff8316;
  background: #fff2e2;
  box-sizing: border-box;
  position: relative;
  width: 59.375rem;
  min-height: 34.375rem;
  padding: 0.625rem;
  z-index: 100;
  .close-btn {
    position: absolute;
    cursor: pointer;
    top: -1.125rem;
    right: 2.5rem;
  }

  .history-title {
    position: absolute;
    left: 18%;
    top: -9%;
    transform: translate(-50%, 10%);
  }
  .content {
    width: 100%;
    display: flex;
    box-sizing: border-box;
    padding-top: 1.875rem;
    gap: 1.25rem;
    .content-left {
      flex: 55%;
      box-sizing: border-box;
      height: 34.375rem;
      background: url('/image/task/bg.png') no-repeat center center;
      background-size: 100% 100%;
      padding: 0.625rem 0rem;
      .material-list-box {
        width: 100%;
        overflow-y: auto;
        height: 100%;
        padding: 0.3125rem 1.25rem;
        box-sizing: border-box;
      }
    }
    .content-right {
      flex: 45%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 0.625rem;
      .submit-resources {
        display: flex;
        align-items: center;
        gap: 0.625rem;
        .title {
        }
        .line {
          flex: 1;
          border-bottom: 0.0625rem solid #542d00;
          height: 0.0625rem;
        }
      }
      .submit-resources-buttons {
        display: flex;
        align-items: center;
        gap: 0.625rem;
        width: 100%;
        & > button {
          line-height: 100%;
          font-size: 0.875rem;
          letter-spacing: -0.035rem;
        }
        .all-materials {
          flex: 50%;
          height: 1.875rem;
          background: #fffcf8;
          border-radius: 1.25rem;
          color: #a58061;
          border: 0.0625rem solid #a58061;
          cursor: pointer;
          &:hover {
            background: #a58061;
            color: #fff;
          }
        }
        .clear-all {
          flex: 50%;
          height: 1.875rem;
          background: #fffcf8;
          border-radius: 1.25rem;
          color: #a58061;
          border: 0.0625rem solid #a58061;
          cursor: pointer;
          &:hover {
            background: #a58061;
            color: #fff;
          }
        }
      }

      .slot-list {
        max-width: 25.375rem;
        /* overflow-x: auto;
        overflow-y: hidden;
        scrollbar-width: none; */
        box-sizing: border-box;
      }

      .score-box {
        display: flex;
        align-items: center;
        gap: 0.625rem;
        justify-content: space-between;
        width: calc(100% - 1.25rem);
        .score-title {
          font-size: 1rem;
          font-weight: bold;
          color: #a58061;
        }
        .score-value {
          font-size: 1rem;
          font-weight: bold;
          color: #542d00;
        }
      }
      .submit-buttons {
        display: flex;
        align-items: center;
        gap: 1rem;
        width: calc(100% - 0.9375rem);
        justify-content: space-between;
        margin-top: 0.625rem;
        box-sizing: border-box;
      }
    }
  }
`;
export const ModalWrapper = styled.div`
  position: relative;
  height: 100%;
`;

export const Button = styled.button<{ disabled?: boolean }>`
  padding: 0 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  font-weight: bold;
  cursor: pointer;
  min-width: 11.25rem;
  border: none;
  color: white;
  height: 3.75rem;
  box-sizing: border-box;
  flex: 1;
  &.confirm {
    background: #fc7922;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
    border-bottom: ${({ disabled }) =>
      disabled ? '0.25rem solid #b8a692' : '0.25rem solid #b5581a'};
    position: relative;
    transition:
      transform 0.1s,
      box-shadow 0.1s,
      border-bottom 0.1s;

    &:hover {
      background: #ff8a3c; /* 稍亮的颜色 */
      cursor: pointer;
    }

    &:not(:disabled):active {
      transform: translateY(0.1875rem);
      box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.2);
      border-bottom: 0.0625rem solid #b5581a;
    }

    &:disabled {
      background: #c1af9c;
      cursor: not-allowed;
    }
  }
  &.cancel {
    background: #c1af9c;
  }
`;
