import { Submit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RestButtonWrapper } from './style';
import ResetSvg from '/public/image/reset.svg';
import ResetHoverSvg from '/public/image/reset-hover.svg';
import SubmitModal from './SubmitModal';
import { useMemo, useState } from 'react';
import { IAppState, ICollection } from '../../../constant/type';
import { useSelector } from 'react-redux';
import SaveCollectionModal from '../SaveCollectionModal';
import { IAvatarMetadata } from '../../../AvatarOrdinalsBrowser/constant/type';
import { saveAvatar } from '../../../server';
import toast from 'react-hot-toast';
import { IS_PRO_TEMPORARY_VERSION } from '../../../constant';

export interface SubmitChainIProps {
  userAvatarMetadata: IAvatarMetadata;
  cacheUserAvatarMetadata: IAvatarMetadata;
  updateAvatarPartData: Function;
  exportPreviewImage: (...args: any) => any;
  collectionData: ICollection | null;
  getTempAvatarData: (...args: any) => any;
}

export default function SubmitChain({
  userAvatarMetadata,
  updateAvatarPartData,
  cacheUserAvatarMetadata,
  exportPreviewImage,
  collectionData,
  getTempAvatarData,
}: SubmitChainIProps) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [showSubmitModal, setShowSubmitModal] = useState<boolean>(false);
  const [showSaveTempModal, setShowSaveTempModal] = useState<boolean>(false);
  const [isHoverReset, setIsHoverReset] = useState<boolean>(false);
  const [isHoverSave, setIsHoverSave] = useState<boolean>(false);
  const resetAvatarData = () => {
    updateAvatarPartData(cacheUserAvatarMetadata);
  };
  const isDiff = useMemo(() => {
    return JSON.stringify(userAvatarMetadata) !== JSON.stringify(cacheUserAvatarMetadata);
  }, [userAvatarMetadata, cacheUserAvatarMetadata]);
  const [saveTempLoading, setSaveTempLoading] = useState<boolean>(false);
  const onSaveTmp = () => {
    setSaveTempLoading(true);
    saveAvatar({ content: userAvatarMetadata })
      .then((res: any) => {
        if (res.data.code === 1) {
          toast.success('Save Success');
          getTempAvatarData();
        } else {
          toast.error(res.data.msg || res.data.message[0], { duration: 6000 });
        }
      })
      .finally(() => {
        setSaveTempLoading(false);
      });
  };
  return (
    <SubmitChainView className="submitChainView">
      {isDiff && (
        <>
          {IS_PRO_TEMPORARY_VERSION ? (
            <ButtonWrapper>
              <button
                className="submit-btn"
                onClick={onSaveTmp}
                disabled={!isDiff || !btcAddress || saveTempLoading}>
                Save your Avatar
              </button>
            </ButtonWrapper>
          ) : (
            <ButtonWrapper>
              <button
                className="submit-btn"
                onClick={() => setShowSubmitModal(true)}
                disabled={!isDiff || !btcAddress}>
                Submit to Blockchain
              </button>
            </ButtonWrapper>
          )}

          <RestButtonWrapper>
            <button
              className="reset-btn"
              onClick={() => resetAvatarData()}
              onMouseLeave={() => setIsHoverReset(false)}
              onMouseEnter={() => setIsHoverReset(true)}>
              <img src={isHoverReset ? ResetHoverSvg.src : ResetSvg.src} alt="" draggable={false} />
            </button>
          </RestButtonWrapper>

          {/*<button className="reset-btn" onClick={() => setShowSaveTempModal(true)} onMouseEnter={() => setIsHoverSave(true)} onMouseLeave={() => setIsHoverSave(false)}>
          <img src={isHoverSave ? SaveHoverSvg.src : SaveSvg.src} alt=""/>
        </button>*/}
        </>
      )}
      <SubmitModal
        visible={showSubmitModal}
        onClose={() => setShowSubmitModal(false)}
        userAvatarMetadata={userAvatarMetadata}
        exportPreviewImage={exportPreviewImage}
      />
      <SaveCollectionModal
        visible={showSaveTempModal}
        onClose={() => setShowSaveTempModal(false)}
        userAvatarMetadata={userAvatarMetadata}
        exportPreviewImage={exportPreviewImage}
        collectionData={collectionData}
      />
    </SubmitChainView>
  );
}
