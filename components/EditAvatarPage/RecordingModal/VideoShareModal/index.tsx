import { VideoShareModalView } from './style';
import Modal from '../../BasicComponents/Modal';
import CloseImg from '/public/image/basic/close.png';
import PlayImage from '/public/image/basic/play.svg';
import PauseImage from '/public/image/basic/pause.svg';
import { useEffect, useMemo, useRef, useState } from 'react';
import DownloadImage from '/public/image/basic/download.svg';
import TwitterImage from '/public/image/basic/twitter.svg';
import toast from 'react-hot-toast';
import { setTtsBindStatus, ttsInfoBind, uploadVideo } from '../../../../server';
import { IVTT } from '../../../../constant/type';
import DeleteIcon from '/public/image/recording/delete.svg';
import ConfirmModal from '../../../Basic/ConfirmModal';
import { ButlerData, ButlerUtil } from '@/game/Global/GlobalButlerUtil';
import { compressImageUtil, exportPreviewImage } from '../../../../utils';
import { SHARE_DOMAIN } from '../../../../constant';
import { videoMimeType } from '../index';

export default function VideoShareModal({
  visible,
  onClose,
  videoSrc,
  audioDuration,
  requestId,
  videoBlob,
  subtitle,
  videoId_,
  getHistoryList,
  edit,
  inputText,
}: {
  visible: boolean;
  onClose: () => void;
  videoSrc: string;
  audioDuration: number;
  requestId: string;
  videoBlob: Blob | null;
  subtitle?: IVTT[];
  videoId_?: string; //有videoId_表示为历史查看
  getHistoryList?: Function;
  edit?: boolean; //是否显示删除按钮
  inputText: string;
}) {
  const [isPlay, setIsPlay] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [videoDuration, setVideoDuration] = useState(0);
  const videoRef: any = useRef<HTMLVideoElement>(null);
  const [saveLoading, setSaveLoading] = useState<boolean>(false);
  const [showVtt, setShowVtt] = useState<string>('');
  const [saveShareLink, setSaveShareLink] = useState<string>(''); //是否保存过分享链接，用于判断是否需要保存

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const onPlayVideo = () => {
    if (videoRef.current) {
      if (isPlay) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlay(!isPlay);
    }
  };

  useEffect(() => {
    if (!visible) {
      setIsPlay(false);
      setCurrentTime(0);
    }
  }, [visible]);
  const vtt = subtitle || [];

  const saveData = async () => {
    if (!videoBlob) {
      return {
        videoId: '',
        domain: '',
        videoLink: '',
      };
    }
    // 上传视频
    const loadingToast = toast.loading('Uploading video...', { duration: 6000000 });
    // 提交数据给接口
    const butlerData: ButlerData | null = ButlerUtil.getButlerData();
    const imgBase64 = await compressImageUtil(exportPreviewImage('render-target-game'));
    // const imgBase64 = exportPreviewImage("render-target-game")
    const isBind = await ttsInfoBind({
      requestId: requestId,
      data: butlerData,
      coverImage: imgBase64,
    })
      .then((res: any) => {
        if (res.data.code === 1) {
          return true;
        } else {
          toast.error(res.data.msg);
          return false;
        }
      })
      .catch(() => {
        toast.error('Network error: Store data failed', { duration: 6000 });
        return false;
      });
    if (!isBind) {
      return {
        domain: '',
        videoId: '',
        videoLink: '',
      };
    }

    // 上传视频
    const { videoId, domain, videoLink } = await uploadVideo({
      file: videoBlob,
      requestId: requestId,
    })
      .then((res: any) => {
        loadingToast && toast.dismiss(loadingToast);
        setSaveLoading(false);
        toast.success('Data has been stored', { duration: 6000 });
        return {
          domain: res.data.data.domain,
          videoId: res.data.data.id,
          videoLink: res.data.data.video,
        };
      })
      .catch(() => {
        toast.error('Network error: Upload video failed', { duration: 6000 });
        return {
          domain: '',
          videoId: '',
          videoLink: '',
        };
      });
    return {
      videoId,
      domain,
      videoLink,
    };
  };

  useMemo(() => {
    // 如果视频还未开始播放（currentTime为0），则隐藏字幕
    if (currentTime === 0) {
      setShowVtt('');
      return;
    }

    const currentSubtitle = vtt.find(
      (item) => currentTime >= item.start_time + 2 && currentTime <= item.end_time + 2
    );

    setShowVtt(currentSubtitle?.text || '');
  }, [currentTime]);

  const onDownload = async () => {
    if (!saveShareLink) {
      await saveData();
    }
    const a = document.createElement('a');
    a.href = videoSrc;
    a.target = '_blank';
    a.download = 'recorded_video.mp4';
    a.click();
  };
  const onShare = async () => {
    if (!videoBlob) {
      return;
    }
    if (saveShareLink) {
      window.open(saveShareLink, '_blank');
      return;
    }
    let videoId = videoId_;

    if (!videoId_) {
      setSaveLoading(true);
      const result = await saveData();
      setSaveLoading(false);
      if (!result.videoId) {
        return;
      }
      videoId = result.videoId;
      setSaveLoading(false);
    }

    const text = inputText;
    const url = SHARE_DOMAIN + `?metaVideoId=${videoId}`;
    const hashtags = 'SatWorld';

    const shareUrl = `https://twitter.com/intent/tweet?text=${text}&url=${url}&hashtags=${hashtags}`;
    setSaveShareLink(shareUrl);
    window.open(shareUrl, '_blank');
  };

  // 添加处理视频暂停和结束的函数
  const handlePauseAndEnd = () => {
    setShowVtt('');
  };

  function timeToSeconds(time: string) {
    const [h, m, s] = time.split(':');
    return parseInt(h) * 3600 + parseInt(m) * 60 + parseFloat(s);
  }

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const onDelete = () => {
    setShowDeleteModal(true);
  };
  const onConfirmDelete = () => {
    setShowDeleteModal(false);
    setTtsBindStatus({
      requestId,
      status: 'delete',
    })
      .then(() => {
        getHistoryList && getHistoryList();
        toast.success('Delete success');
        onClose();
      })
      .catch(() => {
        toast.error('Delete failed');
      });
  };

  if (!visible) {
    return null;
  }
  return (
    <>
      <Modal visible={visible} onClose={onClose} zIndex={10} emptyOnly={true} width="1280px">
        <VideoShareModalView>
          <img src={CloseImg.src} alt="" onClick={onClose} className="close-btn" />
          <div className="modal-content">
            <div className="video-box">
              <video
                ref={videoRef}
                controls={false}
                preload="auto"
                onTimeUpdate={(e) => setCurrentTime(e.currentTarget.currentTime)}
                onCanPlay={(e) => {
                  setVideoDuration(
                    e.currentTarget.duration === Infinity
                      ? audioDuration + 4
                      : e.currentTarget.duration
                  );
                }}
                // onLoadedMetadata={(e) => {
                //   console.log('eee', e)
                // }}
                onEnded={() => {
                  setIsPlay(false);
                  setCurrentTime(0);
                  handlePauseAndEnd();
                }}
              >
                <source src={videoSrc} type={videoMimeType} />
                {/*<source src={videoSrc} type="video/webm"/>*/}
              </video>
              <div className="video-controls">
                <div className="video-play" onClick={onPlayVideo}>
                  <img src={isPlay ? PauseImage.src : PlayImage.src} alt="" />
                </div>
                <div className="video-time">
                  <span>{formatTime(currentTime)}</span>
                  <div>
                    <div
                      style={{
                        width: `${videoDuration ? (currentTime / videoDuration) * 100 : 0}%`,
                      }}
                    />
                  </div>
                  <span>{formatTime(videoDuration)}</span>
                </div>
              </div>
              {showVtt && <h1 className="subtitle">{showVtt}</h1>}
            </div>
            <div className="actions">
              {edit && (
                <button className="delete" onClick={onDelete}>
                  <img src={DeleteIcon.src} alt="" />
                </button>
              )}
              <button className="download" onClick={onDownload}>
                <img src={DownloadImage.src} alt="" />
              </button>
              <button className="share" onClick={onShare} disabled={saveLoading}>
                <span>Share</span>
                <img src={TwitterImage.src} alt="" />
              </button>
            </div>
          </div>
        </VideoShareModalView>
      </Modal>
      <ConfirmModal
        visible={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="delete the video"
        content="Please confirm whether to delete the video?"
        onConfirm={onConfirmDelete}
        confirmText="Confirm"
        cancelText="Cancel"
        btnsStyle={{
          flexDirection: 'row-reverse',
        }}
      />
    </>
  );
}
