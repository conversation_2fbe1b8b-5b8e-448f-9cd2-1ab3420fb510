import { memo } from 'react';

import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import styled, { css } from 'styled-components';
import { TAB_ENUM, tabList, useBagContextDispatch, useBagContextSelector } from '../context';

interface IBagInventoryTabsProps {
  isDragging: boolean;
  onDropToDelete?: (item: any) => void;
  onDragOverToDelete?: () => void;
  onDragLeaveToDelete?: () => void;
  deleteHighlight?: boolean;
}

const StyledDiv = styled.div<{ $isActive?: boolean }>`
  position: relative;
  border-radius: 0 2rem 2rem 0;
  overflow: hidden;
  flex-shrink: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 0.25rem 0 rgba(0, 0, 0, 0.25));

  &:not(:last-of-type) {
    margin-top: -1rem;
  }
  transition:
    width 0.1s ease-in 0.2s,
    height 0.1s ease-in 0.1s;

  ${({ $isActive }) =>
    $isActive
      ? css`
          width: 6.5rem;
          height: 7rem;
          z-index: 3;
          background: #fff2e2;
          border-radius: 0 2rem 2rem 0;
          border-top: 0.25rem solid #ed9800;
          border-right: 0.25rem solid #ed9800;
          border-bottom: 0.25rem solid #ed9800;
        `
      : css`
          width: 5.5rem;
          height: 6rem;
          z-index: 1;
          background: linear-gradient(270deg, #ff8316 49.72%, #9d4a00 312.5%);
          border: 0.25rem solid #945f00;
          border-left: 0;

          ::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/image/bag/tabBg.png') lightgray 50% / cover no-repeat;
            opacity: 0.2;
            z-index: -1;
          }
        `}
`;

const InventoryTabsContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  right: 0;
  top: 0;
  width: 6.5rem;
  height: 34.5rem;
  transform: translateX(100%);
  box-sizing: border-box;
  margin-left: -0.25rem;

  * {
    box-sizing: border-box;
  }
`;

const InventoryTabBox = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column-reverse;
  align-items: flex-start;
  justify-content: flex-end;
  flex: 1;
  position: relative;
`;

const StyledSvgWrapper = styled(SvgWrapper)`
  width: 3.1056875rem;
  height: 3.1056875rem;
  flex-shrink: 0;
`;
const StyledDeleteSvgWrapper = styled(SvgWrapper)`
  width: 2rem;
  height: 2rem;
`;
const DeleteButton = styled.div`
  width: 4rem;
  height: 4rem;
  gap: 0.625rem;
  flex-shrink: 0;
  border-radius: 1rem;
  background: #aa4234;
  box-shadow: 0 -0.25rem 0 0 rgba(0, 0, 0, 0.25) inset;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-left: auto;
`;

function BagInventoryTabs({
  isDragging,
  onDropToDelete,
  onDragOverToDelete,
  onDragLeaveToDelete,
  deleteHighlight,
}: IBagInventoryTabsProps) {
  const contextDispatch = useBagContextDispatch();
  const tab = useBagContextSelector((state) => state.tab);

  const updateState = (newTab: TAB_ENUM) => {
    const activeIndex = tabList.findIndex((item) => item.tab === newTab);
    contextDispatch({
      type: 'UPDATE',
      payload: { tab: newTab, activeIndex: activeIndex },
    });
  };

  return (
    <InventoryTabsContainer>
      <InventoryTabBox>
        {tabList.toReversed().map((item) => {
          return (
            <StyledDiv
              key={item.svgIconName + item.tab}
              $isActive={tab === item.tab}
              onClick={() => {
                updateState(item.tab);
              }}>
              <StyledSvgWrapper>
                <SpriteSvg id={`${item.svgIconName}${tab === item.tab ? 'Active' : ''}`} />
              </StyledSvgWrapper>
            </StyledDiv>
          );
        })}
      </InventoryTabBox>
      {/* 销毁工具图标 */}
      {isDragging && (
        <DeleteButton
          onDragOver={(e) => {
            e.preventDefault();
            onDragOverToDelete?.();
          }}
          style={{
            background: deleteHighlight ? '#E9513C' : '#AA4234',
          }}
          onDrop={(e) => {
            e.preventDefault();
            const data = e.dataTransfer.getData('text/plain');
            try {
              const item = JSON.parse(data);
              onDropToDelete?.(item);
            } catch {}
          }}
          onDragLeave={() => {
            onDragLeaveToDelete?.();
          }}>
          <StyledDeleteSvgWrapper>
            <SpriteSvg id="delete" />
          </StyledDeleteSvgWrapper>
        </DeleteButton>
      )}
    </InventoryTabsContainer>
  );
}

export default memo(BagInventoryTabs);
