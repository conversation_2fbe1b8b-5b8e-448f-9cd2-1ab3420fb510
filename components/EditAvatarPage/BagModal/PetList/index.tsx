import StaggeredAnimation from '@/commons/StaggeredAnimation';
import { IBagPetList } from '@/constant/type';
import { useAppSelector } from '@/hooks/useStore';
import ContentItem, { IDetails } from '../ContentItem';
import PetInfoPanel, { rarityToNumber } from './PetInfoPanel';
import styled from 'styled-components';
import useUpdatePetListItem from '@/hooks/useUpdatePetListItem';
import { useMemo, useState } from 'react';
import { changePetStatus } from '@/server';
import toast from 'react-hot-toast';
import { LargeButton } from '@/components/Basic/Button';
import { INVENTORY_TYPE_ENUM, PetStatus } from '@/constant/enum';
import { StatusTag } from '@/components/GameWindow/components/PetPanel/components/PetDetailPanel/style';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import {
  bagOnlyStatusConfig,
  basicOperationArr,
  featureOperationConfigArr,
} from '@/components/GameWindow/components/PetPanel/constant';

interface PetListProps {
  isVisible: boolean;
}

const MAX_FOLLOW_LIMIT = 3;

const PetList = ({ isVisible }: PetListProps) => {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const TOTAL_SLOTS = 20;
  const slots = [...Array(TOTAL_SLOTS)].map((_, index) => {
    return index < petList.length ? petList[index] : null;
  });

  return (
    <div className="inventory-list">
      {slots.map((item, index) => {
        const showDetail: IDetails = {
          name: item?.bagConfigInfo.name ?? '',
          description: item?.bagConfigInfo.description ?? '',
          type: INVENTORY_TYPE_ENUM.pet,
          quality: item?.quality ? rarityToNumber(item.quality) : 0,
        };
        return (
          <StaggeredAnimation
            key={item ? item._id : `empty-material-${index}`}
            index={index}
            isVisible={isVisible}
            initialScale={0.7}
            duration={0.5}
            bounceEffect={true}>
            {item ? (
              <ContentItem
                src={item?.bagConfigInfo.icon}
                check={false}
                itemId={item._id}
                showDetail={showDetail}
                isNew={item.newFlag}
                tooltipBoxRender={() => (
                  <PetInfoPanel petInfo={item} ButtonRender={<ButtonRender petInfo={item} />} />
                )}
                showBind={true}
                isBind={item.bindingFlag}
                renderWithSvg={true}
                ItemSlots={({ onClick, onDragStart }) => (
                  <ItemRender
                    url={item.bagConfigInfo.icon}
                    color={item.bagConfigInfo.iconColor}
                    onClick={onClick}
                    onDragStart={onDragStart}
                    petStatus={item.petStatus !== PetStatus.REST ? item.petStatus : undefined}
                  />
                )}
              />
            ) : (
              // 渲染空格子
              <ContentItem
                key={`empty-material-slot-${index}`}
                // 不设置src，显示为空格子
                check={false}
                disabled={false} // 设为false以避免灰度滤镜
                style={{
                  cursor: 'default',
                  background: '#FBF4E8', // 保持与有物品格子相同的背景色
                }}
              />
            )}
          </StaggeredAnimation>
        );
      })}
    </div>
  );
};

interface IPetInfoPanelProps {
  petInfo: IBagPetList;
}

const ButtonRender = ({ petInfo }: IPetInfoPanelProps) => {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const isReachFollowLimit = useMemo(() => {
    return petList.filter((item) => item.petStatus !== PetStatus.REST).length >= MAX_FOLLOW_LIMIT;
  }, [petList]);

  const { updateGameStatePetItemData } = useUpdatePetListItem();

  const isSummon = petInfo.petStatus !== PetStatus.REST;
  const [loading, setLoading] = useState(false);

  const handleChangePetStatus = async (petStatus: PetStatus.REST | PetStatus.FOLLOW) => {
    setLoading(true);
    try {
      const res = await changePetStatus({ petId: petInfo._id, petStatus });
      if (res.data.code === 1) {
        const newPetData = res.data.data;
        updateGameStatePetItemData(newPetData);
      } else {
        toast.error(res.data.msg);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const handleClick = () => {
    if (loading) return;
    if (!isSummon && isReachFollowLimit) {
      toast.error('Please recycle one pet first.');
    }
    handleChangePetStatus(isSummon ? PetStatus.REST : PetStatus.FOLLOW);
  };

  const disabled = !isSummon && isReachFollowLimit ? true : false;

  return (
    <LargeButton
      btnType={isSummon ? 'green' : 'primary'}
      onClick={handleClick}
      isLoading={loading}
      disabled={disabled}>
      {isSummon ? 'Recall' : 'Summon'}
    </LargeButton>
  );
};

export const ItemRender = ({
  url,
  color,
  onClick,
  onDragStart,
  petStatus,
}: {
  url: string;
  color: string;
  onClick?: () => void;
  onDragStart?: () => void;
  petStatus?: PetStatus;
}) => {
  const statusIconArr = [
    ...basicOperationArr,
    ...bagOnlyStatusConfig,
    ...featureOperationConfigArr,
  ];
  const currentOperation = statusIconArr.find((it) => it.action === petStatus) || statusIconArr[0];
  // 岩系最低品质特殊处理
  const isRock = url.includes('rock');
  const isCommon = color === '#99A1A4';

  return (
    <ItemRenderContainer
      style={
        isRock && isCommon
          ? ({
              '--filterColor': color,
              '--mixMode': 'color',
            } as any)
          : ({ '--filterColor': color } as any)
      }
      onDragStart={onDragStart}
      onClick={onClick}>
      <svg>
        <use href={`${url}#svgRoot`} />
      </svg>
      {petStatus && (
        <StyledStatusTag
          $borderColor={currentOperation.panelColorConfig.borderColor}
          $color={currentOperation.panelColorConfig.color}>
          <StyledStatusSvg>
            <SpriteSvg id="petStatus" />
          </StyledStatusSvg>
          <span>
            <span data-text={currentOperation.panelDisplay}>{currentOperation.panelDisplay}</span>
          </span>
        </StyledStatusTag>
      )}
    </ItemRenderContainer>
  );
};

const StyledStatusSvg = styled(SvgWrapper)`
  height: 1.25rem;
  flex-shrink: 0;
`;

const StyledStatusTag = styled(StatusTag)`
  min-width: 4.625rem;
  height: 1.25rem;
  bottom: 10%;
  & > span:last-of-type {
    padding: 0;
    & > span {
      font-size: 0.6875rem;
    }
  }
`;

const ItemRenderContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  svg {
    max-width: 100%;
    max-height: 100%;
    width: 4.6875rem;
    height: 5.75rem;
    flex-shrink: 0;
    aspect-ratio: 75/92;
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

export default PetList;
