import { IBagPetList } from '@/constant/type';
import { INVENTORY_TYPE_ENUM, Rarity } from '@/constant/enum';
import { TooltipBoxView } from '../ContentItem/style';
import styled from 'styled-components';
import { useMemo } from 'react';
import dayjs from 'dayjs';
import FeatureTag from '@/components/GameWindow/components/PetPanel/components/PetDetailPanel/FeatureTag';

export function rarityToNumber(rarity: Rarity): number {
  const map = {
    [Rarity.COMMON]: 1,
    [Rarity.UNCOMMON]: 2,
    [Rarity.RARE]: 3,
    [Rarity.EPIC]: 4,
    [Rarity.LEGENDARY]: 5,
    [Rarity.MYTHIC]: 6,
  };
  return map[rarity];
}

interface IPetInfoPanelProps {
  petInfo: IBagPetList;
  ButtonRender?: React.ReactElement;
}

const PetInfoPanel = ({ petInfo, ButtonRender }: IPetInfoPanelProps) => {
  const showDetail = {
    name: petInfo?.bagConfigInfo.name,
    description: petInfo?.bagConfigInfo.description,
    type: INVENTORY_TYPE_ENUM.pet,
    quality: petInfo?.quality ? rarityToNumber(petInfo.quality) : 0,
    deployTime: petInfo?.createdAt ? dayjs(petInfo.createdAt).format('YYYY/MM/DD HH:mm:ss') : '',
    attribute: petInfo.affinity,
    symbol: petInfo.species,
    characteristic: petInfo.featureInfos,
  };

  const tags = useMemo(() => {
    return (petInfo.featureInfos || []).map((item, idx) => {
      return (
        <StyledFeatureTag
          key={item.feature + item.featureLevel + idx}
          feature={item.feature}
          featureLevel={item.featureLevel}
        />
      );
    });
  }, [petInfo.featureInfos]);

  return (
    <StyledTooltipBoxView quality={showDetail.quality} style={{ minHeight: '14.375rem' }}>
      <StyledImg src={petInfo.bagConfigInfo.infoImageUrl} alt="pet" draggable={false} />
      <h3 className="tooltip-box-title">{showDetail.name}</h3>
      <InfoBox>
        {showDetail.symbol && (
          <p>
            <span>Symbol:</span>
            <span>{showDetail.symbol}</span>
          </p>
        )}
        {showDetail.attribute && (
          <p>
            <span>Attribute:</span>
            <span>{showDetail.attribute}</span>
          </p>
        )}
        {petInfo.featureInfos?.length > 0 && (
          <p>
            <span>Characteristic:</span>
            <TagWrapper>{tags}</TagWrapper>
          </p>
        )}
        {showDetail.deployTime && (
          <p>
            <span>Deploy Time:</span>
            <span>{showDetail.deployTime}</span>
          </p>
        )}
      </InfoBox>

      {ButtonRender ? <ButtonWrapper>{ButtonRender}</ButtonWrapper> : <></>}
      {/* <LargeButton
          btnType={isSummon ? 'green' : 'primary'}
          onClick={handleClick}
          isLoading={loading}
          disabled={disabled}>
          {isSummon ? 'Recall' : 'Summon'}
        </LargeButton> */}
    </StyledTooltipBoxView>
  );
};

const StyledFeatureTag = styled(FeatureTag)`
  min-width: 10rem;
  box-sizing: border-box;
`;

const TagWrapper = styled.span`
  display: flex;
  min-width: 11rem;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

const InfoBox = styled.div`
  display: block;
`;

const StyledTooltipBoxView = styled(TooltipBoxView)`
  & > .tooltip-box-title {
    word-break: break-all;
  }
  & > ${InfoBox} {
    display: block;
  }
  & > div > p {
    display: block;
    width: 100%;
    margin-right: auto;
    line-height: 1;
    &:not(:first-of-type) {
      margin-top: 0.5rem;
    }
    & > span {
      font-family: 'JetBrains Mono';
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      &:first-of-type {
        color: #686663;
        margin-right: 0.5rem;
      }
      &:last-of-type {
        color: #3f3b37;
        text-transform: capitalize;
      }
    }
  }
`;

const StyledImg = styled.img`
  width: 7.5rem;
  height: 7.5rem;
  flex-shrink: 0;
  float: right;
`;

const ButtonWrapper = styled.div`
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1rem;
  margin: 0 auto !important;
  transform: translateY(100%);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  & * {
    pointer-events: auto;
  }
`;

export default PetInfoPanel;
