import { Icon<PERSON>ontainer, MenuItemSvgWrapper, StorageMenuItem, StorageMenuView } from './style';
import { useEffect, useState } from 'react';
import {
  IAppState,
  IBasicSummaryData,
  SCENE_TYPE,
  STORAGE_MENU_ENUM,
} from '../../../constant/type';
import PathMenuSvg from '/public/image/storage-menu/path-menu.svg';
import NftMenuSvg from '/public/image/storage-menu/nft-menu.svg';
import PlayMenuSvg from '/public/image/storage-menu/play-menu.svg';
import { useDispatch, useSelector } from 'react-redux';
import { setMenuVisible, setStorageMenu } from '../../../store/app';
import GlobalSpaceEvent, {
  GlobalDataKey,
  SpaceStatus,
} from '@/game/Global/GlobalSpaceEvent';
import RecordingModal from '../RecordingModal';
import CharacterSelectionModal from '../RecordingModal/CharacterSelectionModal';
import { useRouter } from 'next/router';
import BagModal from '../BagModal';
import RecordButton from './components/RecordButton';
import BackpackButton from './components/BackpackButton';
import TaskButton from './components/TaskButton';
import ChangeSceneButton from './components/ChangeSceneButton';

// 预加载所有图片
function preloadImages(images: string[]) {
  images.forEach((src) => {
    const img = new Image();
    img.src = src;
  });
}

export default function StorageMenu({
  basicSummaryData,
}: {
  basicSummaryData: IBasicSummaryData | null;
}) {
  const router = useRouter();
  const dispatch = useDispatch();

  const { sceneType, storageMenu } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  // 预加载所有图片
  useEffect(() => {
    preloadImages([PathMenuSvg.src, NftMenuSvg.src, PlayMenuSvg.src]);
  }, []);

  useEffect(() => {
    switch (storageMenu) {
      case STORAGE_MENU_ENUM.PATH_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Avatar);
        break;
      case STORAGE_MENU_ENUM.NFT_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.NFT);
        dispatch(setMenuVisible(false));
        break;
      case STORAGE_MENU_ENUM.PLAY_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Game);
        dispatch(setMenuVisible(false));
        break;
      default:
        break;
    }
  }, [storageMenu]);

  return (
    <>
      {sceneType === SCENE_TYPE.Room && router.pathname !== '/avatar' && (
        <RoomMenu basicSummaryData={basicSummaryData} />
      )}
      <div
        style={{
          display:
            sceneType === SCENE_TYPE.Island || sceneType === SCENE_TYPE.Community
              ? 'block'
              : 'none',
        }}>
        <LandMenu basicSummaryData={basicSummaryData} />
      </div>
      {/* 背包弹窗 */}
      <BagModal />
    </>
  );
}

const PathMenuSvgIcon = () => {
  return (
    <MenuItemSvgWrapper>
      <svg
        width="56"
        height="56"
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
          d="M21.3384 8.21875H16.2895C15.0315 8.21875 12.3065 11.6258 11.4873 12.4416C10.2877 13.6413 9.18395 14.8821 8.12138 16.0955C7.04853 17.3157 6.90457 19.1632 7.86088 20.476C8.21393 20.9662 8.59782 21.446 9.03656 21.8813C9.47187 22.3166 9.94831 22.704 10.4419 23.057C11.7513 24.0099 13.6022 23.8659 14.8224 22.7965C14.914 22.7177 15.0264 22.6668 15.1461 22.65C15.2658 22.6333 15.3878 22.6512 15.4976 22.7018C15.6073 22.7523 15.7003 22.8334 15.7653 22.9352C15.8304 23.0371 15.8648 23.1555 15.8644 23.2764C15.7822 30.7418 15.7719 36.9638 16.0358 43.2569C16.056 43.8946 16.268 44.5114 16.644 45.0269C17.0201 45.5423 17.5427 45.9324 18.1438 46.1464C24.8277 48.4601 31.1311 48.203 37.7396 46.1121C38.3701 45.9081 38.9224 45.5145 39.3211 44.9852C39.7198 44.4558 39.9455 43.8164 39.9676 43.1541C40.2281 36.8712 40.2212 30.7007 40.139 23.2764C40.1378 23.1555 40.1715 23.0369 40.2361 22.9348C40.3007 22.8326 40.3934 22.7513 40.5031 22.7006C40.6128 22.65 40.7348 22.632 40.8545 22.649C40.9741 22.666 41.0863 22.7173 41.1776 22.7965C42.3978 23.8694 44.2453 24.0133 45.5581 23.0604C46.0568 22.7049 46.5267 22.3107 46.9634 21.8813C47.4022 21.446 47.7861 20.9696 48.1425 20.476C49.0954 19.1632 48.9515 17.3157 47.882 16.0955C46.8195 14.8821 45.7123 13.6413 44.5127 12.4416C43.7037 11.6327 40.9856 8.21875 39.7105 8.21875H33.911C33.6016 9.65623 32.8089 10.9443 31.665 11.8681C30.5211 12.792 29.0951 13.296 27.6247 13.296C26.1543 13.296 24.7283 12.792 23.5844 11.8681C22.4405 10.9443 21.6478 9.65623 21.3384 8.21875Z"
          fill="white"
        />
      </svg>
    </MenuItemSvgWrapper>
  );
};
const NFTSvgIcon = () => {
  return (
    <MenuItemSvgWrapper>
      <svg
        width="56"
        height="56"
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <mask id="path-1-inside-1_9233_23201" fill="white">
          <path d="M43.2764 6.60938C45.9764 6.60938 48.165 8.79799 48.165 11.498V16.3867C48.165 19.0868 45.9764 21.2754 43.2764 21.2754H18.832C16.132 21.2753 13.9434 19.0867 13.9434 16.3867V15.834H11.499C10.8241 15.834 10.2774 16.3807 10.2773 17.0557V24.6113C10.2776 25.2861 10.8242 25.8339 11.499 25.834H25.499C28.8738 25.8342 31.6102 28.5695 31.6104 31.9443V33.498H33.499C34.1739 33.4982 34.7207 34.0458 34.7207 34.7207V48.165C34.7205 48.8398 34.1738 49.3866 33.499 49.3867H27.3877C26.7128 49.3867 26.1652 48.8399 26.165 48.165V34.7207C26.165 34.0457 26.7127 33.498 27.3877 33.498H29.166V31.9443C29.1658 29.9196 27.5238 28.2785 25.499 28.2783H11.499C9.47418 28.2783 7.83227 26.6361 7.83203 24.6113V17.0557C7.83209 15.0307 9.47407 13.3887 11.499 13.3887H13.9434V11.498C13.9434 8.79802 16.132 6.60942 18.832 6.60938H43.2764Z" />
        </mask>
        <path
          d="M43.2764 6.60938C45.9764 6.60938 48.165 8.79799 48.165 11.498V16.3867C48.165 19.0868 45.9764 21.2754 43.2764 21.2754H18.832C16.132 21.2753 13.9434 19.0867 13.9434 16.3867V15.834H11.499C10.8241 15.834 10.2774 16.3807 10.2773 17.0557V24.6113C10.2776 25.2861 10.8242 25.8339 11.499 25.834H25.499C28.8738 25.8342 31.6102 28.5695 31.6104 31.9443V33.498H33.499C34.1739 33.4982 34.7207 34.0458 34.7207 34.7207V48.165C34.7205 48.8398 34.1738 49.3866 33.499 49.3867H27.3877C26.7128 49.3867 26.1652 48.8399 26.165 48.165V34.7207C26.165 34.0457 26.7127 33.498 27.3877 33.498H29.166V31.9443C29.1658 29.9196 27.5238 28.2785 25.499 28.2783H11.499C9.47418 28.2783 7.83227 26.6361 7.83203 24.6113V17.0557C7.83209 15.0307 9.47407 13.3887 11.499 13.3887H13.9434V11.498C13.9434 8.79802 16.132 6.60942 18.832 6.60938H43.2764Z"
          fill="white"
        />
        <path
          d="M18.832 21.2754L18.832 23.7198H18.832V21.2754ZM13.9434 15.834H16.3878V13.3895H13.9434V15.834ZM11.499 15.834V13.3895L11.4988 13.3895L11.499 15.834ZM10.2773 17.0557L7.8329 17.0555V17.0557H10.2773ZM10.2773 24.6113H7.8329L7.8329 24.6122L10.2773 24.6113ZM11.499 25.834L11.4988 28.2784H11.499V25.834ZM25.499 25.834L25.4992 23.3895H25.499V25.834ZM31.6104 31.9443H34.0548V31.9442L31.6104 31.9443ZM31.6104 33.498H29.1659V35.9425H31.6104V33.498ZM33.499 33.498L33.4995 31.0536H33.499V33.498ZM34.7207 48.165L37.1651 48.1657V48.165H34.7207ZM33.499 49.3867V51.8312H33.4995L33.499 49.3867ZM26.165 48.165H23.7206V48.1657L26.165 48.165ZM29.166 33.498V35.9425H31.6105V33.498H29.166ZM29.166 31.9443H31.6105V31.9441L29.166 31.9443ZM25.499 28.2783L25.4993 25.8339H25.499V28.2783ZM11.499 28.2783L11.499 30.7228H11.499V28.2783ZM7.83203 24.6113H5.38759V24.6116L7.83203 24.6113ZM7.83203 17.0557L5.38759 17.0556V17.0557H7.83203ZM11.499 13.3887V10.9442L11.499 10.9442L11.499 13.3887ZM13.9434 13.3887V15.8331H16.3878V13.3887H13.9434ZM13.9434 11.498L11.4989 11.498V11.498H13.9434ZM18.832 6.60938V4.16493H18.832L18.832 6.60938ZM43.2764 6.60938V9.05382C44.6264 9.05382 45.7206 10.148 45.7206 11.498H48.165H50.6095C50.6095 7.44796 47.3265 4.16493 43.2764 4.16493V6.60938ZM48.165 11.498H45.7206V16.3867H48.165H50.6095V11.498H48.165ZM48.165 16.3867H45.7206C45.7206 17.7367 44.6264 18.8309 43.2764 18.8309V21.2754V23.7198C47.3265 23.7198 50.6095 20.4368 50.6095 16.3867H48.165ZM43.2764 21.2754V18.8309H18.832V21.2754V23.7198H43.2764V21.2754ZM18.832 21.2754L18.8321 18.8309C17.482 18.8309 16.3878 17.7367 16.3878 16.3867H13.9434H11.4989C11.4989 20.4368 14.782 23.7198 18.832 23.7198L18.832 21.2754ZM13.9434 16.3867H16.3878V15.834H13.9434H11.4989V16.3867H13.9434ZM13.9434 15.834V13.3895H11.499V15.834V18.2784H13.9434V15.834ZM11.499 15.834L11.4988 13.3895C9.47402 13.3897 7.83308 15.0307 7.8329 17.0555L10.2773 17.0557L12.7218 17.0559C12.7217 17.7308 12.1742 18.2784 11.4992 18.2784L11.499 15.834ZM10.2773 17.0557H7.8329V24.6113H10.2773H12.7218V17.0557H10.2773ZM10.2773 24.6113L7.8329 24.6122C7.8336 26.6331 9.47095 28.2783 11.4988 28.2784L11.499 25.834L11.4992 23.3895C12.1775 23.3896 12.7216 23.9391 12.7218 24.6105L10.2773 24.6113ZM11.499 25.834V28.2784H25.499V25.834V23.3895H11.499V25.834ZM25.499 25.834L25.4989 28.2784C27.5243 28.2785 29.1658 29.9201 29.1659 31.9445L31.6104 31.9443L34.0548 31.9442C34.0546 27.2189 30.2234 23.3898 25.4992 23.3895L25.499 25.834ZM31.6104 31.9443H29.1659V33.498H31.6104H34.0548V31.9443H31.6104ZM31.6104 33.498V35.9425H33.499V33.498V31.0536H31.6104V33.498ZM33.499 33.498L33.4985 35.9425C32.8217 35.9424 32.2763 35.3937 32.2763 34.7207H34.7207H37.1651C37.1651 32.6978 35.5261 31.054 33.4995 31.0536L33.499 33.498ZM34.7207 34.7207H32.2763V48.165H34.7207H37.1651V34.7207H34.7207ZM34.7207 48.165L32.2763 48.1644C32.2764 47.4898 32.8235 46.9424 33.4985 46.9423L33.499 49.3867L33.4995 51.8312C35.5241 51.8308 37.1646 50.1898 37.1651 48.1657L34.7207 48.165ZM33.499 49.3867V46.9423H27.3877V49.3867V51.8312H33.499V49.3867ZM27.3877 49.3867V46.9423C28.0609 46.9423 28.6093 47.4878 28.6095 48.1644L26.165 48.165L23.7206 48.1657C23.7211 50.192 25.3647 51.8312 27.3877 51.8312V49.3867ZM26.165 48.165H28.6095V34.7207H26.165H23.7206V48.165H26.165ZM26.165 34.7207H28.6095C28.6095 35.3957 28.0627 35.9425 27.3877 35.9425V33.498V31.0536C25.3627 31.0536 23.7206 32.6957 23.7206 34.7207H26.165ZM27.3877 33.498V35.9425H29.166V33.498V31.0536H27.3877V33.498ZM29.166 33.498H31.6105V31.9443H29.166H26.7216V33.498H29.166ZM29.166 31.9443L31.6105 31.9441C31.6102 28.5686 28.873 25.8342 25.4993 25.8339L25.499 28.2783L25.4988 30.7228C26.1746 30.7228 26.7215 31.2705 26.7216 31.9445L29.166 31.9443ZM25.499 28.2783V25.8339H11.499V28.2783V30.7228H25.499V28.2783ZM11.499 28.2783L11.4991 25.8339C10.8244 25.8339 10.2766 25.2863 10.2765 24.611L7.83203 24.6113L5.38759 24.6116C5.38798 27.986 8.12392 30.7227 11.499 30.7228L11.499 28.2783ZM7.83203 24.6113H10.2765V17.0557H7.83203H5.38759V24.6113H7.83203ZM7.83203 17.0557L10.2765 17.0557C10.2765 16.3808 10.8241 15.8331 11.4991 15.8331L11.499 13.3887L11.499 10.9442C8.12402 10.9443 5.38768 13.6807 5.38759 17.0556L7.83203 17.0557ZM11.499 13.3887V15.8331H13.9434V13.3887V10.9442H11.499V13.3887ZM13.9434 13.3887H16.3878V11.498H13.9434H11.4989V13.3887H13.9434ZM13.9434 11.498L16.3878 11.498C16.3878 10.1481 17.482 9.05384 18.8321 9.05382L18.832 6.60938L18.832 4.16493C14.782 4.165 11.4989 7.44795 11.4989 11.498L13.9434 11.498ZM18.832 6.60938V9.05382H43.2764V6.60938V4.16493H18.832V6.60938Z"
          fill="white"
          mask="url(#path-1-inside-1_9233_23201)"
        />
      </svg>
    </MenuItemSvgWrapper>
  );
};
const PlayMenuSvgIcon = () => {
  return (
    <MenuItemSvgWrapper>
      <svg
        width="56"
        height="56"
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <circle cx="26.3472" cy="10.3394" r="8.83154" fill="white" />
        <path
          d="M23.9673 20.2734H29.923C31.8566 20.2734 33.6937 21.1184 34.9521 22.5865L39.5931 28.001H42.905C44.1243 28.001 45.1128 28.9895 45.1128 30.2089V33.5207C45.1128 34.7401 44.1243 35.7286 42.905 35.7286H39.5931L34.0734 32.4168V39.0405L37.8425 42.8096C38.2566 43.2236 38.4892 43.7852 38.4892 44.3708V51.1838C38.4892 52.4032 37.5007 53.3917 36.2813 53.3917H31.8655C30.6462 53.3917 29.6576 52.4032 29.6576 51.1838V46.7681L22.0466 42.9625C21.2986 42.5885 20.8261 41.824 20.8261 40.9877V33.5207H19.7222L17.3189 38.3273C16.8053 39.3544 15.5903 39.8164 14.5241 39.3899L12.6082 38.6235C11.6145 38.2261 11.0494 37.1762 11.2924 36.1339C11.8804 33.6107 13.0331 29.0897 14.2025 26.8971C15.2155 24.9976 16.8918 23.3291 18.3045 22.1419C19.8765 20.8208 21.9139 20.2734 23.9673 20.2734Z"
          fill="white"
        />
        <path
          d="M27.4498 47.872L19.7222 43.8978L16.7265 48.0918C15.9665 49.1557 16.2925 50.6434 17.4277 51.2921L21.2447 53.4732C22.2522 54.0489 23.5335 53.7465 24.1772 52.7809L27.4498 47.872Z"
          fill="white"
        />
      </svg>
    </MenuItemSvgWrapper>
  );
};

// 房间菜单
function RoomMenu({ basicSummaryData }: { basicSummaryData: IBasicSummaryData | null }) {
  const dispatch = useDispatch();
  const { storageMenu, isTtsWhiteList, isMobile } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [isShowSelectCharacter, setIsShowSelectCharacter] = useState<boolean>(false);
  const isDisabledPet = basicSummaryData ? !basicSummaryData.petInfo.claim : true;

  return (
    <>
      <StorageMenuView $isMobile={isMobile}>
        {/* 装饰按钮 */}
        <StorageMenuItem
          className={storageMenu === STORAGE_MENU_ENUM.PATH_MENU ? 'active' : ''}
          onClick={() => {
            dispatch(setStorageMenu(STORAGE_MENU_ENUM.PATH_MENU));
            dispatch(setMenuVisible(true));
          }}>
          <IconContainer>
            <PathMenuSvgIcon />
          </IconContainer>
        </StorageMenuItem>
        {/* NFT 按钮 */}
        <StorageMenuItem
          className={storageMenu === STORAGE_MENU_ENUM.NFT_MENU ? 'active' : ''}
          onClick={() => {
            dispatch(setStorageMenu(STORAGE_MENU_ENUM.NFT_MENU));
            dispatch(setMenuVisible(false));
          }}>
          <IconContainer>
            <NFTSvgIcon />
          </IconContainer>
        </StorageMenuItem>
        {/* 游戏按钮 */}
        <StorageMenuItem
          className={storageMenu === STORAGE_MENU_ENUM.PLAY_MENU ? 'active' : ''}
          onClick={() => {
            if (isDisabledPet || !isTtsWhiteList) {
              dispatch(setStorageMenu(STORAGE_MENU_ENUM.PLAY_MENU));
            } else {
              setIsShowSelectCharacter(true);
            }
            dispatch(setMenuVisible(false));
          }}>
          <IconContainer>
            <PlayMenuSvgIcon />
          </IconContainer>
        </StorageMenuItem>
        <ChangeSceneButton />
      </StorageMenuView>
      <CharacterSelectionModal
        visible={isShowSelectCharacter}
        onClose={() => setIsShowSelectCharacter(false)}
        basicSummaryData={basicSummaryData}
      />
    </>
  );
}

// 岛屿菜单
function LandMenu({ basicSummaryData }: { basicSummaryData: IBasicSummaryData | null }) {
  const { isRecording, isMobile } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  return (
    <>
      {!isRecording && (
        <StorageMenuView $isMobile={isMobile}>
          {/* 场景切换按钮 */}
          <RecordButton />
          {/* 任务按钮 */}
          <TaskButton />
          {/* 背包按钮 */}
          <BackpackButton />
          <ChangeSceneButton />
        </StorageMenuView>
      )}

      {/* 场景弹窗 */}
      <RecordingModal basicSummaryData={basicSummaryData} />
    </>
  );
}
