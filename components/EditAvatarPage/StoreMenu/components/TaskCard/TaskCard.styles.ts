import styled, { css } from 'styled-components';

// 卡片状态类型
type CardStatus = 'active' | 'tracking' | 'completed';

// 根据状态返回颜色
const getStatusColor = (status: CardStatus) => {
  switch (status) {
    case 'active':
      return '#FFFFFF';
    case 'tracking':
      return '#E8BD78';
    case 'completed':
      return '#FF8316';
    default:
      return '#FFFFFF';
  }
};

// 根据状态返回标签颜色
const getTagColor = (status: CardStatus) => {
  switch (status) {
    case 'active':
      return '#FF8316';
    case 'tracking':
      return '#E8BD78';
    case 'completed':
      return '#878787';
    default:
      return '#FF8316';
  }
};

// 卡片容器
export const CardContainer = styled.div<{ status: CardStatus }>`
  position: relative;
  background-color: #ffffff;
  border-radius: 1.5rem;
  border: 0.0625rem solid #d5c4af;
  padding: 0.9375rem 1.4375rem;
  width: 100%;
  margin-bottom: 1.5rem;
  margin-top: 0.25rem;
  box-shadow: inset 0rem -0.375rem 0rem 0rem rgba(0, 0, 0, 0.25);
  box-sizing: border-box;

  /* 添加高亮效果，仅当状态为completed时 */
  ${({ status }) =>
    status === 'completed' &&
    css`
      padding: 1rem 1.5rem;
      border: none;
      box-shadow:
        0 0 1rem 0 #ff8316,
        0 -0.375rem 0 0 rgba(0, 0, 0, 0.25) inset,
        0 0 0 0.25rem #ff8316 inset;
    `}
`;

// 卡片内容
export const CardContent = styled.div`
  display: flex;
  flex-direction: column;

  .progress-list {
    display: flex;
    /* gap: 0.25rem; */
    margin-top: 1rem;
  }

  .rewards-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-top: 2rem;

  }

  .reward-icon {
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0rem 0.09375rem 0.36875rem rgba(0, 0, 0, 0.15);
  }
`;

// 任务标题区域
export const TaskTitle = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.25rem;
  flex-direction: column;

  .time-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border: 0.0625rem solid #cabfab;
    border-radius: 0.75rem;
    background-color: #ffffff;
    gap: 0.25rem;
    & > svg {
      width: 1.25rem;
      height: 1.25rem;
    }
  }

  .time {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    color: #140f08;
  }

  .title-text {
    color: #140f08;
    margin: 0;
    /* line-height: 1.24; */
    font-size: 1.25rem;
    text-shadow: 0 0 black;
  }
`;

export const TaskDescription = styled.div`
  color: #a58061;
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.25rem;
`;

// 倒计时组件
export const TimeRemaining = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border: 0.0625rem solid #cabfab;
  border-radius: 0.75rem;
  background-color: #ffffff;

  span {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    color: #140f08;
  }
`;

// NEW标签
export const NewBadge = styled.div`
  position: absolute;
  top: -0.9375rem;
  left: -0.9375rem;
`;

// 进度项容器
export const ProgressItemContainer = styled.div<{ isCompleted: boolean }>`
  display: flex;
  align-items: start;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  background-color: #fbf4e8;
  border-radius: 0.75rem;
  flex-direction: column;

  .progress {
    font-size: 0.875rem;
    font-weight: 500;
    color: #140f08;
    display: flex;
    align-items: flex-start;
    /* gap: 0.5rem; */
    position: relative;
    overflow: hidden;

    &.completed {
      color: #878787;
      text-decoration: line-through;
    }

    .text-wrapper {
      display: inline;
      flex: 1;
      word-break: break-word;
      .check-icon {
        color: #878787;
        font-size: 1rem;
        margin-right: 0.375rem;
        align-self: flex-start;
      }

      .dot {
        width: 0.375rem;
        height: 0.375rem;
        border-radius: 50%;
        background-color: #140f08;
        display: inline-block;
        margin: 0.125rem 0.375rem 0.15625rem 0rem;
      }
    }

    .task-description {
      white-space: pre-line;
      overflow: visible;
      display: inline;
      word-break: break-all;
    }

    .task-progress {
      white-space: nowrap;
      display: inline;
      margin-left: 0.25rem;
    }

    .check-text {
      color: #ff7a27;
      font-size: 0.875rem;
      font-weight: 900;
      text-underline-offset: 0.25rem;
      cursor: pointer;
      text-decoration: underline;
      display: inline;
      margin-left: 0.25rem;
    }
  }
`;
