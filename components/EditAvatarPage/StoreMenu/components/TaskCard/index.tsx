import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import {
  CardContainer,
  CardContent,
  NewBadge as NewBadgeWrapper,
  ProgressItemContainer,
  TaskDescription,
  TaskTitle,
} from './TaskCard.styles';
import newImg from '/public/image/task/new.png';
import Time from './Time';
import styled, { css } from 'styled-components';
import { FormattedTaskReward } from '../TaskModal';
import { useTaskContext } from 'contexts/TaskContext';
import TaskCheckButton from '../TaskCheck';
import NewBadge from '@/components/NewBadge';

// 定义任务子项接口
export interface TaskSubItem {
  description: string;
  currentProgress: number;
  totalProgress: number;
  isCompleted?: boolean;
  check?: boolean;
  id?: string;
  clientRefresh?: string[];
}

// 定义任务卡片属性接口
export interface TaskCardProps {
  id?: string; // 任务ID，如果不提供则使用title作为ID
  title: string; // 任务标题
  timeRemaining?: string; // 倒计时（如 "23D 16H"）
  newTag?: boolean; // 是否为新任务
  taskItems: TaskSubItem[]; // 子任务项
  status?: 'active' | 'tracking' | 'completed'; // 任务状态
  onClaim?: () => void; // 领取奖励回调
  onTrack?: () => void; // 追踪任务回调
  onUntrack?: () => void; // 取消追踪任务回调
  rewards?: FormattedTaskReward[]; // 任务奖励
  taskDesc?: string; // 任务描述
  taskType?: string;
}

// 定义任务项结构
export interface TaskTrackerItem {
  id: string;
  title: string;
  taskItems: TaskSubItem[];
  timestamp: number;
  timeRemaining?: string;
  rewards?: FormattedTaskReward[];
}

// 从localStorage获取任务追踪数据 (保留以便其他组件引用)
export const getTrackedTasksFromStorage = (): TaskTrackerItem[] => {
  if (typeof window === 'undefined') return [];

  try {
    const storedData = localStorage.getItem('tracked_tasks');
    return storedData ? JSON.parse(storedData) : [];
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return [];
  }
};

// 保存任务追踪数据到localStorage (保留以便其他组件引用)
export const saveTrackedTasksToStorage = (tasks: TaskTrackerItem[]) => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem('tracked_tasks', JSON.stringify(tasks));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

// 进度项组件
const ProgressItem: React.FC<{
  taskItems: TaskSubItem[];
  onCheckTask: (taskId: string, clientRefresh?: string[]) => void;
  isLoading: boolean;
}> = ({ taskItems, onCheckTask, isLoading }) => {
  // 检查是否所有任务都已完成
  const allCompleted = taskItems.every(
    (item) => item.isCompleted || item.currentProgress >= item.totalProgress
  );

  return (
    <div className="progress-list">
      <ProgressItemContainer isCompleted={allCompleted}>
        {taskItems.map((item, index) => {
          const isItemCompleted = item.isCompleted || item.currentProgress >= item.totalProgress;
          return (
            <div key={index} className={`progress ${isItemCompleted ? 'completed' : ''}`}>
              <div className="text-wrapper">
                {isItemCompleted ? (
                  <span className="check-icon">✓</span>
                ) : (
                  // 小圆点
                  <span className="dot" />
                )}
                <span className="task-description">{item.description}</span>
                <span className="task-progress">
                  {item.currentProgress}/{item.totalProgress}
                </span>
                {item.check && item.currentProgress !== item.totalProgress && (
                  <TaskCheckButton
                    taskId={item.id ?? ''}
                    clientRefresh={item.clientRefresh}
                    isLoading={isLoading}
                    onCheck={onCheckTask}
                  />
                )}
              </div>
            </div>
          );
        })}
      </ProgressItemContainer>
    </div>
  );
};

const RequirementImage = styled.div`
  width: 5.5rem;
  height: 5.5rem;
  border-radius: 1rem;
  background-color: #faf3e5;
  border: 0.0625rem solid #c2b8a2;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
  box-shadow: 0 0 0.3125rem rgba(0, 0, 0, 0.3);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  .material-icon {
    width: calc(100% - 0.625rem);
    height: calc(100% - 0.625rem);
  }
  // 奖励数量
  .reward-count {
    position: absolute;
    bottom: 0;
    left: 0.4375rem;
    color: #ffffff;
    border-radius: 0.75rem;
    padding: 0 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    text-shadow:
      -0.0625rem 0.0625rem 0 #a58061,
      0.0625rem 0.0625rem 0 #a58061,
      0.0625rem -0.0625rem 0 #a58061,
      -0.0625rem -0.0625rem 0 #a58061;
  }
`;

// 自定义按钮组件，根据状态显示不同颜色
const ActionButton = styled.button<{
  buttonType: 'track' | 'tracking' | 'claim';
}>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  padding: 0 1.5rem;
  height: 3rem;
  border-radius: 0.75rem;
  border: none;
  font-weight: 500;
  font-size: 1.125rem;
  color: white;
  cursor: pointer;
  transition:
    background-color 0.3s,
    transform 0.2s;
  // 增加按钮下边框阴影
  box-shadow: inset 0rem -0.25rem 0rem rgba(0, 0, 0, 0.25);
  font-family: 'JetBrains Mono';
  ${({ buttonType }) => {
    if (buttonType === 'track') {
      return `background-color: #FF8316;`;
    } else if (buttonType === 'tracking') {
      return `background-color: #E8BD78;`;
    } else {
      return css`
        background-color: #fcbc2f;
        border-radius: 1rem;
        border: none;
        background: linear-gradient(180deg, #ffd036 0%, #ffa600 100%);
        box-shadow:
          0 0 1rem 0 #ffd036,
          0 -0.25rem 0 0 rgba(0, 0, 0, 0.25) inset,
          0 0 0 0.0625rem #fffae9 inset;
      `;
    }
  }}

  &:hover {
    transform: translateY(-0.125rem);
  }

  &:active {
    transform: translateY(0.0625rem);
  }
`;

const StyledNewBadge = styled(NewBadge)`
  transform: rotate(-26.313deg);
  & > span {
    font-family: 'JetBrains Mono' !important;
    font-size: 1.25rem !important;
  }
`;

// 添加按钮容器组件，用于定位按钮在右下角
const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
  width: 100%;
`;

const EmptyAvatar = 'https://next-cdn.unisat.io/icon/unknown-brc20.svg';

const TaskCard: React.FC<TaskCardProps> = ({
  id,
  title,
  timeRemaining,
  newTag = false,
  taskItems,
  status,
  onClaim,
  onTrack,
  onUntrack,
  rewards,
  taskDesc,
  taskType,
}) => {
  // 使用TaskContext
  const { addTask, removeTask, isTaskTracked, trackedTasks, onCheckTask, loading, markTaskAsRead } =
    useTaskContext();

  // 使用本地状态来跟踪任务是否被追踪
  const [isBeingTracked, setIsBeingTracked] = useState(false);

  // 使用标题作为任务ID（如果没有提供明确的ID）
  const taskId = id || title;

  // 监听trackedTasks的变化，更新跟踪状态
  useEffect(() => {
    const tracked = isTaskTracked(taskId);
    setIsBeingTracked(tracked);
  }, [taskId, trackedTasks, isTaskTracked]);

  // 更新卡片状态以反映追踪状态
  const cardStatus = isBeingTracked ? 'tracking' : status;
  const isFinish = status;

  // 检查是否所有子任务都已完成
  const allCompleted = status === 'completed';

  // 卡片是否可以领取奖励 - 只要所有任务条件完成就可以领取
  const isClaimable = allCompleted;

  // 处理追踪任务
  const handleTrack = () => {
    // 创建任务对象
    const task = {
      id: taskId,
      title,
      taskItems,
      timestamp: Date.now(),
      timeRemaining,
      rewards,
      taskDesc,
      taskType,
    };

    // 添加到Context中
    addTask(task);

    // 如果是 new，标记为已阅
    if (newTag && taskType && taskId) {
      markTaskAsRead(taskType, taskId);
    }

    // 如果存在外部的追踪回调，也调用它
    if (onTrack) {
      onTrack();
    }
  };

  // 处理取消追踪
  const handleUntrack = () => {
    // 从Context中移除任务
    removeTask(taskId);

    // 如果存在外部的取消追踪回调，也调用它
    if (onUntrack) {
      onUntrack();
    }
  };

  // 处理领取奖励
  const handleClaim = () => {
    // 从Context中移除任务
    // removeTask(taskId);

    // 如果存在外部的领取回调，也调用它
    if (onClaim) {
      onClaim();
    }
  };

  return (
    <CardContainer status={isFinish}>
      {/* 新任务标签 */}
      {newTag && (
        <NewBadgeWrapper>
          <StyledNewBadge />
        </NewBadgeWrapper>
      )}

      <CardContent>
        {/* 任务标题和时间 */}
        <TaskTitle>
          {/* 倒计时 */}
          {timeRemaining && <Time timeRemaining={timeRemaining} />}
          <h3 className="title-text">{title}</h3>
        </TaskTitle>

        {/* 任务描述 */}
        <TaskDescription>{taskDesc}</TaskDescription>
        {/* 任务进度项列表 */}
        <ProgressItem taskItems={taskItems} onCheckTask={onCheckTask} isLoading={loading} />

        {/* 奖励图标 */}
        <div className="rewards-container">
          {rewards?.map((reward, index) => {
            return (
              <RequirementImage key={index}>
                {reward.url ? (
                  <Image
                    src={reward.url}
                    alt=""
                    width={74}
                    height={74}
                    className="material-icon"
                    style={{
                      width: '4.625rem',
                      height: '4.625rem',
                    }}
                    draggable={false}
                  />
                ) : (
                  <Image
                    src={EmptyAvatar}
                    alt=""
                    width={74}
                    height={74}
                    className="material-icon"
                    style={{
                      width: '4.625rem',
                      height: '4.625rem',
                    }}
                    draggable={false}
                  />
                )}
                {/* quantity 大于0展示 */}
                {reward.quantity > 0 && <div className="reward-count">{reward.quantity}</div>}
              </RequirementImage>
            );
          })}
        </div>

        {/* 按钮状态 */}
        <ButtonContainer>
          {isClaimable ? (
            <ActionButton buttonType="claim" onClick={handleClaim}>
              Claim rewards
            </ActionButton>
          ) : isBeingTracked ? (
            <ActionButton buttonType="tracking" onClick={handleUntrack}>
              Tracking
            </ActionButton>
          ) : (
            <ActionButton buttonType="track" onClick={handleTrack}>
              Track
            </ActionButton>
          )}
        </ButtonContainer>
      </CardContent>
    </CardContainer>
  );
};

export default TaskCard;
