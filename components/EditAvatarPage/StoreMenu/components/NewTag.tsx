import { motion } from 'motion/react';
import styled from 'styled-components';

const StyledNewTag = styled(motion.div)`
  position: absolute;
  top: -0.75rem;
  right: -1.125rem;
  background-color: #ffcc00;
  color: white;
  font-size: 1rem;
  font-weight: bold;
  border-radius: 3.125rem;
  z-index: 20;
  border: 0.0625rem solid #000000;
  letter-spacing: 0.03125rem;
  width: 3rem;
  height: 1.875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: center center;
  box-shadow: 0rem 0.25rem 0.25rem 0rem rgba(0, 0, 0, 0.25);
  font-family: 'JetBrains Mono';
  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 0.1875rem #542d00;
    z-index: -1;
  }
`;

const NewTag = () => {
  return (
    <StyledNewTag
      initial={{
        rotate: 30,
        scale: 1,
      }}
      animate={{
        rotate: 30, // 保持30度倾斜
        scale: [1, 1.05, 1], // 只缩放不改变旋转角度
      }}
      transition={{
        duration: 0.8,
        repeat: Infinity,
        repeatType: 'loop',
        ease: 'easeInOut',
      }}
      data-text={'NEW'}>
      NEW
    </StyledNewTag>
  );
};

export default NewTag;
