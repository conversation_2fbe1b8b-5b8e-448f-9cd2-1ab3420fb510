import { IAppState, STORAGE_MENU_ENUM } from '@/constant/type';
import { useDispatch, useSelector } from 'react-redux';
import { setStorageMenu } from '@/store/app';
import ItemDropAnimation from './ItemDropAnimation';
import { useAnimation } from 'motion/react';
import { useEffect, useMemo, useState } from 'react';
import { clearEvent, emitItemCollected } from '@/store/events';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';
import { IconContainer, MenuItemSvgWrapper, StorageMenuItem, StyledKeyTag } from '../style';

// 全局事件系统，用于触发物品掉落动画
export const BackpackEvents = {
  // 重新实现为使用Redux事件系统
  emitItemCollected: (itemImage: string, count: number = 1) => {
    // 确保在客户端执行
    if (typeof window !== 'undefined') {
      const store = require('@/store').default;
      store.dispatch(emitItemCollected({ itemImage, count }));
    }
  },
};

const BackpackSvgIcon = () => {
  return (
    <MenuItemSvgWrapper>
      <svg
        width="56"
        height="56"
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_9044_22631)">
          <path
            d="M28 11.5C37.1127 11.5 44.5 18.8873 44.5 28V43.8887C44.5 47.2637 41.7637 50 38.3887 50H17.6113C14.2363 50 11.5 47.2637 11.5 43.8887V28C11.5 18.8873 18.8873 11.5 28 11.5ZM20.668 31.6719C18.643 31.6719 17.0011 33.3139 17.001 35.3389V41.4492C17.0012 43.4741 18.6431 45.1162 20.668 45.1162H35.334C37.3587 45.116 39.0008 43.4739 39.001 41.4492V35.3389C39.0009 33.314 37.3588 31.6721 35.334 31.6719H20.668ZM30.9023 34.1162V36.2578L30.9092 36.3828C30.9722 36.9986 31.4925 37.4795 32.125 37.4795C32.7575 37.4795 33.2778 36.9986 33.3408 36.3828L33.3477 36.2578V34.1162H35.334C36.0088 34.1164 36.5565 34.6641 36.5566 35.3389V41.4492L36.5498 41.5742C36.4913 42.1493 36.034 42.6065 35.459 42.665L35.334 42.6719H20.668L20.543 42.665C19.9266 42.6026 19.4455 42.082 19.4453 41.4492V35.3389C19.4454 34.706 19.9265 34.1845 20.543 34.1221L20.668 34.1162H30.9023Z"
            fill="white"
          />
          <path
            d="M28 6C30.6997 6.00039 32.8887 8.18983 32.8887 10.8896V12.2383C39.6145 14.3218 44.5 20.5913 44.5 28.002V43.8906C44.5 47.1603 41.9322 49.8306 38.7031 49.9941L38.3887 50.002H17.6113L17.2969 49.9941C14.0678 49.8306 11.5 47.1603 11.5 43.8906V28.002C11.5 20.5916 16.3849 14.3221 23.1104 12.2383V10.8896C23.1104 8.18959 25.2999 6 28 6ZM28 13.9463C20.2373 13.9463 13.9443 20.2393 13.9443 28.002V43.8906C13.9443 45.9157 15.5863 47.5576 17.6113 47.5576H38.3887C40.4137 47.5576 42.0557 45.9157 42.0557 43.8906V28.002C42.0557 20.2393 35.7627 13.9463 28 13.9463ZM28 8.44531C26.65 8.44531 25.5557 9.53962 25.5557 10.8896V11.6826C26.3533 11.5642 27.1694 11.502 28 11.502C28.8306 11.502 29.6467 11.5642 30.4443 11.6826V10.8896C30.4443 9.53986 29.3497 8.4457 28 8.44531Z"
            fill="white"
          />
          <path
            d="M46.332 47.5564V46.3342V31.6675V30.4453C48.3571 30.4453 49.9987 32.0869 49.9987 34.112V43.8898C49.9987 45.9148 48.3571 47.5564 46.332 47.5564Z"
            fill="white"
          />
          <path
            d="M9.66797 47.5564V46.3342V31.6675V30.4453C7.64292 30.4453 6.0013 32.0869 6.0013 34.112V43.8898C6.0013 45.9148 7.64292 47.5564 9.66797 47.5564Z"
            fill="white"
          />
        </g>
        <defs>
          <clipPath id="clip0_9044_22631">
            <rect width="44" height="44" fill="white" transform="translate(6 6)" />
          </clipPath>
        </defs>
      </svg>
    </MenuItemSvgWrapper>
  );
};

const BackpackButton = () => {
  const { storageMenu, btcAddress } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  // 从Redux获取itemCollected事件
  const itemCollectedEvent = useSelector(
    (state: any) => state.EventsReducer?.currentEvents?.itemCollected
  );
  const dispatch = useDispatch();
  const controls = useAnimation();
  const [droppingItems, setDroppingItems] = useState<string>('');

  // 监听物品收集事件
  useEffect(() => {
    if (itemCollectedEvent) {
      const { itemImage } = itemCollectedEvent;
      setDroppingItems(itemImage);

      // 触发背包抖动动画序列
      const shakeSequence = async () => {
        // 动画完成一段时间后，触发背包下沉动画
        setTimeout(async () => {
          // 背包下沉动画
          await controls.start({
            y: ['0rem', '0.9375rem', '0.3125rem', '0rem'], // 先快速下沉，再缓慢下沉
            scale: [1, 0.95, 1.02, 0.98, 1], // 添加缩放效果增强弹性
            transition: {
              duration: 0.6,
              times: [0, 0.3, 0.7, 1], // 控制每个阶段都时间比例
              ease: 'easeInOut',
            },
          });
        }, 800);
      };

      shakeSequence();
      // 处理完事件后清除
      dispatch(clearEvent('itemCollected'));
    }
  }, [controls, dispatch, itemCollectedEvent]);

  // 监听键盘B
  useEffect(() => {
    const cancel = KeyPressUtil.registerKeyPress(['B', 'b'], (event: KeyboardEvent) => {
      dispatch(
        setStorageMenu(
          storageMenu === STORAGE_MENU_ENUM.BAG_MENU ? null : STORAGE_MENU_ENUM.BAG_MENU
        )
      );
    });
    return () => {
      cancel();
    };
  }, [dispatch, storageMenu]);

  const isKeyDown = useMemo(() => {
    return storageMenu === STORAGE_MENU_ENUM.BAG_MENU;
  }, [storageMenu]);

  if (!btcAddress) {
    return null;
  }

  return (
    <StorageMenuItem
      animate={controls}
      style={{ position: 'relative' }}
      className={storageMenu === STORAGE_MENU_ENUM.BAG_MENU ? 'active' : ''}
      onClick={() => {
        dispatch(
          setStorageMenu(
            storageMenu === STORAGE_MENU_ENUM.BAG_MENU ? null : STORAGE_MENU_ENUM.BAG_MENU
          )
        );
      }}>
      {/* 渲染掉落物品动画 */}
      {droppingItems && (
        <ItemDropAnimation
          itemImage={droppingItems}
          onComplete={() => {
            setDroppingItems('');
          }}
        />
      )}
      <IconContainer whileHover="hover" initial="initial">
        <BackpackSvgIcon />
        <StyledKeyTag>B</StyledKeyTag>
      </IconContainer>
    </StorageMenuItem>
  );
};

export default BackpackButton;
