import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import Dialog from '@/commons/Dialog';
import { ModalContent, ModalWrapper } from './TaskModal.styles';
import Image from 'next/image';
import taskImg from '/public/image/task.png';
import CloseSvg from '/public/image/basic/close.png';
import TaskMenu from './TaskMenu';
import TaskList from './TaskList';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState, TaskReward } from '@/constant/type';
import { groupBy } from 'es-toolkit';
import { TaskSubItem } from '../TaskCard';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { getTaskList } from '@/server';
import { setTaskInfo } from '@/store/app';
import { useTaskContext } from 'contexts/TaskContext';
import { IS_BITCOIN_SIGNET } from '@/constant';

// 加载dayjs的duration插件
dayjs.extend(duration);

export interface TaskModalRef {
  // Future methods can be added here
  open: () => void;
  close: () => void;
}

interface TaskModalProps {
  // Future props can be added here
  onClose: () => void;
}

// 定义转换后的任务奖励格式
export interface FormattedTaskReward {
  url: string;
  quantity: number;
  itemTag: string;
}

// 更新FormattedTask接口
export interface FormattedTask {
  id: string;
  title: string;
  timeRemaining: string;
  newTag?: boolean;
  status: 'active' | 'tracking' | 'completed';
  taskItems: TaskSubItem[];
  rewards?: FormattedTaskReward[]; // 添加奖励属性
  taskDesc?: string; // 任务描述
  taskType?: string;
}

// 从数字映射到任务类型
const menuTypeToTaskCategory = (type: number = 1) => {
  switch (type) {
    case 1:
      return 'daily';
    case 2:
      return 'community';
    case 3:
      return 'outside';
    default:
      return 'daily';
  }
};

type IncentivesData = {
  tag: string;
  type: string;
  link: string;
};

// 获取任务奖励的资源URL
const getTaskRewardUrl = (reward: TaskReward, incentivesConfig: IncentivesData[]): string => {
  if (!incentivesConfig || incentivesConfig.length === 0) {
    return '';
  }
  // 根据type和tag匹配获取资源
  const config = incentivesConfig.find(
    (item) => item.type === reward.itemType && item.tag === reward.itemTag
  );
  if (config && config.link) {
    return config.link;
  }

  return '';
};

const menus = [
  {
    name: 'Daily',
    type: 1,
    icon: 'daily',
    taskType: 'daily',
  },
  {
    name: 'Community',
    type: 2,
    icon: 'community',
    taskType: 'community',
  },
  {
    name: 'Festival',
    type: 3,
    icon: 'festival',
    taskType: 'outside',
  },
];

// eslint-disable-next-line react/display-name
const TaskModal = forwardRef<TaskModalRef, TaskModalProps>((props, ref) => {
  const { onClose } = props;
  const [isOpen, setIsOpen] = useState(false);
  // 添加当前选中的菜单类型状态
  const [activeMenuType, setActiveMenuType] = useState(1); // 默认选中Daily
  const dispatch = useDispatch();

  // 从Redux获取任务数据
  const { taskInfo } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  // 使用TaskContext
  const {
    trackedTasks,
    syncTaskProgress,
    incentivesConfig,
    getTaskNewStatus,
    setLastViewedTaskType,
    reloadTaskNewStatus,
    clearNewStatusForTaskType,
  } = useTaskContext();

  // 按taskType对任务进行分组
  const groupedTasks = useMemo(() => {
    if (!taskInfo || !Array.isArray(taskInfo) || taskInfo.length === 0) {
      return {
        daily: [],
        community: [],
        outside: [],
      };
    }

    const grouped = groupBy(taskInfo, (item) => item.taskType);
    if (IS_BITCOIN_SIGNET) {
      // 体验服仅开放
      return {
        daily: grouped.daily || [],
        community: [],
        outside: [],
      };
    }
    // 确保所有分类都存在，即使为空
    return {
      daily: grouped.daily || [],
      community: grouped.community || [],
      outside: grouped.outside || [],
    };
  }, [taskInfo]);

  // 映射任务状态
  const mapTaskStatus = (taskId: string, status: string): 'active' | 'tracking' | 'completed' => {
    if (status === 'completed') {
      return 'completed';
    }
    // 首先检查是否被追踪
    const isTracked = trackedTasks.some((task) => task.id === taskId);

    return isTracked ? 'tracking' : 'active';
  };

  // 获取倒计时显示
  const getTimeRemaining = (expireAt: string): string => {
    // 将剩余时间时间戳(毫秒)转换为天和小时
    const remainTimeMs = parseInt(expireAt);

    if (remainTimeMs <= 0) return 'Expired';

    // 使用dayjs的duration功能进行转换
    const duration = dayjs.duration(remainTimeMs);
    const days = Math.floor(duration.asDays());
    const hours = duration.hours();

    return `${days}D ${hours}H`;
  };

  // 转换奖励数据的函数
  const formatTaskRewards = (
    rewards: TaskReward[],
    incentivesConfig: IncentivesData[]
  ): FormattedTaskReward[] => {
    return rewards.map((reward) => {
      return {
        ...reward,
        url: getTaskRewardUrl(reward, incentivesConfig),
      };
    });
  };

  // 任务奖励
  const getTaskRewards = (
    taskRewards: TaskReward[],
    incentivesConfig: IncentivesData[]
  ): FormattedTaskReward[] => {
    if (!taskRewards || !Array.isArray(taskRewards) || taskRewards.length === 0) {
      return [];
    }

    return formatTaskRewards(taskRewards, incentivesConfig);
  };

  // 获取当前选中分类的任务，并转换为组件所需格式
  const currentTasks = useMemo(() => {
    const category = menuTypeToTaskCategory(activeMenuType);
    const categoryTasks = groupedTasks[category] || [];
    // 转换任务格式
    return categoryTasks.map((task) => ({
      id: task.id,
      title: task?.taskTitle,
      timeRemaining: task.expireAt ? getTimeRemaining(task.expireAt) : '',
      // isNew: isNewTask(task.id),
      status: mapTaskStatus(task.id, task.status),
      taskItems: task.taskProgress.map((progress: any) => {
        return {
          ...progress,
          description: progress.conditionDesc,
          currentProgress: progress.currentCount,
          totalProgress: progress.requiredCount,
          isCompleted: progress.currentCount >= progress.requiredCount,
          conditionType: progress.conditionType,
          // 判断是否在clientRefresh中
          check: task.clientRefresh.includes(progress.conditionType),
          id: task.id,
          clientRefresh: task.clientRefresh,
        };
      }),
      rewards: getTaskRewards(task.taskRewards, incentivesConfig),
      taskDesc: task.taskDesc,
      newTag: isNewStatus(task.taskType, task.id),
      taskType: task.taskType,
    }));
  }, [activeMenuType, groupedTasks, incentivesConfig, trackedTasks]);

  useImperativeHandle(ref, () => ({
    open: async () => {
      reloadTaskNewStatus();
      try {
        // 1. 从API获取最新任务数据
        const res = await getTaskList();
        if (res.data.code === 1) {
          const taskList = res.data.data;
          dispatch(setTaskInfo(taskList));
          // 使用Context中的方法同步任务
          syncTaskProgress(taskList, incentivesConfig);
        }
        setIsOpen(true);
      } catch (error) {
        console.log('error=======', error);
      }
    },
    close: () => setIsOpen(false),
  }));

  // 处理菜单切换
  const handleMenuChange = (type: number) => {
    setActiveMenuType(type);
  };

  // 重置状态函数
  const resetState = () => {
    setActiveMenuType(1);
  };

  // 判断是否是新状态
  function isNewStatus(taskType: string, taskId: string): boolean {
    const taskNewStatus = getTaskNewStatus();
    return taskNewStatus[taskType][taskId];
  }

  return (
    <Dialog isOpen={isOpen}>
      <ModalWrapper style={{ minHeight: '31.25rem' }}>
        {isOpen && (
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <Image src={taskImg.src} alt="" className="history-title" width={250} height={60} />
            <Image
              src={CloseSvg.src}
              alt=""
              onClick={() => {
                setIsOpen(false);
                onClose?.();
                resetState();
                // 关闭弹窗会检查当前彩蛋设置为已阅
                const menu = menus.find((menu) => menu.type === activeMenuType);
                if (menu) {
                  clearNewStatusForTaskType(menu.taskType);
                }
                setLastViewedTaskType('');
              }}
              className="close-btn"
              width={48}
              height={48}
              style={{
                width: '3rem',
                height: '3rem',
              }}
            />
            {/* 任务内容 */}
            <div className="task-content">
              <TaskMenu
                activeMenuType={activeMenuType}
                onMenuChange={handleMenuChange}
                menus={menus}
              />
              <TaskList
                tasks={currentTasks}
                onClaim={() => {
                  setIsOpen(false);
                  onClose?.();
                  resetState();
                  const menu = menus.find((menu) => menu.type === activeMenuType);
                  if (menu) {
                    clearNewStatusForTaskType(menu.taskType);
                  }
                  setLastViewedTaskType('');
                }}
              />
            </div>
          </ModalContent>
        )}
      </ModalWrapper>
    </Dialog>
  );
});

export default TaskModal;
