import styled, { css } from 'styled-components';
import menuImg from '/public/image/task/menu.png';
import Image from 'next/image';
import ActiveMenu from '/public/image/task/active-menu.png';
import { useTaskContext } from 'contexts/TaskContext';
import newImg from '/public/image/task/new.png';
import { useEffect, useMemo } from 'react';
import { px2rem } from '@/utils/px2rem';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import NewBadge from '@/components/NewBadge';

const TaskMenuContainer = styled.div`
  /* flex: 17%; */
  width: 14.25rem;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-height: 28rem;
  .menu-item {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.25rem;
    cursor: pointer;
    z-index: 10;
    position: relative;
    span {
      font-family: 'Baloo 2';
      font-size: 1.125rem;
      font-weight: 800;
      color: #fff;
      line-height: 1;
    }
    .menu-item-icon {
      position: relative;
      .new-badge {
        position: absolute;
        top: -1.25rem;
        left: -1.875rem;
      }
    }
  }
`;

const MenuItem = styled.div`
  position: absolute;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 10rem;
  height: 28rem;
  background-image: url(${menuImg.src});
  z-index: -1;
`;

const ActiveMenuItem = styled.div<{ $activeTop: number }>`
  position: absolute;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 13.625rem;
  height: 7.5rem;
  background-image: url(${ActiveMenu.src});
  z-index: 0;
  transform: translateY(17%);

  ${({ $activeTop = 0 }) => css`
    top: ${$activeTop}%;
  `}
  transition: top 0.3s ease;
`;

interface TaskMenuProps {
  activeMenuType?: number;
  onMenuChange?: (type: number) => void;
  menus: {
    name: string;
    type: number;
    icon: string;
    taskType: string;
  }[];
}

const TaskMenu = ({ activeMenuType = 1, onMenuChange, menus }: TaskMenuProps) => {
  const { hasNewInTaskType, clearNewStatusForTaskType, setLastViewedTaskType } = useTaskContext();

  // 移除本地状态，使用传入的activeMenuType
  const handleMenuClick = (type: number, taskType: string) => {
    clearNewStatusForTaskType(taskType);
    // 调用传入的回调函数
    onMenuChange?.(type);
  };

  const activeTop = useMemo(() => {
    const index = menus.findIndex((menu) => menu.type === activeMenuType);
    return (100 / menus.length) * index;
  }, [activeMenuType, menus]);

  useEffect(() => {
    const menu = menus.find((menu) => menu.type === activeMenuType);
    if (menu) {
      setLastViewedTaskType(menu.taskType);
    }
  }, []);

  return (
    <TaskMenuContainer>
      <MenuItem />
      <ActiveMenuItem $activeTop={activeTop} />
      {menus.map((menu) => (
        <div
          key={menu.type}
          className="menu-item"
          onClick={() => handleMenuClick(menu.type, menu.taskType)}>
          <div className="menu-item-icon">
            <StyledMenuIconWrapper>
              <SpriteSvg id={menu.icon} />
            </StyledMenuIconWrapper>
            {hasNewInTaskType(menu.taskType) && (
              <div className="new-badge">
                <StyledNewBadge />
              </div>
            )}
          </div>
          <span>{menu.name}</span>
        </div>
      ))}
    </TaskMenuContainer>
  );
};

const StyledNewBadge = styled(NewBadge)`
  transform: rotate(-26.313deg);
  & > span {
    font-family: 'JetBrains Mono' !important;
    font-size: 1.25rem !important;
    line-height: 0.75 !important;
  }
`;

const StyledMenuIconWrapper = styled(SvgWrapper)`
  width: 2.5rem;
  height: 2.5rem;
`;

export default TaskMenu;
