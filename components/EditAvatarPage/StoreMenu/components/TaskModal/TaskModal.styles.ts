import { motion } from 'motion/react';
import styled from 'styled-components';

export const ModalContent = styled(motion.div)`
  /* overflow-y: auto; */
  transform-origin: center bottom; /* 设置变换原点为底部中心，更符合弹跳效果 */
  border: 0.25rem solid #ff8316;
  background: #fff2e2;
  border-radius: 2rem;
  box-sizing: border-box;
  position: relative;
  /* max-width: 57.5rem; */
  width: 70.125rem;
  height: 40rem;
  padding: 0.75rem;
  z-index: 100;
  box-sizing: border-box;
  .close-btn {
    position: absolute;
    cursor: pointer;
    top: -1.125rem;
    right: 2.5rem;
  }

  .history-title {
    position: absolute;
    left: 18%;
    top: -9%;
    transform: translate(-50%, 10%);
    width: 22.375rem;
    height: 5.25rem;
  }
  .task-content {
    width: 100%;
    height: 100%;
    display: flex;
    box-sizing: border-box;
    padding-top: 2rem;
  }
`;
export const ModalWrapper = styled.div`
  position: relative;
  height: 100%;
`;
