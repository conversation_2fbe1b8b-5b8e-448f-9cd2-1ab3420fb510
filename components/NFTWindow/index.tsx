import { NFTWindowView } from './style';
import React, { useEffect, useState } from 'react';
import * as THREE from 'three';
import AvatarDataNFT from '@/game/TS/Data/AvatarDataNFT';
import { getOrdInscriptionLink, toFormatStr } from '../../AvatarOrdinalsBrowser/utils';

import { PATH_ID_ENUM } from '../../AvatarOrdinalsBrowser/constant/type';
import GlobalSpaceEvent, { GlobalDataKey, SpaceStatus } from '@/game/Global/GlobalSpaceEvent';

interface IProps {
  updatePath: (key: string, value: string) => void;
  isVisitor: boolean;
}

export default function NFTWindow({ updatePath, isVisitor }: IProps) {
  const [mouseOver, setMouseOver] = useState(false);
  const [haveHoverObj, setHaveHoverObj] = useState(false);
  const [dataRef, setData] = useState<AvatarDataNFT>(new AvatarDataNFT());
  const [mouse, setMouse] = useState<THREE.Vector2>(new THREE.Vector2(0, 0));
  const [spaceStatus, setSpaceStatus] = useState<SpaceStatus>(SpaceStatus.Avatar);

  let timer: any = null;
  const handleMouseOver = () => {
    clearTimeout(timer);
    setMouseOver(true);
  };
  const handleMouseLeave = () => {
    clearTimeout(timer);
    //在不延迟的时候, 由于先 setMouseOver(false) 再 setHaveHoverObj(true) 导致的窗口闪烁
    //场景 : 在鼠标悬停 NFTWindow 返回指向场景nft的瞬间, 出现的window闪烁问题
    timer = setTimeout(() => {
      setMouseOver(false);
    }, 100);
  };

  useEffect(() => {
    //监听 spaceStatus改变
    const spaceStatusKey = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (status) => {
        setSpaceStatus(status);
      }
    );

    //监听悬停NFT
    const nftKey = GlobalSpaceEvent.ListenKeyDataChange<{
      nft: AvatarDataNFT;
      mouse: THREE.Vector2;
    }>(GlobalDataKey.NftHoverObj, (obj: { nft: AvatarDataNFT; mouse: THREE.Vector2 }) => {
      if (obj.nft) {
        setMouse(obj.mouse);
        setData(obj.nft);
        setHaveHoverObj(true);
      } else {
        setHaveHoverObj(false);
      }
    });

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, spaceStatusKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.NftHoverObj, nftKey);
    };
  }, []);

  const onReplace = () => {
    GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.NFT);
    GlobalSpaceEvent.SetDataValue<number>(GlobalDataKey.LookingNftIndex, dataRef?.position || 0);
  };

  const inscriptionId = dataRef?.content.split('/').pop() || '';
  const onApply = () => {
    //设置衣服当前贴图
    // Global.changeTextureId(inscriptionId)
    const content = (dataRef?.content || '').split('/').pop() || '';
    updatePath(PATH_ID_ENUM.shirtTextureId, content);
  };

  const contentText = dataRef ? toFormatStr(inscriptionId, 4, 4) : '';
  return (
    <NFTWindowView style={{ top: mouse.y + 35, left: mouse.x + 20 }}>
      {spaceStatus !== SpaceStatus.NFT && (mouseOver || haveHoverObj) && (
        <div
          className={'currency-box'}
          onMouseOver={handleMouseOver}
          onMouseLeave={handleMouseLeave}
        >
          <svg
            width="122"
            height="54"
            viewBox="0 0 122 54"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="fixed-nail"
          >
            <circle cx="16" cy="16" r="15" stroke="#FAFAFA" strokeWidth="2" />
            <circle cx="16" cy="16" r="12" fill="#FAFAFA" />
            <path d="M30 21L121.5 53" stroke="#FAFAFA" strokeWidth="2" />
          </svg>
          <div
            className="fixed-nail-more"
            onMouseOver={handleMouseOver}
            onMouseLeave={handleMouseLeave}
          />

          <div
            className="currency-box-bg"
            onMouseOver={handleMouseOver}
            onMouseLeave={handleMouseLeave}
          />
          {
            <div className="currency-box-content" onMouseOver={handleMouseOver}>
              <p onMouseOver={handleMouseOver} onMouseLeave={handleMouseLeave}>
                Inscription ID:{' '}
                <a href={getOrdInscriptionLink(inscriptionId)} target="_blank">
                  {contentText}
                </a>
              </p>
              {!isVisitor && spaceStatus === SpaceStatus.Avatar && (
                <div
                  className="currency-box-btns"
                  onMouseOver={handleMouseOver}
                  onMouseLeave={handleMouseLeave}
                >
                  <button
                    onMouseOver={handleMouseOver}
                    onMouseLeave={handleMouseLeave}
                    onClick={() => onApply()}
                  >
                    Apply as Texture
                  </button>
                  <button
                    onMouseOver={handleMouseOver}
                    onMouseLeave={handleMouseLeave}
                    onClick={() => onReplace()}
                  >
                    Replace
                  </button>
                </div>
              )}
            </div>
          }
        </div>
      )}
    </NFTWindowView>
  );
}
