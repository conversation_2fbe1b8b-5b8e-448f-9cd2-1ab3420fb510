'use client';
import { forwardRef, Ref, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import Dialog from '@/commons/Dialog';
import ModalContent from '@/commons/ModalContent';
import Image from 'next/image';
import Rank2 from '/public/image/rank2.png';
import Rank1 from '/public/image/rank1.png';
import Rank3 from '/public/image/rank3.png';
import Rank4 from '/public/image/rank4.png';
import Rank5 from '/public/image/rank5.png';
import styled from 'styled-components';
import woodIcon from '/public/image/t2-1.png';
import stoneIcon from '/public/image/t2-2.png';
import fishIcon from '/public/image/yu.png';
import { motion } from 'motion/react';
import { createParams, rsaEncrypt } from '@/utils';
import { getTaskList, submitMaterial } from '@/server';
import { setLeaderboard } from '@/store/app';
import confetti from 'canvas-confetti';
import toast from 'react-hot-toast';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { createHeartEmoji } from '@/utils/creatConfettiText';
import { ConfigManager } from '@/game/Config/ConfigManager';
import { useTaskContext } from '@/contexts/TaskContext';
import CapWidgetModal from '../CapWidgetModal';

// Community rank image mapping
const COMMUNITY_RANK_IMAGES = {
  potato: Rank2.src,
  wangcai: Rank1.src,
  TheLonelyBit: Rank3.src,
  Pizza: Rank4.src,
  DomoDucks: Rank5.src,
};

// Community types type definition
type CommunityType = keyof typeof COMMUNITY_RANK_IMAGES;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 1.25rem;
`;

const CommunityContainer = styled.div`
  display: flex;
  width: 100%;
  height: 100%;
  gap: 1.25rem;
  box-sizing: border-box;
  padding: 0.625rem 0;
`;

const MenuContent = styled(motion.div)`
  display: flex;
  align-items: center;
  width: calc(100% - 3.75rem);
  justify-content: space-around;
  padding: 2.375rem 1.5rem;
  background-color: #f5e5c7;
  border-radius: 1.75rem;
  box-shadow: inset 0 0rem 1.25rem rgba(0, 0, 0, 0.15);
`;

const Items = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 0.625rem;
  border-top: 0.1875rem solid #8c8475;
  box-shadow: 0 0rem 0.25rem rgba(0, 0, 0, 0.1) inset;
  background: #c2b8a2;
  border-radius: 1rem;
  width: 6.25rem;
  height: 2.1875rem;
  position: relative;

  .icon {
    position: absolute;
    left: -1.125rem;
    top: 50%;
    transform: translate(0%, -50%);
  }

  .value {
    color: #fff;
    font-size: 1.25rem;
    font-weight: bold;
    padding-right: 0.625rem;
  }
`;

interface ModalProps {
  onClose: () => void;
}

export interface SubmissionRef {
  open: (data: any, communityType: CommunityType) => void;
}

/**
 * @deprecated 该组件已经弃用
 */
const Submission = forwardRef<SubmissionRef, ModalProps>(
  ({ onClose }: ModalProps, ref: Ref<SubmissionRef>) => {
    // 提交成功后重置积分和资源
    const { btcAddress, userBasicInfo } = useSelector(
      (state: { AppReducer: IAppState }) => state.AppReducer
    );
    const dispatch = useDispatch();
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [isMounted, setIsMounted] = useState(false);
    const [communityType, setCommunityType] = useState<CommunityType>('potato');
    const scoreConfigRef = useRef<{ [key: string]: number }>({});
    const [isOpen, setIsOpen] = useState(false);
    const [resources, setResources] = useState({
      wood: 0,
      stone: 0,
      fish: 0,
    });

    const { syncTaskProgress, incentivesConfig } = useTaskContext();
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

    const [totalScore, setTotalScore] = useState('');

    useEffect(() => {
      ConfigManager.getInstance().getData((data) => {
        scoreConfigRef.current = data.material_score_config;
      });
    }, []);

    useEffect(() => {
      setIsMounted(true);
      return () => setIsMounted(false);
    }, []);

    const communityText = useMemo(() => {
      return `Confirm submitting all backpack resources to ${communityType} Community for development?
      ${communityType} Community will receive [${totalScore}] points.`;
    }, [communityType, totalScore]);

    const reset = () => {
      setResources({
        wood: 0,
        stone: 0,
        fish: 0,
      });
      setTotalScore('');
    };
    const close = () => {
      setIsOpen(false);
    };

    const handleSubmit = async (token?: string) => {
      const params = createParams(btcAddress, `/activity-rank/cmi`);
      const encrypted = rsaEncrypt(params);
      const headers = {
        sw: encrypted,
        'x-captcha-token': token,
      };
      const res = await submitMaterial({ tick: communityType }, headers);
      const { code, msg, data } = res.data;
      if (code === 1) {
        dispatch(setLeaderboard(data));
        confetti({
          particleCount: 200, // 粒子数量
          spread: 200, // 粒子扩散范围
          origin: { y: 0.6 }, // 粒子发射位置
          zIndex: 2000, // 层级
          ticks: 300,
          shapes: [createHeartEmoji()],
          scalar: 1.2,
        });
        toast.success('Submit Success!');
        reset();
        close();
        const result = await getTaskList();
        if (result.data.code === 1) {
          const list = result.data.data;
          syncTaskProgress(list, incentivesConfig);
        }
      } else if (code === 125) {
        setIsModalOpen(true);
      } else {
        console.error(msg);
      }
    };

    const onConfirm = async () => {
      if (userBasicInfo?.refreshFlag) {
        setIsModalOpen(true);
        return;
      }
      setConfirmLoading(true);
      await handleSubmit();
      setConfirmLoading(false);
    };

    useImperativeHandle(ref, () => ({
      open: (data: any, communityType) => {
        setCommunityType(communityType);
        // 从data中获取木头，矿石，鱼的数量
        if (data && Array.isArray(data)) {
          const resourcesData = {
            wood: 0,
            stone: 0,
            fish: 0,
          };

          // 用于跟踪不同种类鱼的map
          const fishMap = new Map();

          let totalPoints = 0;

          // 遍历数据查找木头、石头和鱼
          data.forEach((item) => {
            // 计算积分：根据tag和数量
            const tagScore =
              scoreConfigRef.current[item.tag as keyof typeof scoreConfigRef.current] || 0;
            const quantity = item.quantity || 0;
            const itemScore = tagScore * quantity;
            totalPoints += itemScore;

            if (item.parentType === 'Wood') {
              resourcesData.wood = quantity;
            } else if (item.parentType === 'Stone') {
              resourcesData.stone = quantity;
            } else if (item.parentType === 'Fish') {
              // 使用鱼的name作为key，累加不同种类鱼的数量
              const fishName = item.name;
              // 计算这条鱼的积分
              const fishScore = tagScore * quantity;
              if (fishMap.has(fishName)) {
                fishMap.set(fishName, fishMap.get(fishName) + fishScore);
              } else {
                fishMap.set(fishName, fishScore);
              }
            }
          });
          // 计算所有种类鱼的总数量
          resourcesData.fish = Array.from(fishMap.values()).reduce(
            (total, score) => total + score,
            0
          );

          setResources(resourcesData);
          setTotalScore(totalPoints.toString());
        }
        setIsOpen(true);
      },
    }));

    // 背包资源：木头，矿石，鱼
    const backpackResources = useMemo(() => {
      return [
        {
          icon: woodIcon.src,
          name: 'Wood',
          value: resources.wood,
        },
        {
          icon: stoneIcon.src,
          name: 'Stone',
          value: resources.stone,
        },
        {
          icon: fishIcon.src,
          name: 'Fish',
          value: resources.fish,
        },
      ];
    }, [resources]);

    const onVerify = async (token: string) => {
      try {
        if (!token) return;
        // console.log("token=====", token);
        setConfirmLoading(true);
        await handleSubmit(token);
        setConfirmLoading(false);
      } catch (error) {
        console.error(error);
      }
    };

    const rankImg = useMemo(() => {
      return COMMUNITY_RANK_IMAGES[communityType] || '';
    }, [communityType]);

    if (!isMounted) {
      return null;
    }

    return (
      //@ts-ignore
      <>
        <Dialog isOpen={isOpen} onClose={onClose}>
          <ModalContent
            confirmText="Submit"
            onConfirm={onConfirm}
            confirmLoading={confirmLoading}
            confirmDisabled={confirmLoading}
            modalBodyPadding="3.125rem 2.5rem 1.875rem 2.5rem"
            onClose={() => {
              setIsOpen(false);
              setConfirmLoading(false);
            }}
            onCancel={() => {
              setIsOpen(false);
              setConfirmLoading(false);
            }}
            title={
              <Image
                src="/image/submit.png"
                alt="submit"
                width={360}
                height={62}
                style={{ width: '22.5rem', height: '3.875rem' }}
              />
            }
            footerStyle={{
              padding: '0.375rem 2.625rem 2.125rem',
            }}>
            <ContentContainer>
              <CommunityContainer>
                <motion.div
                  className="avatar"
                  initial={{ x: -100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{
                    duration: 0.6,
                    type: 'spring',
                    stiffness: 100,
                    damping: 10,
                  }}
                  style={{
                    width: '4.375rem',
                    height: '4.375rem',
                    borderRadius: '50%',
                    border: '0.125rem solid #fc7922',
                    backgroundImage: `url(${rankImg})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                  }}
                />
                <motion.div
                  className="text"
                  initial={{ x: 100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{
                    duration: 0.6,
                    type: 'spring',
                    stiffness: 100,
                    damping: 10,
                    delay: 0.2,
                  }}
                  style={{
                    color: '#000',
                    fontSize: '1.125rem',
                    flex: '70%',
                    display: 'flex',
                    flexDirection: 'column',
                  }}>
                  {communityText}
                  <span>You will submit:</span>
                </motion.div>
              </CommunityContainer>
              <MenuContent
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}>
                {backpackResources.map((item, index) => (
                  <Items
                    key={item.name}
                    initial={{ x: -50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{
                      duration: 0.4,
                      delay: 0.3 + index * 0.1,
                      type: 'spring',
                      stiffness: 260,
                      damping: 20,
                    }}>
                    <div className="icon">
                      <Image
                        src={item.icon}
                        alt={item.name}
                        width={50}
                        height={50}
                        style={{ width: '3.125rem', height: '3.125rem' }}
                      />
                    </div>
                    <div className="value">{item.value}</div>
                  </Items>
                ))}
              </MenuContent>
            </ContentContainer>
          </ModalContent>
        </Dialog>
        <CapWidgetModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onVerify={onVerify}
          isFooter={false}
        />
      </>
    );
  }
);

Submission.displayName = 'Submission';

export default Submission;
