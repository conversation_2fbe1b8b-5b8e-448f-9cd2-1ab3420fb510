import styled, { css } from 'styled-components';

const StyledNewBadgeWrapper = styled.div`
  display: flex;
  width: 3.125rem;
  height: 1.875rem;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 3.125rem;
  border: 0.0625rem solid #000;
  background: #fc0;
  box-shadow:
    0 0.25rem 0.25rem 0 rgba(0, 0, 0, 0.25),
    -0.0625rem -0.0625rem 0 0 rgba(0, 0, 0, 0.25) inset,
    0.125rem 0.125rem 0.125rem 0 rgba(255, 255, 255, 0.25) inset;
  box-sizing: border-box;
  span {
    color: #fff;
    font-family: 'JetBrains Mono';
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 800;
    line-height: 1;
    position: relative;
    z-index: 1;
    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.1875rem #542d00;
      z-index: -1;
      left: 0;
    }
  }
`;

const NewBadge = ({ text = 'NEW', className = '' }: { text?: string; className?: string }) => {
  return (
    <StyledNewBadgeWrapper className={className}>
      <span data-text={text}>{text}</span>
    </StyledNewBadgeWrapper>
  );
};

export default NewBadge;
