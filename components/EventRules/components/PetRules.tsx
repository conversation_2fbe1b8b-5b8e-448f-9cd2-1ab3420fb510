import BasicRulesContent from './BasicRulesContent';
import styled from 'styled-components';
import { StyledEventTitle } from './styles';
import { StyledMainTitle } from '@/components/EasterEggReward/styles';
import { ModalCloseBtnWrapper } from '@/components/BasicCommunityModalContent';
import { ruleImageMap } from '../constant';

const StyledBasicRulesContent = styled(BasicRulesContent)`
  ${StyledEventTitle} {
    justify-content: flex-end;
    top: -3.5%;
    width: 30.375rem;
    height: 8.8125rem;
    ${StyledMainTitle} {
      color: #fff;
      text-align: center;
      font-family: 'Baloo 2';
      font-size: 3.5rem;
      font-style: normal;
      font-weight: 800;
      line-height: 100%;
      filter: none;
      position: relative;
      margin-bottom: 1rem;
      &::before {
        content: attr(data-text);
        position: absolute;
        -webkit-text-stroke: 0.5rem #664830;
        z-index: -1;
        left: 0;
      }
    }
  }
  ${ModalCloseBtnWrapper} {
  }
`;

const PetRules = ({ onClose }: { onClose: () => void }) => {
  return (
    <>
      <StyledBasicRulesContent
        onClose={onClose}
        titleBgSrc={ruleImageMap.petRule.titleBgSrc}
        popupImages={ruleImageMap.petRule.popupImages}
        bgImgSrc={ruleImageMap.petRule.bgImgSrc}
        mainTitle="SatWorld Pets"
        withCloseBtn={true}
      />
    </>
  );
};

export default PetRules;
