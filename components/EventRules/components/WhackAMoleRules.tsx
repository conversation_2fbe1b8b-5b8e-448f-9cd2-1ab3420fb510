import BasicRulesContent from './BasicRulesContent';
import { useAppSelector } from '@/hooks/useStore';
import { useMemo } from 'react';
import { AnimationSequenceConfig } from '@/components/AnimationSequence/config';
import styled from 'styled-components';
import { ruleImageMap } from '../constant';

const StyledPreloadImage = styled.div<{
  $img1?: string;
  $img2?: string;
  $img3?: string;
  $img4?: string;
}>`
  /* display: none; */
  visibility: hidden;
  pointer-events: none;
  &::before {
    content: '';
    display: block;
    position: absolute;
    background:
      url(${(props) => props.$img1}) no-repeat -9999px -9999px,
      url(${(props) => props.$img2}) no-repeat -9999px -9999px,
      url(${(props) => props.$img3}) no-repeat -9999px -9999px,
      url(${(props) => props.$img4}) no-repeat -9999px -9999px;
    width: 0;
    height: 0;
  }
`;

function usePreloadWhackAMoleImage() {
  const { whackAMoleEasterEgg } = useAppSelector((state) => state.AppReducer);
  const rewardType = whackAMoleEasterEgg?.rewardType;

  const imageConfig = useMemo(() => {
    return rewardType ? AnimationSequenceConfig[rewardType] : null;
  }, [rewardType]);

  const preloadImageEL = useMemo(() => {
    return (
      <StyledPreloadImage
        $img1={imageConfig?.eventImageActiveSrc}
        $img2={imageConfig?.eventImageSrc}
        $img3={imageConfig?.failureImageSrc}
        $img4={imageConfig?.victoryImageSrc}
      />
    );
  }, [imageConfig]);

  return preloadImageEL;
}

const WhackAMoleRules = ({ onClose }: { onClose: () => void }) => {
  const preloadImageEL = usePreloadWhackAMoleImage();

  return (
    <>
      <BasicRulesContent
        onClose={onClose}
        titleBgSrc={ruleImageMap.whackAMole.titleBgSrc}
        popupImages={ruleImageMap.whackAMole.popupImages}
        bgImgSrc={ruleImageMap.whackAMole.bgImgSrc}
        mainTitle="Hidden Challenge"
        subTitle="Tap & Stick Fun"
      />
      {preloadImageEL}
    </>
  );
};

export default WhackAMoleRules;
