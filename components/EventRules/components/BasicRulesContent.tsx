import Image from 'next/image';

import { motion, Variants } from 'framer-motion';
import { useEffect, useState } from 'react';
import { StyledRulesContentContainer, EventImageContainer, StyledEventTitle } from './styles';
import { StyledMainTitle, StyledSubTitle } from '@/components/EasterEggReward/styles';
import { ShakeButton } from '@/components/EasterEggReward/styles';
import { px2rem } from '@/utils/px2rem';
import styled from 'styled-components';
import { Close } from '@/components/BasicCommunityModalContent';
// 定义容器的变体
const defaultContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.5, // 子元素之间的延迟时间
      delayChildren: 0.3, // 第一个子元素开始前的延迟
    },
  },
};

// 定义子元素的变体
const defaultItemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: 'easeOut',
    },
  },
};

interface IRulesContentProps {
  onClose: () => void;
  titleBgSrc: string;
  popupImages: {
    src: string;
    imgObj?: string;
    alt: string;
    width: number;
    height: number;
    className?: string;
    description: string;
  }[];
  bgImgSrc: string;
  itemVariants?: Variants;
  containerVariants?: Variants;
  mainTitle?: React.ReactNode;
  subTitle?: React.ReactNode;
  titleStyle?: React.CSSProperties;
  className?: string;
  withCloseBtn?: boolean;
}

const ruleDescriptionImg = [
  {
    bg: {
      src: '/image/ruleDesc_bg1.png',
      width: px2rem(358),
      height: px2rem(465),
    },
    step: {
      src: '/image/ruleDesc_step1.svg',
      width: px2rem(54),
      height: px2rem(47),
    },
  },
  {
    bg: {
      src: '/image/ruleDesc_bg2.png',
      width: px2rem(359),
      height: px2rem(455),
    },
    step: {
      src: '/image/ruleDesc_step2.svg',
      width: px2rem(54),
      height: px2rem(47),
    },
  },
  {
    bg: {
      src: '/image/ruleDesc_bg3.png',
      width: px2rem(334),
      height: px2rem(451),
    },
    step: {
      src: '/image/ruleDesc_step3.svg',
      width: px2rem(54),
      height: px2rem(47),
    },
  },
];

interface IStyledRuleItem {
  $bgImage: string;
  $width: string;
  $height: string;
}

const EventImageWrapper = styled(EventImageContainer)`
  pointer-events: none;
  & > div {
    &:nth-of-type(1) {
      & > div:first-of-type {
        transform: rotate(-8.81deg) translate(1rem, 6.25rem);
        transform-origin: top left;
        & > img:last-of-type {
          transform: translate(0.75rem, -7.25rem);
        }
      }
    }
    &:nth-of-type(2) {
      & > div:first-of-type {
        transform: rotate(6.22deg) translate(1.375rem, 3.65rem);
        transform-origin: bottom left;
        & > img:last-of-type {
          transform: translate(0.75rem, -6.25rem);
        }
      }
    }
    &:nth-of-type(3) {
      & > div:first-of-type {
        transform: rotate(-4.3deg) translate(1.375rem, 5.5rem);
        transform-origin: top left;
        & > img:last-of-type {
          transform: translate(0.75rem, -6.25rem);
        }
      }
    }
  }
`;

const StyledRuleItem = styled(motion.div)<IStyledRuleItem>`
  position: relative;
  background-image: url(${(props) => props.$bgImage});
  background-size: cover;
  background-repeat: no-repeat;
  height: ${(props) => props.$height};
  width: ${(props) => props.$width};

  display: flex;
  justify-content: flex-start;
  /* & > img:nth-of-type(2) {
    top: 3.6rem;
    left: 2.125rem;
    transform: rotate(-9deg);
  } */
`;

const StyledRuleDescBox = styled.div`
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  /* margin-top: 4.5rem;
  margin-left: 1.9375rem; */
  user-select: none;

  p {
    margin-top: 0.75rem;
    font-family: 'JetBrains Mono';
    font-weight: 400;
    font-size: 1.125rem;
    line-height: 120%;
    letter-spacing: -0.04em;
    padding: 0 0.25rem;
    vertical-align: middle;
    pointer-events: auto;
    user-select: auto;
    color: #140f08;
  }
  .stepIcon {
    position: absolute;
    bottom: 0;
    right: 0;
  }
`;

const StyleClose = styled(Close)`
  top: 0;
  width: 3.5rem;
  height: 3.5rem;
  transform: translateY(-30%);
  & > img {
    width: 3.5rem;
    height: 3.5rem;
  }
`;

function BasicRulesContent({
  onClose,
  titleBgSrc,
  popupImages,
  bgImgSrc,
  itemVariants = defaultItemVariants,
  containerVariants = defaultContainerVariants,
  mainTitle,
  subTitle,
  titleStyle = {},
  className = '',
  withCloseBtn = false,
}: IRulesContentProps) {
  return (
    <StyledRulesContentContainer bgImgSrc={bgImgSrc} className={className}>
      <StyledEventTitle
        $bgSrc={titleBgSrc}
        style={{
          textIndent: '.75rem',
          ...titleStyle,
        }}>
        {mainTitle && (
          <StyledMainTitle style={{ marginTop: '.75rem' }} data-text={mainTitle}>
            {mainTitle}
          </StyledMainTitle>
        )}
        {subTitle && <StyledSubTitle data-text={subTitle}>{subTitle}</StyledSubTitle>}
      </StyledEventTitle>
      {withCloseBtn && <StyleClose onClick={onClose} />}
      <EventImageRender
        popupImages={popupImages}
        itemVariants={itemVariants}
        containerVariants={containerVariants}
      />
      <ShakeButton text="Start!" onClick={onClose} style={{ marginTop: '-2rem' }} />
    </StyledRulesContentContainer>
  );
}

export interface IEventImageRenderProps {
  popupImages: {
    src: string;
    imgObj?: string;
    alt: string;
    width: number;
    height: number;
    className?: string;
    description: string;
  }[];
  itemVariants?: Variants;
  containerVariants?: Variants;
  className?: string;
}

export const EventImageRender: React.FC<IEventImageRenderProps> = ({
  containerVariants = defaultContainerVariants,
  popupImages,
  itemVariants = defaultItemVariants,
  className,
}) => {
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  useEffect(() => {
    if (!imagesPreloaded && typeof window !== 'undefined') {
      let loadedCount = 0;
      const totalImages = popupImages.length;

      popupImages.forEach((img) => {
        const imgElement = document.createElement('img');
        imgElement.src = img.src;

        // 监听加载完成事件
        imgElement.onload = () => {
          loadedCount++;
          if (loadedCount === totalImages) {
            setImagesPreloaded(true);
          }
        };
      });
    }
  }, [imagesPreloaded]);

  return (
    <EventImageWrapper
      variants={containerVariants}
      initial="hidden"
      animate={imagesPreloaded ? 'visible' : 'hidden'}
      className={className ?? ''}>
      {popupImages.map((img, index) => {
        const bg = ruleDescriptionImg[index].bg;
        const step = ruleDescriptionImg[index].step;
        return (
          <StyledRuleItem
            key={img.alt}
            variants={itemVariants}
            className={img.className}
            $bgImage={bg.src}
            $width={`${bg.width}rem`}
            $height={`${bg.height}rem`}>
            <StyledRuleDescBox
              style={{
                width: `${img.width}rem`,
                height: `${bg.height}rem`,
              }}>
              <Image
                src={img.src}
                alt={img.alt}
                width={img.width}
                height={img.height}
                style={{
                  width: `${img.width}rem`,
                  height: `${img.height}rem`,
                }}
              />
              <p>{img.description}</p>
              <Image
                src={step.src}
                alt={img.alt}
                width={step.width}
                height={step.height}
                className="stepIcon"
                style={{
                  width: `${step.width}rem`,
                  height: `${step.height}rem`,
                }}
              />
            </StyledRuleDescBox>
          </StyledRuleItem>
        );
      })}
    </EventImageWrapper>
  );
};

export default BasicRulesContent;
