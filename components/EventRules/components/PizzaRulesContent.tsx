import BasicRulesContent from './BasicRulesContent';
import { ruleImageMap } from '../constant';

const PizzaRulesContent = ({ onClose }: { onClose: () => void }) => {
  return (
    <BasicRulesContent
      onClose={onClose}
      titleBgSrc={ruleImageMap.pizzaRule.titleBgSrc}
      popupImages={ruleImageMap.pizzaRule.popupImages}
      bgImgSrc={ruleImageMap.pizzaRule.bgImgSrc}
      titleStyle={{
        backgroundSize: 'contain',
      }}
    />
  );
};

export default PizzaRulesContent;
