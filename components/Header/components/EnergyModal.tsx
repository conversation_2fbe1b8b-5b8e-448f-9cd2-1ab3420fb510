import Dialog from '@/commons/Dialog';
import BasicButton from '@/components/Basic/Button';
import SvgWrapper from '@/components/SvgWrapper';
import playerTpUtil from '@/utils/playerTpUtils';
import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import styled from 'styled-components';
import { usePlayerEnergyResetTime } from './PlayerEnergy';
import { getMillisecondsTimeInfo } from '@/utils/dayjsHelper';
import { IS_EXP_SERVER } from '@/constant';

const StyledDialogBody = styled.div`
  box-sizing: border-box;
  position: relative;
  width: 31.25rem;
  height: 18.25rem;
  display: flex;
  padding: 1.5rem 2rem;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;

  border-radius: 3rem;
  border: 0.5rem solid #ed9800;
  background: #fff2e2;
  * {
    box-sizing: border-box;
  }
`;
const StyledTitle = styled.p`
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 0.5rem;
  align-self: stretch;
  & > span:first-of-type {
    color: #ffffff;
    display: flex;
    width: 1.5rem;
    height: 1.5rem;
    padding: 0.625rem;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;

    border-radius: 50%;
    background: #ff8316;
    box-shadow: 0px 0.25rem 4px 0px rgba(0, 0, 0, 0.25);
  }
  & > span:last-of-type {
    color: #140f08;
    text-align: center;
    font-family: 'JetBrains Mono';
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-transform: capitalize;
  }
`;

const StyledBody = styled.div`
  margin: 0;
  color: #686663;
  text-align: center;
  font-family: 'JetBrains Mono';
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  & > p:first-of-type {
    margin: 0;
    margin-bottom: 1.5rem;
  }
`;

const StyledFooter = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: nowrap;
`;

const StyledSvgWrapper = styled(SvgWrapper)`
  width: 1.5rem;
  height: 1.5rem;
`;

const ClockSvg = () => {
  return (
    <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask
        id="mask0_9272_22660"
        style={{
          maskType: 'alpha',
        }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="19"
        height="20">
        <circle cx="9.5" cy="10" r="9.375" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_9272_22660)">
        <circle cx="9.5" cy="10" r="9.0625" fill="#CA5500" stroke="#140F08" strokeWidth="0.625" />
        <circle
          cx="8.875"
          cy="9.375"
          r="8.625"
          fill="#FFCC22"
          stroke="#140F08"
          strokeWidth="0.25"
        />
        <g filter="url(#filter0_i_9272_22660)">
          <circle cx="8.875" cy="9.375" r="6.875" fill="#EDE1CC" />
        </g>
        <circle cx="8.875" cy="9.375" r="6.75" stroke="#140F08" strokeWidth="0.25" />
        <circle cx="9.5" cy="10" r="9.0625" stroke="#140F08" strokeWidth="0.625" />
        <path
          d="M9.89897 5.02269C10.0559 4.73235 10.4115 4.61408 10.7111 4.7526L10.7798 4.78436C11.0794 4.92289 11.2196 5.27045 11.1 5.57807L9.49783 9.70025L7.79597 8.91329L9.89897 5.02269Z"
          fill="#47351B"
        />
        <path
          d="M5.09318 9.80952C4.77305 9.75444 4.54848 9.46331 4.57648 9.13969L4.58518 9.03917C4.61319 8.71555 4.88444 8.46732 5.20928 8.46806L8.35686 8.47522L8.1952 10.3432L5.09318 9.80952Z"
          fill="#47351B"
        />
        <circle cx="8.25" cy="9.375" r="1.125" fill="#FF8316" stroke="#140F08" strokeWidth="0.25" />
      </g>
      <defs>
        <filter
          id="filter0_i_9272_22660"
          x="2"
          y="2.5"
          width="13.75"
          height="13.75"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="0.625" dy="0.625" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend mode="normal" in2="shape" result="effect1_innerShadow_9272_22660" />
        </filter>
      </defs>
    </svg>
  );
};

const StyledEnergyResetDesc = styled.p`
  margin: 0;
  display: inline-flex;
  padding: 0.25rem 0.5rem;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 0.75rem;
  border: 0.0625rem solid #cabfab;
  background: #fff;
  & > span:nth-of-type(2) {
    color: #a58061;
    font-family: Inter;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
  & > span:last-of-type {
    color: #140f08;
    font-family: 'JetBrains Mono';
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: capitalize;
  }
`;

const CloseSvgIcon = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.39558 5.39168C5.73032 5.05694 6.27303 5.05694 6.60777 5.39168L12.0017 10.7856L17.3956 5.39168C17.7303 5.05694 18.273 5.05694 18.6078 5.39168C18.9425 5.72641 18.9425 6.26912 18.6078 6.60386L13.2139 11.9978L18.6078 17.3917C18.9425 17.7264 18.9425 18.2691 18.6078 18.6039C18.273 18.9386 17.7303 18.9386 17.3956 18.6039L12.0017 13.21L6.60777 18.6039C6.27303 18.9386 5.73032 18.9386 5.39558 18.6039C5.06085 18.2691 5.06085 17.7264 5.39558 17.3917L10.7895 11.9978L5.39558 6.60386C5.06085 6.26912 5.06085 5.72641 5.39558 5.39168Z"
        fill="#767676"
      />
    </svg>
  );
};

const StyledCloseSvgIconWrapper = styled(SvgWrapper)`
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  right: 1.25rem;
  top: 0.75rem;
  cursor: pointer;
`;

export interface IEnergyModalRef {
  open: () => void;
}

const EnergyModal = forwardRef<IEnergyModalRef, any>((props, ref) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => {
    setIsOpen(true);
  };
  const handleClose = () => {
    setIsOpen(false);
  };
  const handleConfirm = () => {
    if (IS_EXP_SERVER) return;
    const npcIdArr = [3002, 3003, 3004, 3005, 3006];
    const randomIndex = Math.floor(Math.random() * npcIdArr.length);
    const randomNpcId = npcIdArr[randomIndex];
    playerTpUtil(randomNpcId);
    setIsOpen(false);
  };
  const { energyRefreshTime } = usePlayerEnergyResetTime();
  const { hours, minutes, seconds } = useMemo(() => {
    return getMillisecondsTimeInfo(energyRefreshTime);
  }, [energyRefreshTime]);

  useImperativeHandle(ref, () => {
    return {
      open: handleOpen,
    };
  });

  if (!isOpen) return null;

  return (
    <Dialog isOpen={isOpen} onClose={handleClose}>
      <StyledDialogBody>
        <StyledCloseSvgIconWrapper onClick={handleClose}>
          <CloseSvgIcon />
        </StyledCloseSvgIconWrapper>
        <StyledTitle>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="4"
              height="14"
              viewBox="0 0 4 14"
              fill="none">
              <path
                d="M2.95142 0.5V2.65172L2.57487 9.55517H1.42728L1.05073 2.65172V0.5H2.95142ZM0.65625 12.4421C0.65625 12.1193 0.763836 11.8563 0.979009 11.6531C1.19418 11.4499 1.4751 11.3483 1.82177 11.3483H2.18039C2.52705 11.3483 2.80797 11.4499 3.02315 11.6531C3.23832 11.8563 3.34591 12.1193 3.34591 12.4421C3.34591 12.7648 3.23832 13.0218 3.02315 13.2131C2.80797 13.4044 2.52705 13.5 2.18039 13.5H1.82177C1.4751 13.5 1.19418 13.4103 0.979009 13.231C0.763836 13.0398 0.65625 12.7768 0.65625 12.4421Z"
                fill="white"
              />
            </svg>
          </span>
          <span>energy deficiency</span>
        </StyledTitle>
        <StyledBody>
          <p>You don’t have enough Energy to perform this action.</p>
          <StyledEnergyResetDesc>
            <SvgWrapper
              style={{
                width: '1.25rem',
                height: '1.25rem',
              }}>
              <ClockSvg />
            </SvgWrapper>
            <span>Restore full in</span>
            <span>{`${hours}H ${minutes}M`}</span>
          </StyledEnergyResetDesc>
        </StyledBody>
        <StyledFooter>
          <BasicButton $type="cancel" onClick={handleClose}>
            <span>cancel</span>
          </BasicButton>
          <BasicButton onClick={handleConfirm} disabled={IS_EXP_SERVER}>
            <StyledSvgWrapper>
              <svg
                width="16"
                height="22"
                viewBox="0 0 16 22"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M8.93262 1.22461C9.2502 1.14965 9.59046 1.18732 9.88379 1.35156C10.1769 1.51592 10.3854 1.7856 10.4873 2.09473C10.5905 2.40782 10.5731 2.72813 10.498 3.00098L10.4971 3.00195C10.3898 3.38822 10.2792 3.77284 10.165 4.15527C9.7642 5.49822 9.31655 6.81588 8.83105 8.11133C9.72947 8.44181 10.6207 8.79134 11.502 9.16699L14.3691 10.3906L14.9268 10.6279L14.5869 11.1299L12.8408 13.7139V13.7148C12.8349 13.7236 12.8282 13.73 12.8242 13.7354L12.8252 13.7363C11.3017 15.9877 9.41001 17.9877 7.15625 19.7354C6.82997 19.9884 6.4959 20.2373 6.15332 20.4805L6.15234 20.4824C5.97015 20.6106 5.7436 20.7289 5.48438 20.7822L5.37109 20.8008C5.06382 20.8392 4.74446 20.7775 4.47559 20.5957C4.20707 20.414 4.02994 20.14 3.95117 19.8398C3.87275 19.5406 3.89869 19.244 3.95703 18.9971V18.9951C4.05542 18.5866 4.16278 18.1832 4.27637 17.7861C4.72047 16.2338 5.28271 14.7588 5.96387 13.3623C5.30892 13.0358 4.65643 12.7006 4.01172 12.3477L1.58398 11.0195L1.0957 10.752L1.41406 10.2949L2.73926 8.38965C4.1401 6.3762 5.67575 4.4381 7.34473 2.57617C7.61094 2.27919 7.88091 1.98296 8.1543 1.68945L8.15527 1.68848C8.3492 1.48142 8.61262 1.30007 8.93262 1.22461Z"
                  fill="#F8B81D"
                  stroke="#140F08"
                  strokeLinecap="round"
                />
              </svg>
            </StyledSvgWrapper>
            <span>Buy Energy</span>
          </BasicButton>
        </StyledFooter>
      </StyledDialogBody>
    </Dialog>
  );
});

EnergyModal.displayName = 'EnergyModal';

export default EnergyModal;
