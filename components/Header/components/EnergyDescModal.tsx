import { EventImageRender } from '@/components/EventRules/components/BasicRulesContent';
import Dialog from '@/commons/Dialog';
import { HTMLAttributes, memo, useCallback, useEffect, useMemo, useState } from 'react';
import BasicCommunityModalContent, { BasicTitle } from '@/components/BasicCommunityModalContent';
import styled from 'styled-components';
import { ShakeButton } from '@/components/EasterEggReward/styles';
import { ruleImageMap } from '@/components/EventRules/constant';

interface IEnergyDescModalProps {
  modal: {
    isOpen: boolean;
    open: () => void;
    close: () => void;
    onClose: () => void;
  };
}

const EnergyDescModal: React.FC<IEnergyDescModalProps> = ({ modal }) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  if (!isMounted) {
    return null;
  }
  return (
    <Dialog isOpen={modal.isOpen} onClose={modal.onClose}>
      <StyledBasicCommunityModalContent
        onClose={modal.close}
        modalWidth="70rem"
        modalHeight="40.5rem"
        closeButton={false}
        headerStyle={{
          top: 0,
          left: 0,
          transform: 'translate(0, -50%)',
        }}
        title={
          <StyledBasicTitle title="Energy System">
            <StyledEnergyStokeSvg1 />
            <StyledEnergyStokeSvg2 />
          </StyledBasicTitle>
        }
        customFooter={
          <ShakeButton
            text="OK！"
            onClick={modal.onClose}
            style={{ position: 'absolute', bottom: '2.125rem' }}
          />
        }>
        <StyledEventImageRender popupImages={ruleImageMap.playerEnergy.popupImages} />
      </StyledBasicCommunityModalContent>
    </Dialog>
  );
};

const StyledBasicCommunityModalContent = styled(BasicCommunityModalContent)`
  padding-top: 6.5rem;
  border: none;
  box-shadow:
    inset 0 0 0 0.75rem #ff8316,
    0 0 0.25rem 0 #ffb43d;
  border-radius: 4rem;
`;

const StyledSvgWrapper = styled.span`
  display: flex;
  width: 0.875rem;
  height: 0.875rem;
  justify-content: center;
  align-items: center;
  & > svg {
    width: 100%;
    height: 100%;
  }
`;

const SmallEnergyStokeSvg: React.FC<Omit<HTMLAttributes<HTMLSpanElement>, 'children'>> = ({
  ...restProps
}) => {
  return (
    <StyledSvgWrapper {...restProps}>
      <svg
        width="55"
        height="55"
        viewBox="0 0 55 55"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <g filter="url(#filter0_d_9249_22932)">
          <path
            d="M28.9216 6.69667C29.3698 6.57667 29.8354 6.6198 30.2302 6.82573C30.6246 7.03161 30.9264 7.38738 31.0845 7.82306C31.2429 8.2594 31.2394 8.73854 31.1313 9.18241C30.9219 10.0337 30.7038 10.8819 30.4791 11.7252C29.5947 15.0448 28.5913 18.3025 27.471 21.4991C29.8117 22.2622 32.1269 23.0901 34.4153 23.9863L40.8112 26.4919L37.1319 32.2983C37.1209 32.3156 37.11 32.3336 37.099 32.3509C33.956 37.3061 30.0018 41.7467 25.2373 45.6734C24.5458 46.2433 23.8361 46.8032 23.1104 47.3514C22.7544 47.618 22.3448 47.8313 21.9037 47.8997C21.4633 47.9682 21.0304 47.8894 20.676 47.6649C20.3213 47.44 20.0634 47.0814 19.9376 46.6534C19.8115 46.2252 19.8303 45.7633 19.9197 45.3276C20.1063 44.4376 20.3096 43.5575 20.5303 42.6892C21.4846 38.9353 22.7573 35.3833 24.3498 32.0342C22.5588 31.2238 20.7845 30.3706 19.0267 29.4752L13.5898 26.7085L16.3864 22.4227C19.3154 17.934 22.5438 13.6003 26.0701 9.42321C26.6329 8.75653 27.2028 8.09336 27.7809 7.43461C28.083 7.09206 28.4733 6.81654 28.9216 6.69667Z"
            fill="#FFF826"
            stroke="#140F08"
            strokeWidth="2.20378"
            strokeLinecap="round"
          />
        </g>
        <defs>
          <filter
            id="filter0_d_9249_22932"
            x="0"
            y="0.0078125"
            width="54.4375"
            height="57.3123"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="2.87477" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_9249_22932"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_9249_22932"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    </StyledSvgWrapper>
  );
};
const LargeEnergyStokeSvg: React.FC<Omit<HTMLAttributes<HTMLSpanElement>, 'children'>> = ({
  ...restProps
}) => {
  return (
    <StyledSvgWrapper {...restProps}>
      <svg
        width="118"
        height="118"
        viewBox="0 0 118 118"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <g filter="url(#filter0_d_9249_22934)">
          <path
            d="M45.4636 28.1075C46.014 27.589 46.7156 27.2911 47.4414 27.2815C48.1665 27.2723 48.8745 27.5496 49.438 28.0528C50.0025 28.5566 50.3677 29.247 50.5557 29.9675C50.9129 31.3511 51.2553 32.7369 51.5845 34.1208C52.8803 39.5685 53.9574 45.0193 54.8197 50.4726C58.7686 49.7591 62.7311 49.1584 66.708 48.6762L77.8234 47.3301L77.0295 58.5064C77.0271 58.5397 77.0253 58.574 77.023 58.6074C76.3413 68.1476 74.0974 77.5761 70.2938 86.8933C69.7417 88.2455 69.1557 89.5975 68.5379 90.9451C68.2329 91.6028 67.8098 92.2253 67.2297 92.6643C66.6506 93.1029 65.9683 93.3244 65.2862 93.2761C64.6035 93.2273 63.9562 92.9119 63.4448 92.3949C62.9331 91.8778 62.6031 91.2004 62.3948 90.506C61.9749 89.0845 61.5865 87.6643 61.2323 86.2475C59.7013 80.1226 58.7832 74.0414 58.4808 68.0043C55.2841 68.2251 52.0784 68.3717 48.8638 68.4448L38.9231 68.6753L39.6249 60.3633C40.3601 51.658 41.6448 42.9437 43.478 34.2239C43.7706 32.8322 44.076 31.4401 44.3966 30.048C44.5654 29.323 44.9128 28.6259 45.4636 28.1075Z"
            fill="#FFF826"
            stroke="#140F08"
            strokeWidth="3.59212"
            strokeLinecap="round"
          />
        </g>
        <defs>
          <filter
            id="filter0_d_9249_22934"
            x="0"
            y="0.0078125"
            width="117.766"
            height="122.451"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="4.68582" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_9249_22934"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_9249_22934"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    </StyledSvgWrapper>
  );
};

const StyledEnergyStokeSvg1 = styled(LargeEnergyStokeSvg)`
  position: absolute;
  top: 0;
  left: 0;
  width: 5.375rem;
  height: 5.375rem;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  transform: translate(-6%, -24%);
  z-index: 1;
`;
const StyledEnergyStokeSvg2 = styled(SmallEnergyStokeSvg)`
  position: absolute;
  top: 0;
  left: 0;
  width: 3.3125rem;
  height: 3.3125rem;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  transform: translate(83%, -49%);
  z-index: 1;
`;

const StyledBasicTitle = styled(BasicTitle)`
  width: 30.875rem;
  height: 8.0625rem;
  & > img {
    width: 30.875rem;
    height: 8.0625rem;
  }
  & > p {
    font-family: 'Bevan';
    font-size: 2.875rem;
    font-weight: 400;
    text-shadow: 0.1261875rem 0.252375rem 0rem #000b22;
    &::before {
      display: none;
    }
  }
`;

const StyledEventImageRender = styled(EventImageRender)`
  margin-top: 1.125rem;
  &::before {
    content: '';
    display: flex;
    height: 28.9375rem;
    width: 63rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    border-radius: 2rem;
    background: #d8cfc1;
    box-shadow: 0px 0px 0.5rem 0px rgba(0, 0, 0, 0.25) inset;
    position: absolute;
    top: -1.125rem;
  }
`;

export default memo(EnergyDescModal);

export function useEnergyDescModal(onClose: () => void = () => false) {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);
  const close = useCallback(() => {
    setIsOpen(false);
    onClose();
  }, []);
  const handleClose = useCallback(() => {
    setIsOpen(false);
    onClose();
  }, [onClose]);

  const modal = useMemo(() => {
    return {
      isOpen,
      open,
      close,
      onClose: handleClose,
    };
  }, [close, handleClose, isOpen, open]);

  return modal;
}
