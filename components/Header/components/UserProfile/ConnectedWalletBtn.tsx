import useConnectWallet from '@/hooks/useConnectWallet';
import { resetGameState } from '@/store/game';
import { toFormatAccount } from '@/utils';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import styled from 'styled-components';
import { DropDownIcon } from './style';
import PlayerEnergy from '../PlayerEnergy';
import classNames from 'classnames';
import EnergyDescModal, { useEnergyDescModal } from '../EnergyDescModal';

const ConnectedWalletBtnView = styled.div<{ $menusLength: number }>`
  width: 17.5rem;
  height: 4rem;
  box-sizing: border-box;
  border-radius: 1.5rem;
  cursor: pointer;
  position: relative;
  background: #fff0c9;
  border: 0.0625rem solid #140f08;
  box-shadow: inset 0 0 0 0.25rem #fcdba8;
  margin-left: 2.25rem;

  .profile-menu {
    max-height: 0;
  }

  &:hover {
    & .dropDownIcon {
      transform: rotate(180deg);
    }
  }

  &:hover {
    box-shadow:
      inset 0 0.25rem 0 0 #fcdba8,
      /* 顶部阴影 */ inset 0.25rem 0 0 0 #fcdba8,
      /* 左侧阴影 */ inset -0.25rem 0 0 0 #fcdba8; /* 右侧阴影 */

    border: 0.0625rem solid #140f08;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom: 0;

    .profile-menu {
      max-height: ${(props) => (props.$menusLength + 1) * 3.5}rem;
      border: 0.0625rem solid #140f08;
      border-top: 0;
      overflow: hidden;
      z-index: 1;
      transition: max-height 0.3s;
      box-shadow:
        inset 0.25rem 0 0 0 #fcdba8,
        inset -0.25rem 0 0 0 #fcdba8,
        inset 0 -0.25rem 0 0 #fcdba8;
    }
  }
`;

const StyledProfileContainer = styled.div`
  width: 100%;
  position: relative;
  height: 100%;
  box-sizing: border-box;
  padding-left: 2.625rem;
`;
const StyledAvatarBox = styled.div`
  position: absolute;
  left: 0;
  top: 0;
  width: 4.5rem;
  height: 4.5rem;
  aspect-ratio: 1/1;
  box-sizing: border-box;

  background: url('/image/default-user-avatar.png') lightgray 50% / cover no-repeat;
  background-size: cover;

  box-shadow: 0 0 0px 0.125rem #fff inset;
  border: 0.125rem solid black;
  border-radius: 50%;
  z-index: 11;
  transform: translateY(-0.25rem) translateX(-1.875rem);
`;

const StyledProfileBasicInfo = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0.5rem 1rem 0.5rem 0.75rem;
  box-sizing: border-box;
  align-items: center;
  gap: 0.25rem;
  position: relative;
`;

const StyledProfileMenu = styled.div`
  width: calc(100% + 2px);
  position: absolute;
  left: -0.0625rem;
  top: 100%;
  z-index: 2;
  background: #fff0c9;
  padding: 0 1rem;
  box-sizing: border-box;
  max-height: 0;
  overflow: hidden;
  border-bottom-right-radius: 1.5rem;
  border-bottom-left-radius: 1.5rem;
  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 0.0625rem;
    background: #b7b7b7;
    margin: 0.5rem auto 0;
  }

  .connected-menu-list {
    padding: 0.25rem 0 0.5rem;
    position: relative;
    box-sizing: border-box;
    .bg-box2 {
      width: calc(100% - 3rem);
      height: 3.5rem;
      border-radius: 1.5rem;
      position: absolute;
      left: 0;
      right: 0;
      margin: 0 auto;
      background: #140f08;
      transition: transform 0.3s linear;
      pointer-events: none;
      z-index: -1;
    }
    .connected-menu-item {
      height: 3.5rem;
      display: flex;
      align-items: center;
      justify-self: center;
      color: #140f08;
      text-align: center;
      font-family: 'JetBrains Mono';
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      text-transform: capitalize;
      width: 100%;
      justify-content: center;
      align-items: center;
      &.active {
        color: #ffffff;
      }
    }
  }
`;

const AddressBox = styled.div`
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: 100%;
  margin-top: 0.5rem;
  & > span {
    color: #140f08;
    font-family: 'Inter';
    font-size: 1rem;
    font-style: normal;
    font-weight: 600;
    line-height: 100%;
  }
`;

export const ConnectedWalletBtn = () => {
  const { btcAddress, disconnectWallet } = useConnectWallet();
  const router = useRouter();
  const dispatch = useDispatch();
  const [hoverMenu, setHoverMenu] = useState<number>(-1);
  const modal = useEnergyDescModal();

  const menus = [
    {
      name: 'My Avatar',
      action: () => {
        router.push('/');
      },
    },
    {
      name: 'Log Out',
      action: () => {
        disconnectWallet();
      },
    },
  ];

  return (
    <ConnectedWalletBtnView $menusLength={menus.length}>
      <StyledProfileContainer>
        <StyledAvatarBox />
        <StyledProfileBasicInfo onClick={modal.open}>
          <AddressBox>
            <span>{toFormatAccount(btcAddress, 4, 4)}</span>
            <DropDownIcon />
          </AddressBox>
          <PlayerEnergy />
        </StyledProfileBasicInfo>
      </StyledProfileContainer>
      <StyledProfileMenu className="profile-menu">
        {/* <div className="line" /> */}
        <div className="connected-menu-list">
          <div
            className="bg-box2"
            style={{
              transform:
                hoverMenu === -1
                  ? `translateY(calc(${hoverMenu * 100}% - 0.8125rem))`
                  : `translateY(${hoverMenu * 100}%`,
            }}
          />
          {menus.map((item, index) => (
            <div
              key={index}
              className={classNames('connected-menu-item', hoverMenu === index && 'active')}
              onClick={item.action}
              onMouseEnter={() => setHoverMenu(index)}
              onMouseLeave={() => setHoverMenu(-1)}>
              <span>{item.name}</span>
            </div>
          ))}
        </div>
      </StyledProfileMenu>
      <EnergyDescModal modal={modal} />
    </ConnectedWalletBtnView>
  );
};
