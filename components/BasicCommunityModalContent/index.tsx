import styled from 'styled-components';
import React, { HTMLAttributes, ReactNode } from 'react';
import ButtonLoading from '@/commons/ButtonLoading';

import Image from 'next/image';

interface CloseProps {
  onClick: () => void;
  className?: string;
}

export const ModalCloseBtnWrapper = styled.div`
  position: absolute;
  right: 1rem;
  top: 30%;
  cursor: pointer;
  width: 3rem;
  height: 3rem;
  & > img {
    width: 3rem;
    height: 3rem;
  }
`;

export const Close = ({ onClick, className = '' }: CloseProps) => {
  return (
    <ModalCloseBtnWrapper onClick={onClick} className={className}>
      <Image src="/image/close.png" alt="close" width={46} height={46} draggable={false} />
    </ModalCloseBtnWrapper>
  );
};

type ModalProps = {
  title: ReactNode; // 修改为ReactNode类型，可以接受任何React元素
  children: ReactNode;
  onClose: () => void;
  onCancel?: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
  closeButton?: boolean;
  confirmDisabled?: boolean;
  confirmLoading?: boolean;
  modalBodyPadding?: string;
  maxHeight?: string;
  modalWidth?: string;
  footerStyle?: React.CSSProperties;
  buttonStyle?: React.CSSProperties;
  modalHeight?: string;
  className?: string;
  customFooter?: ReactNode;
  headerStyle?: React.CSSProperties;
  onClick?: (e: any) => void;
};

export const BasicCommunityModalContentContainer = styled.div<{
  modalWidth?: string;
  modalHeight?: string;
}>`
  width: ${({ modalWidth }) => modalWidth || '43.5rem'};
  height: ${({ modalHeight }) => modalHeight || 'auto'};
  min-height: ${({ modalHeight }) => (modalHeight ? '27.625rem' : 'auto')};
  box-sizing: border-box;
  background: #fef1df;
  border: 0.25rem solid #000;
  box-shadow: inset 0 0 0 0.5rem #ff9f1c;
  border-radius: 3rem;
  position: relative;

  display: flex;
  padding: 4.5rem 1.5rem 2.5rem;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  align-self: stretch;
`;

const ModalHeader = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 50%;
  top: -2.1875rem;
  transform: translate(-50%, 0px);
  z-index: 999;
`;

export const ModalBody = styled.div<{ padding?: string; _maxHeight?: string }>`
  /* padding: ${({ padding }) => padding || '20px 20px 20px'}; */
  /* max-height: ${({ _maxHeight }) => _maxHeight || '400px'}; */
  /* overflow-y: auto; */
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  /* padding: 6px 42px 24px; */
`;

const Button = styled.button<{ disabled?: boolean }>`
  padding: 0.75rem 2rem;
  border-radius: 1rem;
  font-size: 1.125rem;
  font-weight: bold;
  cursor: pointer;
  min-width: 11.25rem;
  border: none;
  color: white;
  box-sizing: border-box;
  height: 3.75rem;
  &.confirm {
    background: #fc7922;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
    border-bottom: ${({ disabled }) =>
      disabled ? '0.25rem solid #b8a692' : '0.25rem solid #b5581a'};
    position: relative;
    transition:
      transform 0.1s,
      box-shadow 0.1s,
      border-bottom 0.1s;

    &:hover {
      background: #ff8a3c; /* 稍亮的颜色 */
      cursor: pointer;
    }

    &:not(:disabled):active {
      transform: translateY(0.1875rem);
      box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.2);
      border-bottom: 0.0625rem solid #b5581a;
    }

    &:disabled {
      background: #c1af9c;
      cursor: not-allowed;
    }
  }
  &.cancel {
    background: #c1af9c;
  }
`;

const BasicCommunityModalContent: React.FC<ModalProps> = ({
  title,
  children,
  onClose,
  onCancel,
  onConfirm,
  confirmText = 'Drop',
  cancelText = 'Cancel',
  closeButton = true,
  confirmDisabled = false,
  confirmLoading = false,
  modalBodyPadding,
  maxHeight,
  modalWidth,
  footerStyle,
  buttonStyle,
  modalHeight,
  className,
  customFooter,
  headerStyle = {},
  onClick = () => false,
}) => {
  return (
    <BasicCommunityModalContentContainer
      modalWidth={modalWidth}
      modalHeight={modalHeight}
      className={className ?? ''}
      onClick={onClick}>
      <ModalHeader style={headerStyle ?? {}}>
        {title}
        {closeButton && <Close onClick={onClose} />}
      </ModalHeader>

      <ModalBody padding={modalBodyPadding} _maxHeight={maxHeight}>
        {children}
      </ModalBody>

      {(onCancel || onConfirm) && (
        <ModalFooter
          style={{
            ...footerStyle,
          }}>
          {onCancel && (
            <Button
              className="cancel"
              style={{
                ...buttonStyle,
              }}
              onClick={onCancel}>
              {cancelText}
            </Button>
          )}
          {onConfirm && (
            <Button
              className="confirm"
              onClick={() => {
                onConfirm();
              }}
              disabled={confirmDisabled}
              style={{
                ...buttonStyle,
              }}>
              {confirmLoading ? (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ButtonLoading />
                  Loading...
                </div>
              ) : (
                confirmText
              )}
            </Button>
          )}
        </ModalFooter>
      )}
      {customFooter}
    </BasicCommunityModalContentContainer>
  );
};

export default BasicCommunityModalContent;

const StyledTitle = styled.div`
  position: relative;
  width: 22.5rem;
  height: 4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  & > img {
    position: absolute;
    top: 0;
    display: block;
    left: 0;
    width: 22.5rem;
    height: 4rem;
  }
  & > p {
    z-index: 1;
    color: #fff;
    margin: 0;
    text-align: center;
    text-shadow: 0.0625rem 0.125rem 0px #000b22;
    font-family: 'Baloo 2';
    font-size: 2.5rem;
    font-style: normal;
    font-weight: 800;

    line-height: 100%;
    text-transform: capitalize;
    position: relative;

    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.046875rem #4b2800;
      text-stroke: 0.046875rem #4b2800;
      z-index: -1;
      left: 0;
    }
  }
`;

export const BasicTitle: React.FC<HTMLAttributes<HTMLDivElement>> = ({
  title,
  children,
  ...restProps
}) => {
  return (
    <>
      <StyledTitle {...restProps}>
        <Image src="/image/buy-bg.png" alt="buy-tools" width={360} height={64} draggable={false} />
        <p data-text={title}>{title}</p>
        {children}
      </StyledTitle>
    </>
  );
};
