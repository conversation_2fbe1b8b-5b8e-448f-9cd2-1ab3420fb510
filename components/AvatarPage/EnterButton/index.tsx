import { useSelector } from 'react-redux';
import { IAppState } from '../../../constant/type';
import { EnterButtonView } from './style';
import { ButlerUtil } from '@/game/Global/GlobalButlerUtil';
import GlobalSpaceEvent, {
  GlobalDataKey,
  SpaceStatus,
} from '@/game/Global/GlobalSpaceEvent';
import { useEffect, useState } from 'react';

export default function EnterButton() {
  const { btcAddress, domainOwner, sceneType } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  const [spaceStatus, setSpaceStatus] = useState<SpaceStatus>(SpaceStatus.Avatar);

  useEffect(() => {
    GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(GlobalDataKey.SpaceStatus, (value) => {
      setSpaceStatus(value);
    });
  }, []);
  if (!domainOwner && spaceStatus == SpaceStatus.Avatar) {
    return (
      <EnterButtonView
        onClick={() => {
          // toast('Coming soon', {
          //   duration: 6000,
          //   id: 'enterSpace'
          // })
          ButlerUtil.enterVisitor();
        }}
      >
        <p>Enter Space</p>
        {/*<span>Coming soon</span>*/}
      </EnterButtonView>
    );
  }
  // if (sceneType === SCENE_TYPE.Island){
  //   return <RecordingModal showHistoryOnly={true}/>
  // }
  return null;
}
