import { FTWindowView } from './style';
import { useEffect, useState } from 'react';
import AvatarDataFT from '@/game/TS/Data/AvatarDataFT';
import * as THREE from 'three';
import { IBrc20Result, ICatResult, IRunesResult, TOKEN_TYPE_ENUM } from '../../constant/type';
// import {toNumber} from "lodash";
import { toNumber } from 'es-toolkit/compat';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';

interface IProps {}

export default function FTWindow({}: IProps) {
  const [dataRef, setData] = useState<AvatarDataFT | null>(null);
  const [mouse, setMouse] = useState<THREE.Vector2>(new THREE.Vector2(0, 0));
  const [name, setName] = useState<string | null>(null);
  const [id, setId] = useState<string | null>(null);
  const [decimals, setDecimals] = useState<number | null>(null);
  const [balance, setBalance] = useState<number | null>(null);

  useEffect(() => {
    //监听悬停NFT
    const ftKey = GlobalSpaceEvent.ListenKeyDataChange<{
      ft: AvatarDataFT;
      mouse: THREE.Vector2;
    }>(GlobalDataKey.FtHoverObj, (obj: { ft: AvatarDataFT; mouse: THREE.Vector2 }) => {
      setData(null);
      if (obj.ft) {
        const ftData = obj.ft;
        setData(ftData);
        setMouse(obj.mouse);
        if (ftData) {
          setName(null);
          setId(null);
          setDecimals(null);
          setBalance(null);
          switch (ftData.type) {
            case TOKEN_TYPE_ENUM.Brc20:
              const brc20Data = ftData.data as IBrc20Result;
              setName(brc20Data.ticker);
              setDecimals(brc20Data.decimal);
              setBalance(toNumber(brc20Data.availableBalance));
              break;
            case TOKEN_TYPE_ENUM.Runes:
              const runesData = ftData.data as IRunesResult;
              setName(runesData.spacedRune);
              setId(runesData.runeid);
              setBalance(toNumber(runesData.amount));
              break;
            case TOKEN_TYPE_ENUM.Cat20:
              const cat20Data = ftData.data as ICatResult;
              setName(cat20Data.name);
              setDecimals(cat20Data.decimals);
              setBalance(cat20Data.balance);
              break;
          }
        }
        return;
      }
    });

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.FtHoverObj, ftKey);
    };
  }, []);

  return (
    <FTWindowView style={{ top: mouse.y + 35, left: mouse.x + 20 }}>
      {dataRef && (
        <div className={'currency-box'}>
          <svg
            width="122"
            height="54"
            viewBox="0 0 122 54"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="fixed-nail"
          >
            <circle cx="16" cy="16" r="15" stroke="#FAFAFA" strokeWidth="2" />
            <circle cx="16" cy="16" r="12" fill="#FAFAFA" />
            <path d="M30 21L121.5 53" stroke="#FAFAFA" strokeWidth="2" />
          </svg>

          <div className="currency-box-bg" />
          {
            <div className="currency-box-content">
              {name && (
                <p>
                  Name: <span className="currency-name">{name}</span>
                </p>
              )}
              {id && (
                <p>
                  ID: <span className="currency-name">{id}</span>
                </p>
              )}
              {decimals && (
                <p>
                  Decimals: <span className="currency-name">{decimals}</span>
                </p>
              )}
              {balance && (
                <p>
                  Balance: <span className="balance">{balance}</span>
                </p>
              )}
            </div>
          }
        </div>
      )}
    </FTWindowView>
  );
}
