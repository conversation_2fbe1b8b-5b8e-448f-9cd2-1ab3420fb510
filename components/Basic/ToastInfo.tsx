import styled from 'styled-components';
import SvgWrapper, { SpriteSvg } from '../SvgWrapper';
const StyledInfoWrapper = styled.div`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow:
    0 0.1875rem 0.625rem rgba(0, 0, 0, 0.1),
    0 0.1875rem 0.1875rem rgba(0, 0, 0, 0.05);
  max-width: 21.875rem;
  pointer-events: auto;
  padding: 0.5rem 0.625rem;
  border-radius: 0.5rem;
  & > span:last-of-type {
    display: flex;
    justify-content: center;
    margin: 0.25rem 0.625rem;
    color: inherit;
    flex: 1 1 auto;
    white-space: pre-line;
    margin: 0.25rem 0.625rem;
  }
`;
const StyledInfoIcon = styled(SvgWrapper)`
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background: #1677ff;
  box-shadow: 0px 0.25rem 4px 0px rgba(0, 0, 0, 0.25);
  & > svg {
    width: 0.168125rem;
    height: 0.8125rem;
    flex-shrink: 0;
  }
`;

const ToastInfo = ({ text }: { text: string }) => {
  return (
    <StyledInfoWrapper>
      <StyledInfoIcon>
        <SpriteSvg id="exclamation" />
      </StyledInfoIcon>
      <span>{text}</span>
    </StyledInfoWrapper>
  );
};

export default ToastInfo;
