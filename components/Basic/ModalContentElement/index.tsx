import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import styled from 'styled-components';

export const StyledDialogBody = styled.div`
  box-sizing: border-box;
  position: relative;
  width: 31.25rem;
  height: auto;
  display: flex;
  padding: 1.5rem 2rem;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;

  border-radius: 3rem;
  border: 0.5rem solid #ed9800;
  background: #fff2e2;
  box-shadow: 0 0 0 0.25rem #140f08;
  * {
    box-sizing: border-box;
  }
`;

export const StyledTitle = styled.p`
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 0.5rem;
  align-self: stretch;
  & > span:last-of-type {
    color: #140f08;
    text-align: center;
    font-family: 'JetBrains Mono';
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-transform: capitalize;
  }
`;

export const StyledBody = styled.div`
  margin: 0;
  color: #686663;
  text-align: center;
  font-family: 'JetBrains Mono';
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  & > p:first-of-type {
    margin: 0;
    margin-bottom: 1.5rem;
  }
`;

export const StyledFooter = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: nowrap;
`;

const StyledExclamationIconWrapper = styled(SvgWrapper)`
  color: #ffffff;
  display: flex;
  width: 1.5rem;
  height: 1.5rem;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;

  border-radius: 50%;
  background: #ff8316;
  box-shadow: 0px 0.25rem 4px 0px rgba(0, 0, 0, 0.25);
  & > svg {
    width: 0.168125rem;
    height: 0.8125rem;
    flex-shrink: 0;
  }
`;

export const ExclamationIcon = () => {
  return (
    <StyledExclamationIconWrapper>
      <SpriteSvg id="exclamation" />
    </StyledExclamationIconWrapper>
  );
};

const StyledCloseSvgIconWrapper = styled(SvgWrapper)`
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  right: 1.25rem;
  top: 0.75rem;
  cursor: pointer;
`;

export const CloseIcon = ({
  onCloseClick,
  className = '',
}: {
  onCloseClick: (arg?: any) => void;
  className?: string;
}) => {
  return (
    <StyledCloseSvgIconWrapper onClick={onCloseClick} className={className}>
      <SpriteSvg id="closeIcon" />
    </StyledCloseSvgIconWrapper>
  );
};
