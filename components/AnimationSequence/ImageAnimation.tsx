import { motion } from 'framer-motion';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import styled from 'styled-components';
import { AnimationConfig } from '@/components/AnimationSequence/index';
import { AnimationConfigItem } from './config';
import { px2rem } from '@/utils/px2rem';
import useLatest from '@/hooks/useLatest';

type DebouncedFunction<T extends any[]> = (...args: T) => void;

export function useDebounce<T extends any[]>(
  callback: (...args: T) => void,
  delay: number
): DebouncedFunction<T> {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清除定时器
  const clearTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  // 防抖函数
  const debouncedFn = useCallback<DebouncedFunction<T>>(
    (...args: T) => {
      clearTimer();
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  );

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => clearTimer();
  }, []);

  return debouncedFn;
}

const StyleImageClickBox = styled.div<{ $bgImgSrc?: string; $rotateDeg?: number }>`
  width: 25rem;
  height: 23.125rem;
  background: url(${(props) => props.$bgImgSrc}) no-repeat center center;
  background-size: contain;
  transform: rotate(${(props) => props.$rotateDeg || 0}deg);
`;

// 定义12个固定位置点
const fixedPositions = [
  // 左侧位置 - 考虑图片宽度的一半 (9.375rem)
  { id: 1, x: '-1.875rem', y: '25%', area: 'left' }, // A1
  { id: 6, x: '-1.875rem', y: '40%', area: 'left' }, // A2
  { id: 8, x: '-1.875rem', y: '50%', area: 'left' }, // A3
  { id: 2, x: '-1.875rem', y: '60%', area: 'left' }, // A4

  // 底部位置 - 考虑图片高度的一半 (8.4375rem)
  { id: 3, x: '10%', y: 'calc(100% - 21.875rem)', area: 'bottom' }, // B1
  { id: 7, x: '30%', y: 'calc(100% - 21.875rem)', area: 'bottom' }, // B2
  { id: 9, x: '50%', y: 'calc(100% - 21.875rem)', area: 'bottom' }, // B3
  { id: 4, x: '70%', y: 'calc(100% - 21.875rem)', area: 'bottom' }, // B4

  // 右侧位置 - 考虑图片宽度的一半 (9.375rem)
  { id: 10, x: 'calc(100% - 23.125rem)', y: '15%', area: 'right' }, // C1
  { id: 5, x: 'calc(100% - 23.125rem)', y: '35%', area: 'right' }, // C2
  { id: 12, x: 'calc(100% - 23.125rem)', y: '50%', area: 'right' }, // C3
  { id: 11, x: 'calc(100% - 23.125rem)', y: '60%', area: 'right' }, // C4
];

// 定义入场方向
const directionByArea = {
  left: { x: -50, y: 0 },
  right: { x: 50, y: 0 },
  bottom: { x: 0, y: 50 },
};

// 获取位置信息
const getPositionByTag = (tag: number) => {
  const position = fixedPositions.find((pos) => pos.id === tag);
  if (!position) {
    throw new Error(`Invalid tag: ${tag}`);
  }

  return {
    position: { x: position.x, y: position.y },
    direction: directionByArea[position.area as keyof typeof directionByArea],
    area: position.area as 'left' | 'right' | 'bottom',
  };
};

// 只保留过渡动画的时间常量
const TRANSITION_DURATION = 100; // 动画过渡时间

// 使用styled-components创建样式组件
const Container = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
`;

const AnimatedWrapper = styled(motion.div)<{ $posX: string; $posY: string }>`
  position: absolute;
  left: ${(props) => props.$posX};
  top: ${(props) => props.$posY};
  transform: translate(-50%, -50%);
  z-index: 999;
  /* cursor: pointer; */
`;

// 定义组件的 ref 类型
export interface ImageAnimationRef {
  start: () => void;
  reset: () => void;
}

// 添加新的类型定义
interface ClickRecord {
  tag: number;
  t: number;
}

interface ImageAnimationProps {
  onComplete?: (
    result: boolean,
    content?: string,
    readyTimestamp?: number,
    firstAnimationStartTimestamp?: number
  ) => void;
  onImageClick?: (number: number) => void;
  animationConfig: AnimationConfig[];
  imageConfig: AnimationConfigItem;
}

const ImageAnimation = forwardRef<ImageAnimationRef, ImageAnimationProps>(
  ({ onComplete, onImageClick, animationConfig, imageConfig }, ref) => {
    const startRef = useRef<boolean>(false);
    const clickEndTimestampRef = useRef<number>(0);
    const [isVisible, setIsVisible] = useState(false);
    const [currentIndex, setCurrentIndex] = useState(0);
    // const [isEntering, setIsEntering] = useState(true);
    const [isCompleted, setIsCompleted] = useState(false);
    const [animationSequence, setAnimationSequence] = useState<any[]>([]);
    const [clickedSequence, setClickedSequence] = useState<ClickRecord[]>([]);
    const readyTimestampRef = useRef<number>(0);
    const firstAnimationStartTimestampRef = useRef<number>(0);
    const [isClicked, setIsClicked] = useState(false);
    const visibleRef = useLatest(isVisible);

    const debouncedComplete = useDebounce(onComplete as any, 1500);

    useEffect(() => {
      // 执行逻辑以检查当前动画是否已被点击
      if (!startRef.current) return;

      // 检查之前的动画是否都被点击
      if (currentIndex > 0) {
        // 之前动画的索引
        const prevAnimationIndex = currentIndex - 1;

        // 检查对应的点击记录是否存在
        const hasClickForPrevAnimation = clickedSequence.some(
          (click) => click.tag === animationConfig[prevAnimationIndex]?.tag
        );

        // 如果之前的动画没有被点击，或者点击序列长度小于当前索引
        // (意味着有某个动画被跳过没点击)
        if (!hasClickForPrevAnimation || clickedSequence.length < currentIndex) {
          // console.log("动画未被点击，停止序列", {
          //   currentIndex,
          //   clickedSequence,
          //   prevAnimationTag: animationConfig[prevAnimationIndex]?.tag,
          // });

          // 标记为完成并隐藏当前图片
          setIsCompleted(true);
          setIsVisible(false);

          // 可以在这里添加更多的失败处理逻辑
          // 例如，显示提示信息等
        }
      }

      // // 另外验证已点击的序列是否正确
      // // 检查目前为止点击的所有图片是否与动画配置匹配
      // const clicksAreValid = clickedSequence.every(
      //   (click, index) => click.tag === animationConfig[index]?.tag
      // );

      // if (!clicksAreValid && clickedSequence.length > 0) {
      //   console.log("点击序列不匹配配置，停止动画", {
      //     clickedSequence,
      //     expectedSequence: animationConfig
      //       .map((config) => config.tag)
      //       .slice(0, clickedSequence.length),
      //   });

      //   setIsCompleted(true);
      //   setIsVisible(false);
      // }
    }, [clickedSequence, animationConfig, currentIndex]);

    // 修改生成序列的函数
    const generateSequence = () => {
      return animationConfig.map((config) => ({
        ...getPositionByTag(config.tag),
        number: config.tag,
      }));
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      start: () => {
        // 记录动画准备就绪的时间戳
        readyTimestampRef.current = Date.now();
        // console.log("准备就绪时间戳:", readyTimestampRef.current);
        setAnimationSequence(generateSequence());
        setClickedSequence([]);
        setIsVisible(false);
        setCurrentIndex(0);
        // setIsEntering(true);
        setIsCompleted(false);
        startRef.current = true;
      },
      reset: () => {
        startRef.current = false;
        setCurrentIndex(0);
        setIsCompleted(false);
        setAnimationSequence([]);
        setIsVisible(false);
        setClickedSequence([]);
        readyTimestampRef.current = 0;
        firstAnimationStartTimestampRef.current = 0;
      },
    }));

    // 处理图片点击
    const handleImageClick = (number: number) => {
      if (visibleRef.current === false) return;
      // 立即设置图片为点击状态
      setIsClicked(true);

      setClickedSequence((prev) => {
        // 如果已经点击的数量等于或超过配置的动画数量，不再添加新的点击
        if (prev.length >= animationConfig.length) {
          return prev;
        }

        // 记录点击时的时间戳
        const newClickRecord = {
          tag: number,
          t: Date.now(),
        };
        let newClickedSequence = [...prev, newClickRecord];
        let expectedTag = animationConfig[prev.length]?.tag;

        if (prev.length !== 0 && number === prev[prev.length - 1].tag) {
          // 重复点击取第一次的值
          newClickedSequence = [...prev];
          newClickRecord.t = prev[prev.length - 1].t;
          expectedTag = animationConfig[prev.length - 1]?.tag;
        }
        const isCorrectOrder = expectedTag === number;
        if (!isCorrectOrder || clickEndTimestampRef.current < newClickRecord.t) {
          setIsCompleted(true);
        }
        return newClickedSequence;
      });

      if (onImageClick) {
        onImageClick(number);
      }
    };

    useEffect(() => {
      if (!startRef.current) return;
      // 存储所有定时器的引用
      const timeouts: NodeJS.Timeout[] = [];

      firstAnimationStartTimestampRef.current = Date.now();
      animationConfig.forEach((config, index) => {
        // 第一个定时器：控制动画的开始
        timeouts.push(
          setTimeout(() => {
            // setIsEntering(true); // 设置进入状态
            setIsVisible(true); // 使图片可见
            clickEndTimestampRef.current = firstAnimationStartTimestampRef.current + config.endTime; // 设置点击截止时间
          }, config.startTime)
        ); // 在指定的开始时间执行

        // 第二个定时器：控制动画的结束
        timeouts.push(
          setTimeout(() => {
            // 先设置不可见
            setIsVisible(false);
            // 等待过渡动画完成后再更新其他状态
            setTimeout(() => {
              // setIsEntering(false);
              // 在动画完全结束后再更新 currentIndex
              setCurrentIndex((prev) => prev + 1);
            }, TRANSITION_DURATION);
          }, config.endTime)
        ); // 在指定的结束时间执行
      });

      return () => {
        timeouts.forEach((timeout) => clearTimeout(timeout));
      };
    }, [startRef.current]);

    useEffect(() => {
      // console.log("Animation completed!  111111111   ", currentIndex);
      if (currentIndex >= animationConfig.length) {
        // console.log("Animation completed!");
        setIsCompleted(true);
      }
    }, [currentIndex]);

    // 在设置下一个图片时，重置点击状态
    useEffect(() => {
      setIsClicked(false);
    }, [currentIndex]);

    // 监听完成状态来触发回调
    useEffect(() => {
      if (isCompleted && animationConfig.length > 0) {
        const isAllClicked = clickedSequence.length === animationConfig.length;
        const isCorrectSequence = clickedSequence.every(
          (record, index) => record.tag === animationConfig[index]?.tag
        );

        // console.log("Animation completed:", {
        //   isAllClicked,
        //   isCorrectSequence,
        //   clickedSequence,
        //   expectedSequence: animationConfig.map((config) => config.tag),
        // });
        const submitData = JSON.stringify(clickedSequence);
        debouncedComplete?.(
          true,
          submitData,
          readyTimestampRef.current,
          firstAnimationStartTimestampRef.current
        );

        // if (isAllClicked && isCorrectSequence) {
        //   // 生成符合要求的数据字符串
        //   const submitData = JSON.stringify(clickedSequence);
        //   // console.log("submitData=========", submitData);
        //   onComplete?.(
        //     true,
        //     submitData,
        //     readyTimestampRef.current,
        //     firstAnimationStartTimestampRef.current
        //   );
        // } else {
        //   onComplete?.(false);
        // }
      }
    }, [
      isCompleted,
      clickedSequence,
      animationConfig,
      readyTimestampRef,
      firstAnimationStartTimestampRef,
    ]);

    const currentAnimation = animationSequence[currentIndex];

    const rotateDeg = useMemo(() => {
      if (currentAnimation?.area) {
        const AREA_TO_DEG = {
          left: 90,
          bottom: 0,
          right: -90,
        };
        const area = currentAnimation.area as keyof typeof AREA_TO_DEG;
        return AREA_TO_DEG[area];
      }
      return 0;
    }, [currentAnimation?.area]);

    // 如果动画未激活，不渲染任何内容
    if (currentIndex >= animationSequence.length) {
      return null;
    }

    return (
      <Container>
        <AnimatedWrapper
          $posX={currentAnimation.position.x}
          $posY={currentAnimation.position.y}
          initial={{
            opacity: 0,
            x: px2rem(currentAnimation.direction.x * 3)
              .toString()
              .concat('rem'),
            y: px2rem(currentAnimation.direction.y * 3)
              .toString()
              .concat('rem'),
          }}
          animate={{
            opacity: isVisible ? 1 : 0,
            x: isVisible
              ? 0
              : px2rem(currentAnimation.direction.x * 2)
                  .toString()
                  .concat('rem'),
            y: isVisible
              ? 0
              : px2rem(currentAnimation.direction.y * 2)
                  .toString()
                  .concat('rem'),
          }}
          transition={{
            opacity: {
              duration: 0.05, // 极短的透明度过渡时间
              ease: 'linear', // 使用线性过渡让显示更快
            },
            x: {
              type: 'spring',
              stiffness: 1000, // 增加刚性
              damping: 20, // 减少阻尼
              velocity: 5, // 增加初始速度
              restSpeed: 0.1, // 更快到达终点
            },
            y: {
              type: 'spring',
              stiffness: 1000,
              damping: 20,
              velocity: 5,
              restSpeed: 0.1,
            },
          }}
          onClick={() => handleImageClick(currentAnimation.number)}>
          <StyleImageClickBox
            $bgImgSrc={isClicked ? imageConfig.eventImageActiveSrc : imageConfig.eventImageSrc}
            $rotateDeg={rotateDeg}
          />
        </AnimatedWrapper>
      </Container>
    );
  }
);

ImageAnimation.displayName = 'ImageAnimation';

export default ImageAnimation;
