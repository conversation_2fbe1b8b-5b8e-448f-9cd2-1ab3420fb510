import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState, IGameState, IPetInfoItem, IPetShedInfoList } from '@/constant/type';
// import CapWidgetModal from "../../CapWidgetModal";
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import EventRules from '@/components/EventRules';
import RulesContent from '@/components/EventRules/components/RulesContent';
import PizzaRulesContent from '@/components/EventRules/components/PizzaRulesContent';
import { useComboManager } from '@/components/Combo/ComboManager';
import { Reward, RewardsRef } from '@/components/ChatRoom/components/Reward';
import { SurpriseReward, SurpriseRewardRef } from '@/components/SurpriseReward';
import AnimationSequence, { AnimationSequenceRef } from '@/components/AnimationSequence';
import { DecryptedDataJson, useFish } from '@/hooks/useFish';
import { useResourceList } from '@/hooks/useResourceList';
import useConnectWallet from '@/hooks/useConnectWallet';
import { resetGameState, setMaterialList, updateGameState } from '@/store/game';
import { setLoaderType, setShowConnectWallet } from '@/store/app';
import { LoadingPageType } from '@/game/Config/DoorConfig';
import { PetBedManufactureModal, ToolSynthesisModal } from '../components/SynthesisSystem';
import { ItemDropData } from '@/game/Config/ItemDropConfig';
import { useNetWork } from '@/game/TS/useNetWork';
import useBagInventory from '@/hooks/useBagInventory';
import WhackAMoleRules from '@/components/EventRules/components/WhackAMoleRules';
import OrderTreeRules from '@/components/EventRules/components/OrderTreeRules';
import EasterEggReward, { useEasterEggReward } from '@/components/EasterEggReward';
import OrderTreeFailed, { useOrderTreeFailed } from '@/components/OrderTreeFailed';
import TimeLeft from '@/components/TimeLeft';
import PizzaActivityWindow from '../PizzaActivityWindow';
import styled from 'styled-components';
import { useUserBasicInfo } from '@/hooks/useUserBasicInfo';
import PetRules from '@/components/EventRules/components/PetRules';
import PetBedRules from '@/components/EventRules/components/PetBedRules';
import { updateModalState } from '@/store/modal';
import { useAppSelector } from '@/hooks/useStore';
import PlacePetBedModal from '../components/PlacePetBedModal';
import PetFusionModal from '../components/PetFusionModal';
import useUpdatePetListItem from '@/hooks/useUpdatePetListItem';
import useGameEnter from '@/hooks/useGameEnter';
import toast from 'react-hot-toast';
import ToastInfo from '@/components/Basic/ToastInfo';
import { PetFeaturesConfig } from '@/game/Config/PetFeaturesConfig';
import useLatest from '@/hooks/useLatest';

const GameApiWindow: React.FC = () => {
  const myPlayer = GetMyPlayer();
  const { updateDurability, removeItemFromRedux } = useBagInventory();
  // 添加显示/隐藏状态
  const dispatch = useDispatch();
  // 添加按钮加载状态
  const [isOpenRule, setIsOpenRule] = useState(false);
  const [isOpenPizzaRule, setIsOpenPizzaRule] = useState(false);
  const [isOpenWhackAMole, setIsOpenWhackAMole] = useState(false);
  const [isOpenOrderTree, setIsOpenOrderTree] = useState(false);
  const petDescModalOpenConfig = useAppSelector(
    (state) => state.ModalReducer.petDescModalOpenConfig
  );
  const petBedDescModalConfig = useAppSelector((state) => state.ModalReducer.petBedDescModalConfig);
  const btcAddress = useAppSelector((state) => state.AppReducer.btcAddress);
  useGameEnter();
  useResourceList({ autoFetch: true });
  const { onGetFishing, onSetFishScore, onCompleteFishEggTask } = useFish();
  const { showComboWithType, ComboDisplay } = useComboManager();
  const { randomEventResult, whackAMoleEasterEgg } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const petShedInfo = useAppSelector(
    (state: { GameReducer: IGameState }) => state.GameReducer.petShedInfo
  );
  const { refreshUserBasicInfo } = useUserBasicInfo();

  const { disconnectWallet } = useConnectWallet();
  const { getMaterialListData } = useBagInventory(false, false);
  const animationSequenceRef = useRef<AnimationSequenceRef>(null);
  const surpriseRewardRef = useRef<SurpriseRewardRef>(null);
  const rewardRef = useRef<RewardsRef>(null);
  const easterEggRewardRef = useEasterEggReward();
  const orderTreeFailedRef = useOrderTreeFailed();
  const { sendPickUpDrop, sendCutTree, sendMiningRock } = useNetWork();
  const { updateGameStatePetItemData } = useUpdatePetListItem();
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const materialList = useAppSelector((state) => state.GameReducer.materialList);
  const petListRef = useLatest(petList);
  const latestPetShedInfoRef = useLatest(petShedInfo);
  const latestMaterialListRef = useLatest(materialList);

  useEffect(() => {
    if (randomEventResult) {
      surpriseRewardRef.current?.open({
        quantity: randomEventResult.quantity.toString(),
        name: randomEventResult.tag || '',
      });
    }
  }, [randomEventResult]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.activityRule, (ruleType: number) => {
        switch (ruleType) {
          case 1:
            setIsOpenRule(true);
            break;
          case 2:
            setIsOpenPizzaRule(true);
            break;
          case 3:
            setIsOpenWhackAMole(true);
            break;
          case 4:
            setIsOpenOrderTree(true);
            break;
          default:
            setIsOpenRule(true);
            break;
        }
      });
      myPlayer.setAppApi(AppGameApiKey.showCombo, (combo: number) => {
        showComboWithType(combo);
      });
      myPlayer.setAppApi(AppGameApiKey.setLoaderType, (type: LoadingPageType) => {
        dispatch(setLoaderType(type));
      });
      myPlayer.setAppApi(AppGameApiKey.pickUpDrop, async (dropData: ItemDropData) => {
        sendPickUpDrop(String(dropData.id));
      });
    }
  }, []);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.updateItemDurability,
        async (userItemId: string, currentDurability: number) => {
          updateDurability(userItemId, currentDurability);
        }
      );
    }
  }, [updateDurability]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.removeItemId, async (itemId: string) => {
        removeItemFromRedux(itemId);
      });
    }
  }, [removeItemFromRedux]);

  // useEffect(() => {
  //   if (myPlayer) {
  //     myPlayer.setAppApi(AppGameApiKey.startDogEgg, () => {
  //       if (dogEasterEgg) {
  //         setIsOpenWhackAMole(true);
  //         // animationSequenceRef.current?.start();
  //       }
  //     });
  //   }
  // }, [dogEasterEgg]);

  const onOrderTreeModalClose = useCallback(() => {
    setIsOpenOrderTree(false);
  }, []);

  const onWhackAMoleRulesModalClose = useCallback(() => {
    setIsOpenWhackAMole(false);
    if (whackAMoleEasterEgg) {
      animationSequenceRef.current?.start();
    }
  }, [whackAMoleEasterEgg]);

  const onPetRulesClose = useCallback(() => {
    petDescModalOpenConfig.confirmCallback?.();
    dispatch(updateModalState({ petDescModalOpenConfig: { isOpen: false } }));
  }, [petDescModalOpenConfig]);
  const onPetBedRulesClose = useCallback(() => {
    petBedDescModalConfig.confirmCallback?.();
    dispatch(updateModalState({ petBedDescModalConfig: { isOpen: false } }));
  }, [petBedDescModalConfig]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.showRewards, (name: string, quantity: string) => {
        rewardRef.current?.open({ name, quantity });
      });
    }
  }, []);
  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.authTwitter, () => {
        // setIsModalOpen(true)
        if (!myPlayer.btcAddress) {
          dispatch(setShowConnectWallet(true));
          return;
        }
      });
    }
  }, []);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.cutTree,
        (treeTag: number, treeServerId: string, userItemId: string) => {
          sendCutTree(treeTag, treeServerId, userItemId);
        }
      );
    }
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.mining, (rockTag: number, userItemId: string) => {
        sendMiningRock(rockTag, userItemId);
      });
    }
  }, [myPlayer]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.useFishingRod,
        (callback: (json: DecryptedDataJson) => void) => {
          onGetFishing(myPlayer.axeParams?.userItemId || '').then((json) => {
            callback(json as DecryptedDataJson);
          });
        }
      );
    }
  }, [onGetFishing]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.fishingSuccess, () => {
        onSetFishScore().then();
      });
    }
  }, [onSetFishScore]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.disconnectWallet, () => {
        disconnectWallet();
      });
    }
  }, [disconnectWallet]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.finishFishEgg, () => undefined);
    }
  }, [onCompleteFishEggTask]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.nextDay, () => {
        refreshUserBasicInfo();
      });
    }
  }, [refreshUserBasicInfo]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.getDispatch, (callback: (dispatch: any) => void) => {
        callback(dispatch);
      });
    }
  }, [dispatch, myPlayer]);

  useEffect(() => {
    if (myPlayer) {
      const tips = ({ stamina, petId, action }: any) => {
        if (stamina === 0) {
          toast.custom(<ToastInfo text={`Your pet has run out of energy`} />, {
            removeDelay: 3000,
            duration: 1000,
          });
          return;
        }

        const latestPetList = petListRef.current;
        try {
          const currentPetItemFeatureList = latestPetList.find(
            (item) => item._id === petId
          )?.featureInfos;
          const featureInfo = currentPetItemFeatureList?.find((item) => item.feature === action);
          PetFeaturesConfig.getInstance().getData(
            featureInfo!.feature,
            featureInfo!.featureLevel,
            (data) => {
              const staminaConsume = data.staminaConsume;
              if (stamina < staminaConsume) {
                toast.custom(<ToastInfo text={`Your pet has run out of energy`} />, {
                  removeDelay: 3000,
                  duration: 1000,
                });
              }
            }
          );
        } catch (error) {}
      };
      myPlayer.setAppApi(AppGameApiKey.petCutTree, (data: Partial<IPetInfoItem>) => {
        tips({ stamina: data.currentStamina, petId: data._id, action: 'Axe' });
        updateGameStatePetItemData({ followList: [], targetPet: data });
      });
      myPlayer.setAppApi(AppGameApiKey.petMining, (data: Partial<IPetInfoItem>) => {
        tips({ stamina: data.currentStamina, petId: data._id, action: 'Pickaxe' });
        updateGameStatePetItemData({ followList: [], targetPet: data });
      });
      myPlayer.setAppApi(AppGameApiKey.petFish, (data: Partial<IPetInfoItem>) => {
        tips({ stamina: data.currentStamina, petId: data._id, action: 'FishingPole' });
        updateGameStatePetItemData({ followList: [], targetPet: data });
      });
    }
  }, [myPlayer]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.updatePetShedInfo, (data: IPetShedInfoList) => {
        const latestPetShedInfo = latestPetShedInfoRef.current;
        const newPetShedInfo: Partial<IPetShedInfoList>[] = [];
        latestPetShedInfo.forEach((item) => {
          if (item.positionTag === data.positionTag) {
            newPetShedInfo.push({ ...item, ...data });
          } else {
            newPetShedInfo.push({ ...item });
          }
        });
        dispatch(updateGameState({ petShedInfo: newPetShedInfo }));
      });
    }
  }, [myPlayer]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.petUpdateMaterial,
        (data: { materialTag: string; materialQuantity: number }) => {
          const latestMaterialList = latestMaterialListRef.current;
          if (
            latestMaterialList.length === 0 ||
            latestMaterialList.findIndex((item) => item.tag === data.materialTag) === -1
          ) {
            getMaterialListData();
            return;
          } else {
            const idx = latestMaterialList.findIndex((item) => item.tag === data.materialTag);
            const newList = [...latestMaterialList];
            newList[idx] = {
              ...newList[idx],
              quantity: newList[idx].quantity + data.materialQuantity,
            };
            dispatch(setMaterialList(newList));
          }
        }
      );
    }
  }, [myPlayer]);

  return (
    <>
      {/* <CapWidgetModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onVerify={handleVerify}
        isFooter={false}
        isKey="ef2a564429f7"
      /> */}
      <EventRules isOpen={isOpenRule} onClose={() => setIsOpenRule(false)}>
        <RulesContent onClose={() => setIsOpenRule(false)} />
      </EventRules>
      <EventRules isOpen={isOpenPizzaRule} onClose={() => setIsOpenPizzaRule(false)}>
        <PizzaRulesContent onClose={() => setIsOpenPizzaRule(false)} />
      </EventRules>
      <EventRules isOpen={isOpenOrderTree} onClose={onOrderTreeModalClose}>
        <OrderTreeRules onClose={onOrderTreeModalClose} />
      </EventRules>
      <EventRules isOpen={isOpenWhackAMole} onClose={onWhackAMoleRulesModalClose}>
        <WhackAMoleRules onClose={onWhackAMoleRulesModalClose} />
      </EventRules>
      <EventRules isOpen={petDescModalOpenConfig.isOpen} onClose={onPetRulesClose}>
        <PetRules onClose={onPetRulesClose} />
      </EventRules>
      <EventRules isOpen={petBedDescModalConfig.isOpen} onClose={onPetBedRulesClose}>
        <PetBedRules onClose={onPetBedRulesClose} />
      </EventRules>
      <PlacePetBedModal />
      {/* 连击Combo */}
      {ComboDisplay && <ComboDisplay />}
      <SurpriseReward ref={surpriseRewardRef} />
      <Reward ref={rewardRef} />
      <EasterEggReward ref={easterEggRewardRef} />
      <AnimationSequence
        ref={animationSequenceRef}
        animationConfigs={whackAMoleEasterEgg?.ruleInfo as any}
        rewardType={whackAMoleEasterEgg?.rewardType}
      />
      <OrderTreeFailed ref={orderTreeFailedRef} />
      <PetBedManufactureModal />
      <ToolSynthesisModal />
      <PetFusionModal />
      <StyledTimeLeftGroup>
        <TimeLeft />
        <PizzaActivityWindow />
      </StyledTimeLeftGroup>
    </>
  );
};

const StyledTimeLeftGroup = styled.div`
  position: fixed;
  top: 10%;
  left: 0;
  right: 0;
  margin: 0 auto;
  overflow: hidden;
  display: flex;
  justify-content: center;
  pointer-events: none;
`;

/**
 * @description 应为这个组件基本上不依赖父组件的props更新，组件内的更新由redux或则自身的状态决定
 * 所以可以用memo包裹一下，防止父组件重新渲染的时候，带动该组件重新重新渲染
 */
export default React.memo(GameApiWindow);
