import Dialog from '@/commons/Dialog';
import { forwardRef, useEffect, useMemo, useRef } from 'react';
import Image from 'next/image';
import SynthesisContent from './components/SynthesisContent';
import SynthesisRecipe from './components/SynthesisRecipe';
import useSynthesis, { RecipesWithStatusItemType, SynthesisType } from './hooks/useSynthesis';
import LocalLoading from '@/components/LoadingContent';
import {
  PetBedManufactureTitle,
  PetBedManufactureTitleContainer,
  StyledHelpIcon,
  StyledModalContent,
} from './styles';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { updateModalState } from '@/store/modal';
import { SpriteSvg } from '@/components/SvgWrapper';
import { updateCache } from '@/store/cache';

export interface SynthesisSystemRef {
  open: (confirmCallback: () => void) => void;
}

interface ModalProps {
  onClose?: () => void;
  componentType?: SynthesisType;
}

interface IRecipeItem {
  synthesisTag: string;
  itemTag: string;
  itemName?: string;
  itemIcon?: string;
  active?: boolean;
  maxDurability: number;
  quality: number;
}

const SynthesisSystem = forwardRef<SynthesisSystemRef, ModalProps>(
  ({ onClose = () => false, componentType = 'tool' }, _ref) => {
    // const [isOpen, setIsOpen] = useState(false);
    const confirmCallbackRef = useRef<() => void>();
    const { userBasicInfo } = useAppSelector((state) => state.AppReducer);
    const { toolSynthesisModalConfig, petSynthesisModalConfig } = useAppSelector(
      (state) => state.ModalReducer
    );

    const isFirstOpenCraftPet = useAppSelector((state) => state.CacheReducer.isFirstOpenCraftPet);

    const dispatch = useAppDispatch();

    const {
      loading,
      synthesizing,
      recipes,
      recipesWithStatus,
      selectedRecipe,
      setSelectedRecipe,
      fetchSynthesisList,
      synthesize,
      canSynthesize,
      reset,
    } = useSynthesis(componentType);

    // useImperativeHandle(ref, () => ({
    //   open: (confirmCallback: () => void) => {
    //     // 打开弹窗前获取最新数据
    //     confirmCallbackRef.current = confirmCallback;
    //     fetchSynthesisList();
    //     setIsOpen(true);
    //   },

    const isOpen = useMemo(() => {
      return componentType === 'petBed'
        ? petSynthesisModalConfig.isOpen
        : toolSynthesisModalConfig.isOpen;
    }, [componentType, petSynthesisModalConfig, toolSynthesisModalConfig]);

    useEffect(() => {
      if (componentType === 'petBed') {
        if (petSynthesisModalConfig.isOpen) {
          confirmCallbackRef.current = petSynthesisModalConfig.confirmCallback;
          fetchSynthesisList();
        }
      } else {
        if (toolSynthesisModalConfig.isOpen) {
          confirmCallbackRef.current = toolSynthesisModalConfig.confirmCallback;
          fetchSynthesisList();
        }
      }
    }, [componentType, petSynthesisModalConfig, toolSynthesisModalConfig, fetchSynthesisList]);

    // 处理选择配方
    const handleSelectRecipe = (recipe: IRecipeItem) => {
      // 查找完整的配方信息
      const fullRecipe = recipes.find((item) => item.synthesisTag === recipe.synthesisTag);
      if (fullRecipe) {
        setSelectedRecipe(fullRecipe);
      }
    };

    const handleClose = () => {
      onClose();
      if (componentType === 'tool') {
        dispatch(
          updateModalState({
            toolSynthesisModalConfig: {
              isOpen: false,
            },
          })
        );
      } else {
        dispatch(
          updateModalState({
            petSynthesisModalConfig: {
              isOpen: false,
            },
          })
        );
      }
      // 清空弹窗的数据
      reset();
    };

    const onVerify = async () => {
      try {
        if (selectedRecipe) {
          // 执行合成
          const res = await synthesize(selectedRecipe.synthesisTag);

          if (res) {
            // 成功后关闭窗口
            if (confirmCallbackRef.current) {
              confirmCallbackRef.current();
            }
            handleClose();
          }
        }
      } catch (error) {
        console.error(error);
      }
    };

    // 执行合成操作
    const handleConfirm = async (token?: string) => {
      if (userBasicInfo?.refreshFlag) {
        await onVerify();
        return;
      }

      if (!selectedRecipe) {
        // toast.error("请先选择一个配方");
        return;
      }

      // 合成前检查材料是否足够
      if (!canSynthesize(selectedRecipe)) {
        // toast.error("材料不足，无法合成");
        return;
      }

      // 执行合成
      const res = await synthesize(selectedRecipe.synthesisTag, token, () => {
        // setIsModalOpen(true);
      });

      if (res) {
        // 成功后关闭窗口
        if (confirmCallbackRef.current) {
          confirmCallbackRef.current();
        }
        handleClose();
      }
    };

    const titleEl = useMemo(() => {
      return componentType === 'tool' ? (
        <Image
          src="/image/title-bg.png"
          alt="synthesis"
          width={358}
          height={84}
          draggable={false}
          style={{
            width: '22.375rem',
            height: '5.25rem',
          }}
        />
      ) : (
        <PetBedManufactureTitleContainer>
          <PetBedManufactureTitle data-text={'Manufacture'}>Manufacture</PetBedManufactureTitle>
        </PetBedManufactureTitleContainer>
      );
    }, [componentType]);

    const openPetShedDesc = () => {
      dispatch(updateModalState({ petBedDescModalConfig: { isOpen: true } }));
    };

    useEffect(() => {
      if (componentType === 'petBed') {
        if (petSynthesisModalConfig.isOpen && isFirstOpenCraftPet) {
          dispatch(
            updateModalState({
              petBedDescModalConfig: {
                isOpen: true,
                confirmCallback: () => {
                  dispatch(updateCache({ isFirstOpenCraftPet: false }));
                },
              },
            })
          );
        }
      }
    }, [componentType, isFirstOpenCraftPet, petSynthesisModalConfig]);

    return (
      <>
        <Dialog isOpen={isOpen} onClose={handleClose}>
          <div
            style={{
              display: 'flex',
              width: '100%',
              gap: '1.25rem',
              alignItems: 'stretch',
              position: 'relative',
            }}>
            {componentType === 'petBed' && (
              <StyledHelpIcon onClick={openPetShedDesc}>
                <SpriteSvg id="help" />
              </StyledHelpIcon>
            )}
            <StyledModalContent
              $componentType={componentType}
              modalHeight="30.5rem"
              modalWidth="36.25rem"
              confirmText="Confirm"
              onConfirm={handleConfirm}
              confirmLoading={synthesizing}
              maxHeight="27.5rem"
              cancelText="Cancel"
              onCancel={handleClose}
              onClose={handleClose}
              modalBodyPadding="3.75rem 2.625rem 0.625rem"
              footerStyle={{
                justifyContent: 'space-evenly',
                gap: '0rem',
                padding: '1rem 3.75rem',
              }}
              buttonStyle={{
                width: '12.5rem',
              }}
              modalHeaderStyle={{
                top: '-2.9875rem',
                zIndex: 1,
                pointerEvents: componentType === 'petBed' ? 'none' : 'auto',
              }}
              modalCloseBtnStyle={{
                right: '2.25rem',
                top: '35%',
              }}
              title={titleEl}
              confirmDisabled={!selectedRecipe || !canSynthesize(selectedRecipe) || synthesizing}>
              {loading ? (
                <div
                  style={{
                    textAlign: 'center',
                    width: '100%',
                    height: '18.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <LocalLoading />
                </div>
              ) : selectedRecipe ? (
                <SynthesisContent
                  isBatchSynthesis={false}
                  currentItem={convertToSynthesisContentFormat(selectedRecipe)}
                  componentType={componentType}
                />
              ) : (
                <div
                  style={{
                    textAlign: 'center',
                    width: '100%',
                    height: '18.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  No available formulas
                </div>
              )}
            </StyledModalContent>
            {/* 右侧配方栏，里面是配方列表，采用双栏布局 两两组合 */}
            <SynthesisRecipe
              loading={loading}
              recipeList={convertToRecipeFormat(recipesWithStatus)}
              onSelectRecipe={handleSelectRecipe}
              selectedRecipeId={selectedRecipe?.synthesisTag}
              componentType={componentType}
            />
          </div>
        </Dialog>
        {/* <CapWidgetModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onVerify={onVerify}
          isFooter={false}
        /> */}
      </>
    );
  }
);

// 转换数据格式，适配SynthesisRecipe组件
function convertToRecipeFormat(recipes: RecipesWithStatusItemType[]): IRecipeItem[] {
  return recipes.map((recipe) => ({
    synthesisTag: recipe.synthesisTag,
    itemTag: recipe.itemTag,
    itemName: recipe.itemName || '',
    itemIcon: recipe.itemIcon || '',
    active: recipe.active,
    maxDurability: recipe.maxDurability,
    quality: recipe.quality,
  }));
}

// 转换数据格式，适配SynthesisContent组件
function convertToSynthesisContentFormat(recipe: any) {
  return {
    itemId: recipe.synthesisTag,
    name: recipe.itemName || '',
    description: recipe.description || '',
    icon: recipe.itemIcon || '',
    canSynthesize: recipe.canSynthesize || 0,
    synthetics: recipe.synthesis.map((material: any) => ({
      itemId: material.tag,
      name: material.name || '',
      description: material.description || '',
      icon: material.icon || '',
      currentQuantity: material.currentCount,
      needQuantity: material.count,
      currentDurability: material.currentDurability,
    })),
    maxDurability: recipe.maxDurability,
    quality: recipe.quality,
  };
}

SynthesisSystem.displayName = 'SynthesisSystem';

export const withComponentType = (componentType: SynthesisType) => {
  const Wrapped = forwardRef<SynthesisSystemRef, Omit<ModalProps, 'componentType'>>(
    (props, ref) => {
      return <SynthesisSystem ref={ref} componentType={componentType} {...props} />;
    }
  );

  Wrapped.displayName = `withComponentType(${componentType})`;
  return Wrapped;
};

export const PetBedManufactureModal = withComponentType('petBed');

export const ToolSynthesisModal = withComponentType('tool');

export default SynthesisSystem;
