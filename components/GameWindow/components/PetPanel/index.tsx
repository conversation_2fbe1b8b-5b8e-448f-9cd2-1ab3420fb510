import { useEffect, useMemo, useRef } from 'react';
import PetDetailPanel from './components/PetDetailPanel';
import PetStatusPanel from './components/PetStatusPanel';
import { PetPanelContextProvider, usePetPanelContextDispatch, usePetPanelContextSelector } from './context';
import { PetDetailContainer, PetStatusPanelContainer } from './style';
import useClickAway from '@/hooks/useClickAway';
import { AnimatePresence, useAnimate } from 'framer-motion';
import { useAppSelector } from '@/hooks/useStore';
import { PetStatus } from '@/constant/enum';
import RenameModal from './components/RenameModal';
import PetReleaseModal from './components/PetReleaseModal';
import useAutoRefreshPetList from './useAutoRefreshPetList';
import PetFeedModal from './components/PetFeedModal';
import styled from 'styled-components';
import { getPetFoodList } from '@/server';

const PetPanel = () => {
  const statusRef = useRef<HTMLDivElement>(null);
  const detailRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const contextDispatch = usePetPanelContextDispatch();
  const [, statusAnimate] = useAnimate();
  useAutoRefreshPetList();

  useClickAway(() => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isExpand: false,
      },
    });
    setTimeout(() => {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: '',
        },
      });
    }, 300);
  }, [statusRef, detailRef, containerRef]);

  const isExpand = usePetPanelContextSelector((state) => state.isExpand);
  const isPetFeedModalOpen = usePetPanelContextSelector((state) => state.isPetFeedModalOpen);
  const selectedPetId = usePetPanelContextSelector((state) => state.selectedPetId);
  const selectedPetInfo = usePetPanelContextSelector((state) => state.selectedPetInfo);
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const address = useAppSelector((state) => state.AppReducer.btcAddress);
  const fetchFoodList = async () => {
    if (address) {
      try {
        const res = await getPetFoodList();
        if (res.data.code === 1) {
          const data = res.data.data;
          contextDispatch({
            type: 'UPDATE',
            payload: {
              petFoodConfigList: data,
            },
          });
        }
      } catch (error) {}
    }
  };

  useEffect(() => {
    if (selectedPetId && selectedPetInfo && petList.length > 0) {
      const petItem = petList.find((item) => item._id === selectedPetId);
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetInfo: petItem || {},
        },
      });
    }
  }, [petList]);

  useEffect(() => {
    contextDispatch({ type: 'RESET' });
    if (address) {
      fetchFoodList();
    }
  }, [address]);

  useEffect(() => {
    if (statusRef.current) {
      if (isExpand) {
        statusAnimate(
          statusRef.current,
          {
            right: '32.125rem',
          },
          {
            type: 'spring',
            damping: 20,
            stiffness: 100,
            duration: 0.3,
            ease: 'linear',
          }
        );
      } else {
        statusAnimate(
          statusRef.current,
          {
            right: '4rem',
          },
          {
            type: 'spring',
            stiffness: 100,
            damping: 20,
            duration: 0.3,
            ease: 'linear',
          }
        );
      }
    }
  }, [isExpand]);

  const currentSummonList = useMemo(() => {
    return petList.filter((item) => item.petStatus !== PetStatus.REST);
  }, [petList]);

  return (
    <StyledPetPanelContainer ref={containerRef}>
      <AnimatePresence>
        {currentSummonList.length > 0 && (
          <PetStatusPanelContainer
            ref={statusRef}
            key="petStatusAnimate"
            initial={{
              opacity: 0,
              right: '-10rem',
              transition: {
                duration: 0.3,
                ease: 'easeIn',
              },
            }}
            animate={{
              opacity: 1,
              right: '4rem',
              transition: {
                duration: 0.3,
                ease: 'easeIn',
              },
            }}
            exit={{
              opacity: 0,
              right: '-10rem',
              transition: {
                duration: 0.3,
                ease: 'easeIn',
              },
            }}
            style={isPetFeedModalOpen ? { zIndex: 13 } : {}}>
            <PetStatusPanel />
          </PetStatusPanelContainer>
        )}
        {isExpand && (
          <PetDetailContainer
            ref={detailRef}
            key="petDetailAnimate"
            initial={{
              opacity: 0,
              x: '100%',
              transition: {
                duration: 0.3,
                ease: 'easeIn',
              },
            }}
            animate={{
              opacity: 1,
              x: '0',
              transition: {
                x: {
                  duration: 0.525,
                  ease: 'easeOut',
                },
                opacity: {
                  duration: 0.3,
                  ease: 'easeIn',
                },
              },
            }}
            exit={{
              opacity: 0,
              x: '100%',
              transition: {
                duration: 0.3,
                ease: 'easeOut',
              },
            }}>
            <PetDetailPanel />
          </PetDetailContainer>
        )}
      </AnimatePresence>
      {isExpand && <RenameModal portalContainerRef={detailRef} />}
      {isExpand && <PetReleaseModal portalContainerRef={detailRef} />}
      {currentSummonList.length > 0 && <PetFeedModal portalContainerRef={containerRef} />}
    </StyledPetPanelContainer>
  );
};

const StyledPetPanelContainer = styled.div``;

const PetPanelWithContext = () => {
  return (
    <PetPanelContextProvider>
      <PetPanel />
    </PetPanelContextProvider>
  );
};

export default PetPanelWithContext;
