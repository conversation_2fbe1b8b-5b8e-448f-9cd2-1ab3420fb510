import { IBagPetList } from '@/constant/type';
import { createTemplateContext } from '@/utils/createTemplateContext';

interface PetPanelContext {
  isExpand: boolean;
  selectedPetId: string;
  selectedPetInfo: Partial<IBagPetList>;
  isChangePetStatusLoading: boolean;
  isRenameModalOpen: boolean;
  isReleaseModalOpen: boolean;
  isPetFeedModalOpen: boolean;
  petFoodConfigList: { tag: string; stamina: number }[];
}

const initState: PetPanelContext = {
  isExpand: false,
  selectedPetId: '',
  selectedPetInfo: {},
  isChangePetStatusLoading: false,
  isRenameModalOpen: false,
  isReleaseModalOpen: false,
  isPetFeedModalOpen: false,
  petFoodConfigList: [],
};

const {
  TemplateContextProvider: PetPanelContextProvider,
  useContextDispatch: usePetPanelContextDispatch,
  useContextSelector: usePetPanelContextSelector,
} = createTemplateContext<PetPanelContext>(initState);
PetPanelContextProvider.displayName = 'PetPanelContextProvider';
export { PetPanelContextProvider, usePetPanelContextDispatch, usePetPanelContextSelector };
