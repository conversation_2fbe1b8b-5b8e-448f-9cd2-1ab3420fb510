import useBagInventory from '@/hooks/useBagInventory';
import { getUTC8MidnightTimestamp } from '@/utils/dayjsHelper';
import { useEffect, useRef } from 'react';

/**
 * @description UTC 0点自动重新获取petlist数据
 */
export default function useAutoRefreshPetList(cb?: () => void) {
  const timerRef = useRef<NodeJS.Timer>();
  const { getPetListData } = useBagInventory(false);

  useEffect(() => {
    timerRef.current = setInterval(() => {
      let diffTimestamp = getUTC8MidnightTimestamp();
      if (diffTimestamp <= 1000) {
        diffTimestamp = 0;
        getPetListData();
        cb?.();
      }
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);
}
