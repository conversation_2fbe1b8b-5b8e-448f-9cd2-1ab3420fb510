import styled from 'styled-components';
import { motion } from 'framer-motion';

export const PetInfoSection = styled.div`
  display: flex;
  width: 25.5rem;
  padding: 0.5rem;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 1rem;
  border-radius: 1.5rem;
  background: #f7e7cd;
  backdrop-filter: blur(0.25rem);
`;

export const PetInfoSectionTitle = styled.p`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  margin: 0;
  span {
    color: #140f08;
    text-align: center;
    font-family: 'JetBrains Mono';
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: -0.03rem;
    text-transform: capitalize;
  }
  &::after,
  &::before {
    display: block;
    content: '';
    width: 0.375rem;
    height: 0.375rem;
    transform: rotate(45deg);
    flex-shrink: 0;
    border-radius: 0.0625rem;
    background: #ff8316;
  }
`;

export const PetInfoSectionInfoBox = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.375rem 1rem;
  flex-wrap: wrap;
  width: 100%;
`;

export const PetInfoItem = styled.span`
  display: flex;
  height: 1.75rem;
  padding: 0 0.625rem;
  align-items: center;
  gap: 0.25rem;
  border-radius: 0.5rem;
  background: #fbf4e8;
  font-family: Inter;
  font-size: 0.875rem;
  font-style: normal;
  line-height: 100%;
  width: calc(50% - 0.5rem);
  flex-shrink: 0;
  flex-grow: 0;
`;

export const PetInfoItemLabel = styled.span`
  color: #a58061;
  font-weight: 400;
`;
export const PetInfoItemValue = styled.span`
  color: #140f08;
  font-weight: 700;
`;
export const PetInfoItemValueRate = styled.span`
  font-weight: 700;
  margin-left: auto;
  & > span:first-of-type {
    color: #140f08;
  }
  & > span:last-of-type {
    color: #a58061;
  }
`;

export const BtnWrapper = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  margin-top: auto;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
`;

export const PetInfoSectionMainBox = styled.div`
  display: flex;
  width: 25.5rem;
  height: 11.75rem;
  padding: 0.5rem 1rem;
  align-items: flex-start;
  flex-wrap: nowrap;
  gap: 0.5rem;
  border-radius: 1.5rem;
  background: #f7e7cd;
  backdrop-filter: blur(0.25rem);
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
`;

export const ImageBox = styled.div<{ $bgSrc: string }>`
  width: 10rem;
  height: 100%;
  position: relative;
  background: url(${({ $bgSrc }) => $bgSrc});
  background-size: contain;
  background-position: center bottom;
  background-repeat: no-repeat;
  position: relative;
`;

export const StatusTag = styled.span<{ $borderColor: string; $color: string }>`
  min-width: 6.5rem;
  height: 1.75rem;
  flex-shrink: 0;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);

  & > span:first-of-type {
    width: 100%;
    color: ${({ $borderColor }) => $borderColor};
    filter: drop-shadow(0.16175rem 0.16175rem 0.6470625rem rgba(0, 0, 0, 0.4));
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
  & > span:last-of-type {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 1rem;
    & > span {
      position: relative;
      color: ${({ $color }) => $color};
      font-family: Inter;
      font-size: 1rem;
      font-style: italic;
      font-weight: 900;
      line-height: 100%;
      z-index: 1;
      &::before {
        display: flex;
        align-items: center;
        color: ${({ $borderColor }) => $borderColor};
        justify-content: center;
        content: attr(data-text);
        position: absolute;
        -webkit-text-stroke: 0.15rem currentColor;
        z-index: -1;
        left: 0;
      }
    }
  }
`;

export const RarityTag = styled.span<{ $color: string }>`
  display: inline-flex;
  padding: 0.25rem 0.375rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 0.5rem;
  color: #fff;
  background: ${({ $color }) => $color};
  position: absolute;
  top: 0;
  left: 0;
  text-transform: capitalize;
`;

export const RightInfoBox = styled.div`
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 1rem;
`;

export const PetPropertyNameWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
`;

export const PetNameBox = styled.div`
  display: flex;
  padding: 0.25rem 0.375rem 0.25rem 0.75rem;
  gap: 0.625rem;
  flex: 1;
  border-radius: 0.5rem;
  background: #fbf4e8;
  flex-wrap: nowrap;
  height: 2rem;
  align-items: center;

  & > span:first-of-type {
    color: #140f08;
    font-family: 'JetBrains Mono';
    font-size: 1rem;
    font-style: normal;
    font-weight: 500;
    letter-spacing: -0.04rem;
    user-select: text;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 9.625rem;
    text-align: left;
  }
`;

export const SpMpContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  align-self: stretch;
  & > ${PetInfoItem} {
    width: 100%;
  }
`;

export const StatusBarContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  align-self: stretch;
  width: 100%;
`;

export const StatusBarItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  gap: 0.25rem;
`;

export const StatusBarInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1 0 0;
  height: 1.75rem;
  width: 100%;
  align-items: center;
  & > span {
    height: 1.375rem;
  }
  & > span:first-of-type {
    color: #fff;
    font-family: Inter;
    font-size: 1rem;
    font-style: normal;
    font-weight: 900;
    position: relative;
    z-index: 1;
    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.125rem #4b2800;
      z-index: -1;
      left: 0;
    }
  }
  & > span:last-of-type {
    color: #a58061;
    margin-left: auto;
    font-family: 'JetBrains Mono';
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: nowrap;
  }
`;

export const StyledPreviewStamina = styled(motion.div)`
  display: flex;
  padding: 0.25rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 0.5rem;
  border: 0.0625rem solid #140f08;
  background: #fff;
  height: 100%;
`;

export const StatusBar = styled.div<{
  $percent: number;
  $previewPercent: number;
  $barColor: string;
}>`
  width: 100%;
  height: 0.5rem;
  background-color: #cabfab;
  border-radius: 0.25rem;
  position: relative;
  box-sizing: border-box;

  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: ${({ $previewPercent }) =>
      $previewPercent ? `max(${$previewPercent}%, 0.1875rem)` : 0};
    height: 0.5rem;
    box-sizing: border-box;
    border: 0.0625rem solid #140f08;
    border-radius: 0.25rem;
    max-width: 100%;
    background-color: #ffffff;
    transition:
      width 0.5s ease,
      background 0.5s ease;
  }
  &::after {
    content: '';
    display: block;
    position: absolute;
    border-radius: 0.25rem;
    left: 0;
    top: 0;
    width: ${({ $percent }) => ($percent ? `max(${$percent}%, 0.1875rem)` : 0)};
    height: 0.5rem;
    box-sizing: border-box;
    border: 0.0625rem solid #140f08;
    max-width: 100%;

    background-color: ${({ $barColor }) => $barColor};
    transition:
      width 0.5s ease,
      background 0.5s ease;
  }
`;

export const StyledCommandListContainer = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 0.375rem;
  width: 100%;
  height: 3rem;
  position: relative;
`;
