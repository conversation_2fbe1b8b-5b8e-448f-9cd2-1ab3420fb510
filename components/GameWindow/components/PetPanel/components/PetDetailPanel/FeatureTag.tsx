import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { memo } from 'react';
import styled from 'styled-components';
import { PetInfoItem } from './style';

function simpleToRoman(num: number) {
  const roman = ['', 'I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X'];
  return roman[num] || 'Number out of range (1-10)';
}
const FeatureTag = memo(
  ({
    feature,
    featureLevel,
    className,
  }: {
    feature: string;
    featureLevel: number;
    className?: string;
  }) => {
    const romanLevel = simpleToRoman(featureLevel);
    const safetyFeature = (feature || '').toLowerCase();

    const iconMap: Record<string, string> = {
      axe: 'featureAxe',
      pickaxe: 'featurePickAxe',
      fishingpole: 'featureFish',
    };

    return (
      <StyledFeatureTagContainer className={className}>
        <StyledSvgWrapper>
          <SpriteSvg id={iconMap[safetyFeature]} />
        </StyledSvgWrapper>
        <span>{feature}</span>
        <span>{romanLevel}</span>
      </StyledFeatureTagContainer>
    );
  }
);

FeatureTag.displayName = 'FeatureTag';

export default FeatureTag;

const StyledSvgWrapper = styled(SvgWrapper)`
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
  color: #a58061;
`;

const StyledFeatureTagContainer = styled(PetInfoItem)`
  border: 0.0625rem solid #a58061;
  background: #fbf4e8;
  gap: 0;

  & > span {
    font-family: 'JetBrains Mono';
    font-style: normal;
    line-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  & > span:nth-of-type(2) {
    color: #a58061;
    font-family: 'JetBrains Mono';
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    margin-left: 0.25rem;
    margin-right: auto;
  }
  & > span:nth-of-type(3) {
    color: #ff8316;
    font-size: 1.25rem;
    font-weight: 700;
  }
`;
