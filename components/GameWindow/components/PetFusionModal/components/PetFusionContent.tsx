import styled from 'styled-components';
import Image from 'next/image';
import { CommonItemBox } from '../../PlacePetBedModal';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { usePetFusionContextSelector } from '../context';
import { ItemRender } from '@/components/EditAvatarPage/BagModal/PetList';
import { motion, AnimatePresence } from 'framer-motion';

const PetFusionContent = () => {
  const selectedPetSlot = usePetFusionContextSelector((state) => state.selectedPetSlot);

  return (
    <StyledFusionContainer>
      <FusionPreviewBox>
        <Image
          src="./image/pet/pet_fusion_station.png"
          width={132}
          height={83}
          alt="fusion img"
          draggable={false}
        />
        <Image
          src="./image/pet/icon_pet_synthesis.svg"
          width={76}
          height={95}
          alt="fusion img"
          draggable={false}
        />
      </FusionPreviewBox>
      <SelectedContainer>
        <StyledDesc>Please select 2 pets that you want to merge.</StyledDesc>
        <StyledSelectedBox>
          <StyledCommonItemBox $active={false}>
            <AnimatePresence>
              {selectedPetSlot.first && (
                <StyledItemRenderWrapper
                  key={selectedPetSlot.first?._id + 'firstSelectedFusionAnimate'}
                  initial={{ opacity: 0, scale: 0.3 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.7 }}
                  transition={{
                    duration: 0.4,
                    ease: 'easeOut',
                    scale: {
                      type: 'spring',
                      stiffness: 300,
                      damping: 10,
                    },
                  }}>
                  <ItemRender
                    url={selectedPetSlot.first.bagConfigInfo.icon}
                    color={selectedPetSlot.first.bagConfigInfo.iconColor}
                  />
                </StyledItemRenderWrapper>
              )}
            </AnimatePresence>
          </StyledCommonItemBox>

          <StyledPlusWrapper>
            <SpriteSvg id="plus" />
          </StyledPlusWrapper>
          <StyledCommonItemBox $active={false}>
            <AnimatePresence>
              {selectedPetSlot.second && (
                <StyledItemRenderWrapper
                  key={selectedPetSlot.second?._id + 'secondSelectedFusionAnimate'}
                  initial={{ opacity: 0, scale: 0.3 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.7 }}
                  transition={{
                    duration: 0.4,
                    ease: 'easeOut',
                    scale: {
                      type: 'spring',
                      stiffness: 300,
                      damping: 10,
                    },
                  }}>
                  <ItemRender
                    url={selectedPetSlot.second.bagConfigInfo.icon}
                    color={selectedPetSlot.second.bagConfigInfo.iconColor}
                  />
                </StyledItemRenderWrapper>
              )}
            </AnimatePresence>
          </StyledCommonItemBox>
        </StyledSelectedBox>
      </SelectedContainer>
    </StyledFusionContainer>
  );
};

const StyledItemRenderWrapper = styled(motion.div)`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const StyledCommonItemBox = styled(CommonItemBox)`
  width: 7.5rem;
  height: 7.5rem;
  flex-shrink: 0;
`;

const StyledPlusWrapper = styled(SvgWrapper)`
  width: 2.125rem;
  height: 2.125rem;
`;

const StyledFusionContainer = styled.div`
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
`;

const FusionPreviewBox = styled.div`
  width: 13rem;
  height: 11rem;
  position: relative;
  & > img:first-of-type {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 8.25rem;
    height: 5.1875rem;
    flex-shrink: 0;
    aspect-ratio: 132/83;
  }
  & > img:last-of-type {
    position: absolute;
    width: 4.75rem;
    height: 5.9375rem;
    flex-shrink: 0;
    top: 1rem;
    left: 0;
    right: 0;
    margin: 0 auto;
    aspect-ratio: 76/95;
  }
`;

const SelectedContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  flex: 1;
  gap: 0.5rem;
`;

const StyledDesc = styled.p`
  margin: 0;
  display: flex;
  height: 1rem;
  flex-direction: column;
  justify-content: center;
  align-self: stretch;
  color: #140f08;
  text-align: center;
  font-family: 'JetBrains Mono';
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 1.2rem */
  letter-spacing: -0.04rem;
  white-space: nowrap;
`;

const StyledSelectedBox = styled.div`
  display: flex;
  height: 9.5rem;
  padding: 1rem 1.5rem;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 2rem;
  background: #f7e7cd;
  box-shadow: 0 0 0.5rem 0 rgba(0, 0, 0, 0.25) inset;
`;

export default PetFusionContent;
