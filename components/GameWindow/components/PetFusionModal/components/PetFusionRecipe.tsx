import React, { useRef, useState } from 'react';
import styled from 'styled-components';
import StaggeredAnimation from '@/commons/StaggeredAnimation';
import LocalLoading from '@/components/LoadingContent';
import { useAppSelector } from '@/hooks/useStore';
import { CommonItemBox } from '../../PlacePetBedModal';
import { ItemRender } from '@/components/EditAvatarPage/BagModal/PetList';
import PetInfoPanel from '@/components/EditAvatarPage/BagModal/PetList/PetInfoPanel';
import Tooltip, { TooltipRef } from 'rc-tooltip';
import { usePetFusionContextDispatch, usePetFusionContextSelector } from '../context';
import { IBagPetList } from '@/constant/type';
import { PetStatus, Rarity } from '@/constant/enum';
import { LargeButton } from '@/components/Basic/Button';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { motion, AnimatePresence } from 'framer-motion';
import { TooltipProps } from 'rc-tooltip/lib/Tooltip';

const PetFusionRecipeContainer = styled.div`
  height: auto;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 22rem;
  border: 0.5rem solid #efbd73;
  padding: 2rem 1.125rem 2rem 2.5rem;
  border-radius: 3rem;
  height: 36.5rem;
  background: #fff2e2;
  * {
    box-sizing: border-box;
  }
`;

const RecipeList = styled.div`
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: center;
  gap: 1rem;
  padding-right: 0.5rem;
  flex-wrap: wrap;
  ::-webkit-scrollbar {
    background: transparent !important;
    cursor: pointer;
  }
  ::-webkit-scrollbar-track {
    background-color: transparent !important;
    cursor: pointer;
  }
  ::-webkit-scrollbar-thumb {
    cursor: pointer;
    background: #c69f7e !important;
  }
`;

const StyledStaggeredAnimation = styled(StaggeredAnimation)`
  width: calc(50% - 0.5rem);
  aspect-ratio: 1/1;
  flex-shrink: 0;
  position: relative;
`;
const StyledCommonItemBox = styled(CommonItemBox)`
  width: 100%;
  height: 100%;
`;

const StyledSvgWrapper = styled(SvgWrapper)`
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  position: absolute;
  top: 0.4375rem;
  left: 0.625rem;
  z-index: 9;
`;

const StyledSelectedIconWrapper = styled(motion.div)`
  width: 3rem;
  height: 3rem;
  z-index: 9;
  position: absolute;
  right: 0;
  bottom: 0;
`;

const StyledSelectedIcon = styled(SvgWrapper)`
  width: 3rem;
  height: 3rem;
  background: #ff8316;
  border-radius: 50%;
  & > svg {
    width: 1.8125rem;
    height: 1.5625rem;
  }
`;

interface IPetFusionRecipeProps {
  loading?: boolean;
}

const PetFusionRecipe = ({ loading }: IPetFusionRecipeProps) => {
  const petList = useAppSelector((state) => state.GameReducer.petList).filter(
    (item) => item.quality !== Rarity.MYTHIC
  );
  const selectedPetSlot = usePetFusionContextSelector((state) => state.selectedPetSlot);
  const isBindSelected = !!(
    selectedPetSlot.first?.bindingFlag || selectedPetSlot.second?.bindingFlag
  );

  const isAnySelected = !!(selectedPetSlot.first || selectedPetSlot.second);

  return (
    <PetFusionRecipeContainer>
      {loading ? (
        <div
          style={{
            textAlign: 'center',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <LocalLoading />
        </div>
      ) : petList.length > 0 ? (
        <RecipeList>
          {petList
            .toSorted((a, b) => {
              if (a.petStatus === PetStatus.REST && b.petStatus !== PetStatus.REST) return -1;
              if (a.petStatus !== PetStatus.REST && b.petStatus === PetStatus.REST) return 1;

              return 0;
            })
            .map((petItem, index) => {
              const isRest = petItem.petStatus === PetStatus.REST;
              const disabled = isAnySelected
                ? isBindSelected
                  ? !petItem.bindingFlag
                  : petItem.bindingFlag
                : false;

              return (
                <StyledStaggeredAnimation
                  index={index}
                  initialScale={0.7}
                  duration={0.5}
                  bounceEffect={true}
                  key={petItem._id}
                  staggerDelay={0.1}>
                  <PetEggPickItem petItem={petItem} index={index} disabled={!isRest || disabled} />
                  {petItem.bindingFlag && (
                    <StyledSvgWrapper>
                      <SpriteSvg id="bind" />
                    </StyledSvgWrapper>
                  )}
                </StyledStaggeredAnimation>
              );
            })}
        </RecipeList>
      ) : (
        <div
          style={{
            textAlign: 'center',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          No available formulas
        </div>
      )}
    </PetFusionRecipeContainer>
  );
};

interface IPetEggPickItemProps {
  petItem: IBagPetList;
  index: number;
  disabled: boolean;
}

const PetEggPickItem = ({ petItem, index, disabled }: IPetEggPickItemProps) => {
  const [active, setActive] = useState<boolean>(false);
  const petFusionModalConfig = useAppSelector((state) => state.ModalReducer.petFusionModalConfig);
  const isOpen = petFusionModalConfig?.isOpen;
  const selectedPetSlot = usePetFusionContextSelector((state) => state.selectedPetSlot);
  const isSelected =
    (selectedPetSlot.first && selectedPetSlot.first._id === petItem._id) ||
    (selectedPetSlot.second && selectedPetSlot.second._id === petItem._id);
  const defaultAlign = {
    points: ['tl', 'cl'],
    offset: ['20.18%', '0'],
  } as TooltipProps['align'];

  const [adjustedAlign, setAdjustedAlign] = useState<TooltipProps['align']>(defaultAlign);

  const triggerRef = useRef<TooltipRef>(null);

  const handleBeforeVisibleChange = (visible: boolean) => {
    if (visible && triggerRef.current) {
      const popupElement = triggerRef.current.popupElement;

      const handleAdjustAlign = (popupElement: HTMLDivElement) => {
        requestAnimationFrame(() => {
          const viewportHeight = window.innerHeight;
          const rect = popupElement.getBoundingClientRect();
          const fontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
          if (rect.bottom + fontSize * 5 > viewportHeight) {
            setAdjustedAlign({
              points: ['bl', 'cl'],
              offset: ['20.18%', '-12.5%'],
            });
          }
        });
      };

      if (popupElement) {
        handleAdjustAlign(popupElement);
      } else {
        requestAnimationFrame(() => {
          handleBeforeVisibleChange(visible);
        });
      }
    }
  };

  return (
    <>
      <Tooltip
        zIndex={98}
        trigger={'click'}
        ref={triggerRef}
        overlay={
          !disabled && (
            <PetInfoPanel
              petInfo={petItem}
              ButtonRender={
                <ButtonRender
                  petInfo={petItem}
                  onClick={() => {
                    setActive(false);
                  }}
                />
              }
            />
          )
        }
        onVisibleChange={(visible) => {
          if (disabled) return;
          handleBeforeVisibleChange(visible);
          setActive(!!visible);
        }}
        showArrow={false}
        align={adjustedAlign}
        visible={active}>
        <StyledCommonItemBox
          $active={active || !!isSelected}
          initial={{ opacity: 0, scale: 0.7 }}
          animate={isOpen ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.7 }}
          exit={{ opacity: 0, scale: 0.7 }}
          transition={{
            delay: index * 0.1,
            duration: 0.3,
            ease: 'easeOut',

            scale: {
              type: 'spring',
              stiffness: 400,
              damping: 10,
            },
          }}
          $disabled={disabled}>
          <ItemRender url={petItem.bagConfigInfo.icon} color={petItem.bagConfigInfo.iconColor} />
        </StyledCommonItemBox>
      </Tooltip>
      <AnimatePresence>
        {isSelected && (
          <StyledSelectedIconWrapper
            key={petItem._id + 'confirmRoundAnimate'}
            initial={{ opacity: 0, scale: 0.3 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{
              duration: 0.4,
              ease: 'easeOut',
              scale: {
                type: 'spring',
                stiffness: 400,
                damping: 10,
              },
            }}>
            <StyledSelectedIcon>
              <SpriteSvg id="confirmRound" />
            </StyledSelectedIcon>
          </StyledSelectedIconWrapper>
        )}
      </AnimatePresence>
    </>
  );
};

export default PetFusionRecipe;

interface IButtonRenderProps {
  petInfo: IBagPetList;
  onClick?: () => void;
}

const ButtonRender = ({ petInfo, onClick = () => false }: IButtonRenderProps) => {
  const selectedPetSlot = usePetFusionContextSelector((state) => state.selectedPetSlot);
  const contextDispatch = usePetFusionContextDispatch();

  const isSelected =
    (selectedPetSlot.first && selectedPetSlot.first._id === petInfo._id) ||
    (selectedPetSlot.second && selectedPetSlot.second._id === petInfo._id);

  const disabled =
    selectedPetSlot.first &&
    selectedPetSlot.second &&
    selectedPetSlot.first._id !== petInfo._id &&
    selectedPetSlot.second._id !== petInfo._id;

  const handleClick = () => {
    if (disabled) return;
    onClick();
    if (isSelected) {
      if (selectedPetSlot.first?._id === petInfo._id) {
        contextDispatch({
          type: 'UPDATE',
          payload: {
            selectedPetSlot: {
              ...selectedPetSlot,
              first: undefined,
            },
          },
        });
        return;
      }
      if (selectedPetSlot.second?._id === petInfo._id) {
        contextDispatch({
          type: 'UPDATE',
          payload: {
            selectedPetSlot: {
              ...selectedPetSlot,
              second: undefined,
            },
          },
        });
        return;
      }
      return;
    }

    if (!selectedPetSlot.first) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetSlot: {
            ...selectedPetSlot,
            first: petInfo,
          },
        },
      });
      return;
    }
    if (!selectedPetSlot.second) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetSlot: {
            ...selectedPetSlot,
            second: petInfo,
          },
        },
      });
      return;
    }
  };

  return (
    <LargeButton
      btnType={isSelected ? 'green' : 'primary'}
      onClick={handleClick}
      disabled={disabled}>
      {isSelected ? 'Deselect' : 'Select'}
    </LargeButton>
  );
};
