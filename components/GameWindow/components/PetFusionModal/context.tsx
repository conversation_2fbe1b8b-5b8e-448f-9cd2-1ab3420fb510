import { IBagPetList } from '@/constant/type';
import { createTemplateContext } from '@/utils/createTemplateContext';

interface PetFusionContext {
  selectedPetSlot: { first?: IBagPetList; second?: IBagPetList };
  clickPetItemId: string;
  fusionLoading: boolean;
}

const initState: PetFusionContext = {
  selectedPetSlot: {},
  clickPetItemId: '',
  fusionLoading: false,
};

const {
  TemplateContextProvider: PetFusionContextProvider,
  useContextDispatch: usePetFusionContextDispatch,
  useContextSelector: usePetFusionContextSelector,
} = createTemplateContext<PetFusionContext>(initState);

PetFusionContextProvider.displayName = 'PetFusionContextProvider';

export { PetFusionContextProvider, usePetFusionContextDispatch, usePetFusionContextSelector };
