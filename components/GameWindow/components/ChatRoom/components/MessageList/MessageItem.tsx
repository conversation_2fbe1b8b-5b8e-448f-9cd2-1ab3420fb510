import CopyToClipboard from 'react-copy-to-clipboard';
import {
  MessageItemReplyBox,
  PlayerIdBox,
  PlayerTag,
  ReplyContent,
  ReplyIcon,
  ReplyPlayerIdBox,
} from '../../styles';
import {
  MessageButton,
  MessageButtonBox,
  MessageButtonBoxWrapper,
  MessageContent,
  MessageContentBox,
  MessageItemBox,
  MessageItemContainer,
  PlayerIdContainer,
  TGIcon,
} from './styles';
import { ChatData } from '@/game/TS/Chat/ChatData';
import { CSSProperties, FC, memo, useCallback, useMemo } from 'react';
import { useAppSelector } from '@/hooks/useStore';
import { useChatRoomContextDispatch, useChatRoomContextSelector } from '../../context';
import { GM_BTC_ADDRESS, usePlayerColors } from '@/hooks/usePlayerColors';
import { ChatManager } from '@/game/TS/Chat/ChatManager';
import { SpriteSvg } from '@/components/SvgWrapper';
import { formatPlayerId } from '../../utils';
import ConfettiGroupBox from '../Confetti';
import styled from 'styled-components';
import toast from 'react-hot-toast';

const MessageDeleteButton = ({ handleDelete }: { handleDelete: () => void }) => {
  return (
    <MessageButton
      onClick={(e) => {
        e.preventDefault();
        handleDelete?.();
      }}>
      <span>delete</span>
    </MessageButton>
  );
};

interface MessageItemProps {
  message: ChatData;
  isPrimary?: boolean;
  style?: CSSProperties;
  handleReplyFocus?: () => void;
}

const MessageItem: FC<MessageItemProps> = memo(
  ({ message, isPrimary = true, style, handleReplyFocus = () => false }) => {
    const { btcAddress } = useAppSelector((state) => state.AppReducer);
    const contextDispatch = useChatRoomContextDispatch();
    const { getSpecialAccountProps } = usePlayerColors();
    const selectedTab = useChatRoomContextSelector((state) => state.selectedTab);

    const specialAccountProps = getSpecialAccountProps(message.playerId);
    const isMe = message.playerId === btcAddress;
    const replyChatData = useMemo(() => {
      if (message.replyTo.length > 0) {
        const chatList = ChatManager.getInstance().getChatList(selectedTab);
        if (chatList) {
          const replyMessage = chatList.findChatData(message.replyTo);
          if (replyMessage) {
            return replyMessage;
          }
        }
      }
      return null;
    }, []);

    const handleCopySuccess = () => {
      toast.success('copied!', { duration: 6000 });
    };

    let replyChatPlayerId,
      replyChatIsMe = false,
      replyChatIsGM = false,
      replyPlayerTagText = '';
    if (replyChatData) {
      replyChatPlayerId = replyChatData.playerId;
      replyChatIsMe = replyChatPlayerId === btcAddress;
      replyChatIsGM = !!getSpecialAccountProps(replyChatPlayerId);
      replyPlayerTagText = replyChatIsGM
        ? 'GM'
        : replyChatData.admin
          ? 'AM'
          : replyChatIsMe
            ? 'ME'
            : '';
    }

    const playerTagText = !!specialAccountProps ? 'GM' : message.admin ? 'AM' : isMe ? 'ME' : '';

    const isGM = !!specialAccountProps;
    const isAm = message.admin;

    const currentUserIsGM = GM_BTC_ADDRESS === btcAddress;

    const handleDelete = useCallback(() => {
      if (btcAddress === GM_BTC_ADDRESS) {
        ChatManager.getInstance().sendChatMessageDelete(selectedTab, message.uuid);
      }
    }, [btcAddress, message.uuid, selectedTab]);

    const handleReply = useCallback(() => {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          replyChatData: message,
        },
      });
      handleReplyFocus();
    }, [message]);

    const item = useMemo(() => {
      return (
        <MessageItemBox $isMe={isMe && isPrimary} $isPrimary={isPrimary}>
          <MessageContentBox $isMe={isMe} $isPrimary={isPrimary}>
            {replyChatData && (
              <MessageItemReplyBox>
                <ReplyIcon />
                {currentUserIsGM ? (
                  <CopyToClipboard text={replyChatData.playerId} onCopy={handleCopySuccess}>
                    <ReplyPlayerIdBox
                      $isMe={isMe}
                      $isPrimary={isPrimary}
                      $withGMCopyStyle={currentUserIsGM}>
                      {formatPlayerId(replyChatData.playerId)}
                    </ReplyPlayerIdBox>
                  </CopyToClipboard>
                ) : (
                  <ReplyPlayerIdBox $isMe={isMe} $isPrimary={isPrimary}>
                    {formatPlayerId(replyChatData.playerId)}
                  </ReplyPlayerIdBox>
                )}
                {!!replyPlayerTagText && (
                  <PlayerTag
                    $isMe={replyChatIsMe}
                    $isGM={replyChatIsGM}
                    $isAM={replyChatData.admin}
                    $isPrimary={isPrimary}
                    data-text={replyPlayerTagText}>
                    {replyPlayerTagText}
                  </PlayerTag>
                )}
                <ReplyContent>: {replyChatData.content.trim()}</ReplyContent>
              </MessageItemReplyBox>
            )}
            {isPrimary ? (
              <CopyToClipboard text={message.content.trim()} onCopy={handleCopySuccess}>
                <MessageContent
                  $isGMorAm={!!specialAccountProps || message.admin}
                  $isPrimary={isPrimary}>
                  {message.content.trim()}
                </MessageContent>
              </CopyToClipboard>
            ) : (
              <MessageContent
                $isGMorAm={!!specialAccountProps || message.admin}
                $isPrimary={isPrimary}>
                {message.content.trim()}
              </MessageContent>
            )}
          </MessageContentBox>
          {isPrimary && (
            <MessageButtonBoxWrapper className="message-button-box">
              <MessageButtonBox>
                <MessageButton onClick={handleReply}>
                  <ReplyIcon />
                  <span>Reply</span>
                </MessageButton>
                {currentUserIsGM && <MessageDeleteButton handleDelete={handleDelete} />}
              </MessageButtonBox>
            </MessageButtonBoxWrapper>
          )}
        </MessageItemBox>
      );
    }, [
      currentUserIsGM,
      handleDelete,
      handleReply,
      isMe,
      isPrimary,
      message.admin,
      message.content,
      replyChatData,
      replyChatIsGM,
      replyChatIsMe,
      replyPlayerTagText,
      specialAccountProps,
    ]);

    return (
      <MessageItemContainer key={message.uuid} $isMe={isMe} $isPrimary={isPrimary} style={style}>
        <PlayerIdContainer $isPrimary={isPrimary}>
          {message.isTg && (
            <TGIcon>
              <SpriteSvg id="telegramIcon2" />
            </TGIcon>
          )}

          {currentUserIsGM ? (
            <CopyToClipboard text={message.playerId} onCopy={handleCopySuccess}>
              <PlayerIdBox $isMe={isMe} $isPrimary={isPrimary} $withGMCopyStyle={currentUserIsGM}>
                {formatPlayerId(message.playerId)}
              </PlayerIdBox>
            </CopyToClipboard>
          ) : (
            <PlayerIdBox $isMe={isMe} $isPrimary={isPrimary}>
              {formatPlayerId(message.playerId)}
            </PlayerIdBox>
          )}
          {!!playerTagText && (
            <PlayerTag
              $isMe={isMe}
              $isAM={isAm}
              $isGM={isGM}
              data-text={playerTagText}
              $isPrimary={isPrimary}>
              {playerTagText}
            </PlayerTag>
          )}
        </PlayerIdContainer>
        {item}
      </MessageItemContainer>
    );
  }
);
MessageItem.displayName = 'MessageItem';

export interface IMessageSystemItemProps {
  message: ChatData;
  className?: string;
  isPrimary?: boolean;
  style?: CSSProperties;
}

const StyledMessageContent = styled(MessageContent)`
  border: 0.0625rem solid #a58061;
  background: #fff;
  padding: 0.75rem 1rem;

  color: #a58061;
  font-family: Inter;
  font-size: 0.875rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
`;

const MessageSystemItem = ({
  message,
  className = '',
  isPrimary = true,
  style,
}: IMessageSystemItemProps) => {
  const { btcAddress } = useAppSelector((state) => state.AppReducer);
  const selectedTab = useChatRoomContextSelector((state) => state.selectedTab);

  const currentUserIsGM = GM_BTC_ADDRESS === btcAddress;

  const handleDelete = () => {
    if (btcAddress === GM_BTC_ADDRESS) {
      ChatManager.getInstance().sendChatMessageDelete(selectedTab, message.uuid);
    }
  };

  return (
    <MessageItemContainer className={className} $isPrimary={isPrimary} style={style}>
      <MessageItemBox $isPrimary={isPrimary} $isSystem={true}>
        <MessageContentBox $isPrimary={isPrimary}>
          <ConfettiGroupBox />
          <StyledMessageContent $isPrimary={isPrimary}>{message.content}</StyledMessageContent>
        </MessageContentBox>
        {isPrimary && currentUserIsGM && (
          <MessageButtonBoxWrapper className="message-button-box">
            <MessageButtonBox>
              <MessageDeleteButton handleDelete={handleDelete} />
            </MessageButtonBox>
          </MessageButtonBoxWrapper>
        )}
      </MessageItemBox>
    </MessageItemContainer>
  );
};

export { MessageItem, MessageSystemItem };
