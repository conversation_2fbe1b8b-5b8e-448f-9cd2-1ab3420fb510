import SvgWrapper from '@/components/SvgWrapper';
import styled, { css } from 'styled-components';
import { VariableSizeList as List } from 'react-window';

export const MessageListContainer = styled(List)<{ $isPrimary: boolean }>`
  width: 100%;
  gap: 0.5rem;
  height: 100%;
  padding-right: 0.5rem;
  /* display: flex;
  flex-direction: column;
  align-items: flex-start;
  overflow-y: scroll; */
  ::-webkit-scrollbar {
    background: transparent !important;
    cursor: pointer;
    /* display: block !important; */
  }

  ::-webkit-scrollbar-track {
    background-color: transparent !important;
    cursor: pointer;
  }
  ::-webkit-scrollbar-thumb {
    cursor: pointer;

    background: #ededed !important;
    &:hover {
      background: #53515d !important;
    }
  }
  ${({ $isPrimary }) =>
    !$isPrimary &&
    css`
      /* ::-webkit-scrollbar {
        display: none !important;
      } */
      ::-webkit-scrollbar-thumb {
        visibility: hidden;
      }
      &:hover::-webkit-scrollbar {
        display: block !important;
      }
      &:hover::-webkit-scrollbar-thumb {
        visibility: visible;
      }
    `}
`;

export const TGIcon = styled(SvgWrapper)`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: #03a4ed;
  border: 0.0625rem solid #140f08;
  svg {
    fill: white;
    stroke: #140f08;
    stroke-width: 1;
  }
`;

export const MessageItemContainer = styled.div<{ $isMe?: boolean; $isPrimary?: boolean }>`
  display: flex;
  padding: 0.5rem;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.375rem;
  align-self: stretch;
  border-radius: 1rem;
  flex: 1;
  ${({ $isMe = false, $isPrimary = true }) =>
    $isMe &&
    $isPrimary &&
    css`
      /* flex-direction: column-reverse; */
      align-items: flex-end;
    `}

  ${({ $isPrimary = true }) =>
    $isPrimary
      ? css`
          &:hover {
            background: #ededed;
            & .message-button-box {
              display: flex;
            }
          }
        `
      : css`
          flex-direction: row;
          flex-wrap: nowrap;
          align-items: flex-end;
          gap: 0.75rem;
        `}
`;

export const PlayerIdContainer = styled.div<{ $isPrimary: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  height: 1.25rem;
  ${({ $isPrimary }) =>
    $isPrimary
      ? css`
          gap: 0.5rem;
        `
      : css`
          gap: 0.125rem;
        `}
`;

export const MessageItemBox = styled.div<{
  $isMe?: boolean;
  $isPrimary: boolean;
  $isSystem?: boolean;
}>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: nowrap;
  width: 100%;
  position: relative;
  ${({ $isMe = false }) =>
    $isMe &&
    css`
      flex-direction: row-reverse;
    `}
  ${({ $isPrimary, $isSystem = false }) =>
    !$isPrimary &&
    $isSystem &&
    css`
      justify-content: center;
    `}
`;
export const MessageContentBox = styled.div<{ $isMe?: boolean; $isPrimary: boolean }>`
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.375rem;
  border-radius: 1rem;
  max-width: 26rem;
  color: #140f08;
  position: relative;
  ${({ $isMe = false, $isPrimary }) =>
    $isMe &&
    $isPrimary &&
    css`
      align-items: flex-end;
    `}
  ${({ $isPrimary, $isMe = false }) =>
    !$isPrimary
      ? $isMe
        ? css`
            color: #ffff27;
          `
        : css`
            color: #fff;
          `
      : css``}
`;

export const MessageContent = styled.p<{ $isGMorAm?: boolean; $isPrimary: boolean }>`
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  margin: 0;
  user-select: text;
  cursor: pointer;
  border: 0.0625rem solid #cabfab;
  font-family: Inter;
  font-size: 1rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  ${({ $isGMorAm = false }) =>
    $isGMorAm
      ? css`
          background: #ffdc9f;
        `
      : css`
          background: #fbf4e8;
        `}

  ${({ $isPrimary }) =>
    $isPrimary
      ? css`
          &:hover {
            text-decoration: underline;
            text-decoration-style: dotted;
          }
        `
      : css`
          padding: 0;
          border: none;
          background: transparent;
          font-size: 1rem;
          line-height: 100%;
        `}
`;

export const MessageButtonBoxWrapper = styled.div`
  width: 4.5rem;
  height: 100%;
  position: relative;
  display: none;
`;

export const MessageButtonBox = styled.div`
  width: 4.5rem;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  display: flex;
  position: absolute;
  text-transform: capitalize;
  gap: 0.5rem;
  bottom: 0.5625rem;
  span {
    text-transform: capitalize;
  }
`;

export const MessageButton = styled.button`
  display: flex;
  width: 4.5rem;
  height: 2rem;
  padding: 0.25rem 0.5rem;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  border-radius: 0.5rem;
  cursor: pointer;
  border: 0.0625rem solid #777;
  background: #fff;
  box-shadow: 0rem 0.25rem 0rem 0rem rgba(0, 0, 0, 0.5);

  color: #140f08;
  text-align: right;
  font-family: Inter;
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  transition:
    transform 0.1s ease,
    box-shadow 0.1s ease,
    background 0.2s ease;
  transform-origin: bottom;
  &:hover {
    background: #fefefe;
  }
  &:active {
    transform: translateY(0.125rem);
    background: #ededed;
    box-shadow: 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.5);
  }
`;
