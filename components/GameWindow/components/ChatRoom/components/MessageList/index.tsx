import { useChatRoomContextDispatch, useChatRoomContextSelector } from '../../context';
import styled from 'styled-components';
import {
  useMemo,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useLayoutEffect,
} from 'react';
import { ChatData } from '@/game/TS/Chat/ChatData';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import AutoSizer from 'react-virtualized-auto-sizer';
import { MessageItem, MessageSystemItem } from './MessageItem';
import { ListChildComponentProps } from 'react-window';
import { MessageListContainer } from './styles';
import DynamicRow, { useDynamicItemSize } from './DynamicRow';

dayjs.extend(isToday);

type ComponentType = 'preview' | 'primary';

const StyledTimestamp = styled.p`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0rem auto;
`;

export interface IMessageListRef {
  scrollToBottom: () => void;
}

interface IMessageListProps {
  componentType?: ComponentType;
  handleReplyFocus?: () => void;
}

const MessageList = forwardRef<IMessageListRef, IMessageListProps>(
  ({ componentType = 'primary', handleReplyFocus = () => false }, ref) => {
    const messages = useChatRoomContextSelector((state) => state.messages);
    const selectedTab = useChatRoomContextSelector((state) => state.selectedTab);
    const adminOnly = useChatRoomContextSelector((state) => state.adminOnly);
    const isLockedScroll = useChatRoomContextSelector((state) => state.isLockedScroll);
    const contextDispatch = useChatRoomContextDispatch();
    const lastMessageCountRef = useRef(0);
    const lastSelectedTab = useRef(0);
    const outerRef = useRef<HTMLDivElement>(null);

    const filterMessages = useMemo(() => {
      const notEmptyMessage = messages.filter((item) => item.content && item.content.trim());

      if (adminOnly && componentType === 'primary') {
        const arr = notEmptyMessage.filter((item) => item.isTime || item.admin);
        const reduceArr = arr.reduce((newArr, curItem) => {
          if (newArr.length === 0) {
            newArr.push(curItem);
            return newArr;
          }
          if (curItem.isTime && newArr[newArr.length - 1].isTime) {
            newArr[newArr.length - 1] = curItem;
          } else {
            newArr.push(curItem);
          }
          return newArr;
        }, [] as ChatData[]);
        if (reduceArr.length === 1 && reduceArr[0].isTime) {
          return [];
        }

        const lastItem = reduceArr[reduceArr.length - 1];
        if (lastItem && lastItem.isTime) {
          reduceArr.pop();
        }

        return reduceArr;
      } else {
        // 如果最后一条是系统时间的信息则直接过滤掉
        const lastItem = notEmptyMessage[notEmptyMessage.length - 1];
        if (lastItem && lastItem.isTime) {
          notEmptyMessage.pop();
        }
        return notEmptyMessage;
      }
    }, [messages, adminOnly, componentType]);

    const { listRef, setSize, getSize, resetAfterIndex } = useDynamicItemSize({
      itemCount: filterMessages.length,
    });

    const latestFilterMessagesRef = useRef<ChatData[]>(filterMessages);
    latestFilterMessagesRef.current = filterMessages;

    const scrollToBottom = () => {
      if (componentType === 'primary') {
        contextDispatch({
          type: 'UPDATE',
          payload: { isLockedScroll: false, showNewMessageButton: false },
        });
      }
      if (listRef.current?.scrollToItem && latestFilterMessagesRef.current?.length) {
        listRef.current.scrollToItem(latestFilterMessagesRef.current.length - 1, 'end');
      }
    };

    const latestScrollToBottom = useRef<() => void>(scrollToBottom);
    latestScrollToBottom.current = scrollToBottom;

    useImperativeHandle(ref, () => ({
      scrollToBottom: () => latestScrollToBottom.current?.(),
    }));

    useEffect(() => {
      if (!isLockedScroll) {
        scrollToBottom();
      }
      if (componentType === 'preview') {
        scrollToBottom();
      }
    }, [messages, isLockedScroll, componentType]);

    useEffect(() => {
      lastMessageCountRef.current = messages.length;
    }, [messages]);

    useLayoutEffect(() => {
      if (componentType === 'preview') return;
      lastSelectedTab.current = selectedTab;
    }, [selectedTab]);

    useLayoutEffect(() => {
      if (componentType === 'primary' && selectedTab === lastSelectedTab.current) {
        if (messages.length > lastMessageCountRef.current) {
          contextDispatch({ type: 'UPDATE', payload: { showNewMessageButton: true } });
        }
      }
    }, [messages, componentType]);

    const handleScroll = () => {
      if (componentType === 'preview') return;
      if (outerRef.current) {
        const el = outerRef.current;
        const isAtBottom = el.scrollHeight - el.scrollTop <= el.clientHeight + 1;
        if (isAtBottom) {
          contextDispatch({
            type: 'UPDATE',
            payload: { isLockedScroll: false, showNewMessageButton: false },
          });
        } else {
          contextDispatch({ type: 'UPDATE', payload: { isLockedScroll: true } });
        }
      }
    };

    const isPrimaryType = componentType === 'primary';

    return (
      <AutoSizer
        onResize={() => {
          resetAfterIndex(0, true);
        }}>
        {({ width, height }) => (
          <MessageListContainer
            ref={listRef}
            width={width}
            height={height}
            outerRef={outerRef}
            itemData={filterMessages}
            itemCount={filterMessages.length}
            itemSize={(index: number) => {
              return getSize(selectedTab, index);
            }}
            onScroll={() => {
              handleScroll();
            }}
            $isPrimary={isPrimaryType}>
            {({ data, index, style }: ListChildComponentProps<any>) => {
              return (
                <div style={style}>
                  <DynamicRow
                    data={data}
                    index={index}
                    setSize={setSize}
                    selectedTab={selectedTab}
                    windowWidth={width}
                    renderItem={(message: ChatData) => {
                      // // mock data
                      // if (message.uuid === '095f8b3e-b835-485a-8d14-07024d7acd7e') {
                      //   message.isSystem = true;
                      // }

                      if (message.isTime) {
                        const timestamp = message.timestamp;
                        const isTodayTime = dayjs(timestamp).isToday();

                        const format = isTodayTime ? 'HH:mm' : 'MM/DD/YYYY HH:mm';

                        return (
                          <StyledTimestamp key={message.uuid}>
                            {dayjs(timestamp).format(format)}
                          </StyledTimestamp>
                        );
                      }

                      if (message.isSystem) {
                        return (
                          <MessageSystemItem
                            key={message.uuid}
                            message={message}
                            isPrimary={isPrimaryType}
                          />
                        );
                      }

                      return (
                        <MessageItem
                          key={message.uuid}
                          message={message}
                          isPrimary={isPrimaryType}
                          handleReplyFocus={handleReplyFocus}
                        />
                      );
                    }}
                  />
                </div>
              );
            }}
          </MessageListContainer>
        )}
      </AutoSizer>
    );
  }
);
MessageList.displayName = 'MessageList';
export default MessageList;
