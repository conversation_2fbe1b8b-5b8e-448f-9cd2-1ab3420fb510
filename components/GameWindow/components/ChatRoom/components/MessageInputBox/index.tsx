import EmojiButton, { EmojiButtonRef } from '@/commons/EmojiButton';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';
import styled from 'styled-components';
import ChatRoomBirthdayButton from './ChatRoomBirthdayButton';
import { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { AppGameApiKey, useMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { useAppSelector } from '@/hooks/useStore';
import { useChatRoomContextDispatch, useChatRoomContextSelector } from '../../context';
import { ChatTabType } from '@/game/TS/Chat/ChatType';
import { ChatManager } from '@/game/TS/Chat/ChatManager';
import { SCENE_TYPE } from '@/constant/type';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';

export const MessageInputContainer = styled.div<{ replyMode?: boolean }>`
  display: flex;
  padding: 1rem;
  align-items: center;
  gap: 1rem;
  align-self: stretch;
  border-radius: 1.5rem;
  border: 0.0625rem solid #cabfab;
  background: #fff;
  box-shadow: 0rem 0.25rem 1.5rem 0rem rgba(0, 0, 0, 0.25);
  height: 4.5rem;
  pointer-events: auto; // 恢复交互

  .question-ask-input {
    flex: 1;
    overflow: hidden;
    height: 100%;

    & > input {
      width: 100%;
      height: 100%;
      padding: 0;
      outline: none;
      border: 0;
      font-family: Inter;
      font-weight: 400;
      font-size: 1.125rem;
      line-height: 1.36125rem;
      color: #140f08;

      &::placeholder {
        color: #686663;
      }
    }
  }

  button {
    width: 2.5rem;
    height: 2.5rem;
    outline: none;
    border-radius: 0.625rem;
    border: 0;
    background: #ff8316;
    box-shadow: 0rem -0.15625rem 0rem 0rem #00000040 inset;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    & > img {
      width: 1.25rem;
      height: 1.25rem;
    }

    &[disabled] {
      cursor: not-allowed;
      background: #c9b7a5;
      box-shadow: 0rem -0.15625rem 0rem 0rem #00000040 inset;
    }

    &.loading {
      & > img {
        animation: loading-ano 1s infinite;
      }
    }
  }
`;

// 添加倒计时样式
export const CooldownText = styled.span`
  font-size: 1.125rem;
  font-weight: bold;
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

interface ChatRoomInputProps {
  onAirdrop?: () => void;
  onSend?: () => void;
}

export interface ChatRoomInputRef {
  focusInput: () => void;
  setIsLoading: (isLoading: boolean) => void;
}

const MessageInputBox = forwardRef<ChatRoomInputRef, ChatRoomInputProps>(
  ({ onAirdrop, onSend = () => false }, ref) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [inputValue, setInputValue] = useState('');
    const [isOnCooldown, setIsOnCooldown] = useState(false);
    const [cooldownSeconds, setCooldownSeconds] = useState(0);
    const emojiButtonRef = useRef<EmojiButtonRef>(null);
    const cooldownTimerRef = useRef<NodeJS.Timeout | null>(null);
    const myPlayer = useMyPlayer();
    const [isLoading, setIsLoading] = useState(false);
    const sceneType = useAppSelector((state) => state.AppReducer.sceneType);
    const curChatType = useChatRoomContextSelector((state) => state.selectedTab);
    const replyChatData = useChatRoomContextSelector((state) => state.replyChatData);
    const formRef = useRef<HTMLFormElement>(null);
    const contextDispatch = useChatRoomContextDispatch();

    const handleSend = () => {
      contextDispatch({ type: 'UPDATE', payload: { replyChatData: null } });
      onSend();
    };

    const startCooldown = () => {
      setIsOnCooldown(true);
      setCooldownSeconds(3);

      // 清除任何现有的冷却计时器
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current);
      }

      // 创建新的冷却计时器，每秒减少冷却时间
      cooldownTimerRef.current = setInterval(() => {
        setCooldownSeconds((prev) => {
          if (prev <= 1) {
            // 冷却结束
            clearInterval(cooldownTimerRef.current!);
            setIsOnCooldown(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    };

    const onAsk = () => {
      if (!isOnCooldown && inputValue) {
        // 过滤掉空消息
        if (inputValue?.trim() === '') {
          return;
        }
        // 超过100个字符不允许发送
        if (inputValue.length > 100) {
          return;
        }
        ChatManager.getInstance().sendChatMessage(
          myPlayer.btcAddress,
          curChatType,
          inputValue,
          replyChatData?.uuid || ''
        );
        setInputValue('');

        // 触发发送冷却
        startCooldown();
        handleSend();
      }
    };

    // 暴露 focusInput 方法
    useImperativeHandle(ref, () => ({
      focusInput,
      setIsLoading: (isLoading: boolean) => {
        setIsLoading(isLoading);
      },
    }));

    function focusInput() {
      inputRef.current?.focus();
    }

    const showAirDropButton = useMemo(() => {
      return sceneType === SCENE_TYPE.Community && curChatType === ChatTabType.Room;
    }, [curChatType, sceneType]);

    return (
      <MessageInputContainer>
        <EmojiButton
          ref={emojiButtonRef}
          onChangeEmoji={(emoji) => {
            if (emoji) {
              setInputValue((prevValue) => prevValue + emoji);
              focusInput();
            }
          }}
        />
        {showAirDropButton && (
          <ChatRoomBirthdayButton onAirdrop={onAirdrop} isLoading={isLoading} />
        )}
        <form
          ref={formRef}
          name="messageForm"
          className="question-ask-input"
          onSubmit={(e) => {
            e.preventDefault();
            onAsk();
          }}>
          <input
            type="text"
            id="chatInput"
            name="chatInput"
            placeholder="Write a message..."
            ref={inputRef}
            maxLength={100}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !isOnCooldown) {
                onAsk();
              }
            }}
            onFocus={() => {
              // 禁止玩家控制
              KeyPressUtil.setEnable(false);
            }}
            onBlur={() => {
              // 开启玩家控制
              KeyPressUtil.setEnable(true);
            }}
          />
        </form>
        <button
          onClick={() => {
            onAsk();
          }}
          disabled={isOnCooldown}
          title={isOnCooldown ? `Wait ${cooldownSeconds}s` : 'Send message'}>
          {isOnCooldown ? (
            <CooldownText>{cooldownSeconds}</CooldownText>
          ) : (
            <SvgWrapper
              style={{
                width: '2.5rem',
                height: '2.5rem',
              }}>
              <SpriteSvg id="sendIcon" />
            </SvgWrapper>
          )}
        </button>
      </MessageInputContainer>
    );
  }
);

MessageInputBox.displayName = 'MessageInputBox';

export default MessageInputBox;
