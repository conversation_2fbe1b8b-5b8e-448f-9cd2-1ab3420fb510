import styled from 'styled-components';
import { useChatRoomContextDispatch, useChatRoomContextSelector } from '../context';
import {
  MessageItemReplyBox,
  PlayerTag,
  ReplyContent,
  ReplyIcon,
  ReplyPlayerIdBox,
} from '../styles';
import { useAppSelector } from '@/hooks/useStore';
import { usePlayerColors } from '@/hooks/usePlayerColors';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { useCallback } from 'react';
import { motion } from 'framer-motion';
import { formatPlayerId } from '../utils';

const ReplyBox = () => {
  const btcAddress = useAppSelector((state) => state.AppReducer.btcAddress);
  const replyChatData = useChatRoomContextSelector((state) => state.replyChatData);
  const contextDispatch = useChatRoomContextDispatch();

  const { getSpecialAccountProps } = usePlayerColors();

  let replyChatPlayerId,
    replyChatIsMe = false,
    replyChatIsGM = false,
    replyPlayerTagText;
  if (replyChatData) {
    replyChatPlayerId = replyChatData.playerId;
    replyChatIsMe = replyChatPlayerId === btcAddress;
    replyChatIsGM = !!getSpecialAccountProps(replyChatPlayerId);
    replyPlayerTagText = replyChatIsGM
      ? 'GM'
      : replyChatData.admin
        ? 'AM'
        : replyChatIsMe
          ? 'ME'
          : '';
  }

  const handleClose = useCallback(() => {
    contextDispatch({ type: 'UPDATE', payload: { replyChatData: null } });
  }, []);

  return replyChatData ? (
    <>
      <ReplyIcon />
      <StyledReplyPlayerIdBox>{formatPlayerId(replyChatData.playerId)}</StyledReplyPlayerIdBox>
      {!!replyPlayerTagText && (
        <PlayerTag
          $isMe={replyChatIsMe}
          $isGM={replyChatIsGM}
          $isAM={replyChatData.admin}
          data-text={replyPlayerTagText}
          $isPrimary={false}>
          {replyPlayerTagText}
        </PlayerTag>
      )}
      <StyledReplyContent>: {replyChatData.content.trim()}</StyledReplyContent>
      <StyledCloseIcon onClick={handleClose}>
        <SpriteSvg id="closeIcon" />
      </StyledCloseIcon>
    </>
  ) : (
    <></>
  );
};

const StyledCloseIcon = styled(SvgWrapper)`
  width: 1rem;
  height: 1rem;
  margin-left: auto;
  cursor: pointer;
  flex-shrink: 0;
`;

const StyledReplyPlayerIdBox = styled(ReplyPlayerIdBox)`
  color: #ff8316;
  font-size: 0.875rem;
  font-weight: 400;
`;

const StyledReplyContent = styled(ReplyContent)`
  color: #686663;
  font-size: 0.875rem;
  font-weight: 400;
`;

export default ReplyBox;
