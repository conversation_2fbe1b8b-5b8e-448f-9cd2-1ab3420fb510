import { SpriteSvg } from '@/components/SvgWrapper';
import MessageList from '../MessageList';
import { PreviewButton, PreviewButtonSvg, PreviewMessageBox, StyleButtonBox } from './styles';
import { IS_MOBILE_ENV } from '@/constant';
import { useChatRoomContextSelector } from '../../context';
import OrderQuery from './OrderQuery';

interface IChatRoomPreviewProps {
  onExpand: () => void;
}

const ChatRoomPreview: React.FC<IChatRoomPreviewProps> = ({ onExpand }) => {
  const haveNewMessage = useChatRoomContextSelector((state) => state.haveNewMessage);
  return (
    <>
      <StyleButtonBox>
        <OrderQuery />
        <PreviewButton onClick={onExpand}>
          <PreviewButtonSvg $withNew={haveNewMessage}>
            <SpriteSvg id="message" />
          </PreviewButtonSvg>
          <span className="text">Enter</span>
        </PreviewButton>
      </StyleButtonBox>
      {!IS_MOBILE_ENV && (
        <PreviewMessageBox onClick={onExpand}>
          <MessageList componentType="preview" />
        </PreviewMessageBox>
      )}
    </>
  );
};

export default ChatRoomPreview;
