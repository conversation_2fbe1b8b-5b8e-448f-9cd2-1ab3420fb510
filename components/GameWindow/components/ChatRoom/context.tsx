import { ChatTabType } from '@/game/TS/Chat/ChatType';
import { createTemplateContext } from '@/utils/createTemplateContext';
import { ChatData } from '@/game/TS/Chat/ChatData';

interface ChatRoomState {
  typeList: ChatTabType[];
  selectedTab: number;
  adminOnly: boolean;
  messages: ChatData[];
  replyChatData: ChatData | null;
  isLockedScroll: boolean;
  showNewMessageButton: boolean;
  // updateTypeList: (typeList: ChatTabType[]) => void;
  haveNewMessage: boolean;
}

const initState: ChatRoomState = {
  typeList: [],
  selectedTab: -1,
  adminOnly: false,
  messages: [],
  replyChatData: null,
  isLockedScroll: false,
  showNewMessageButton: false,
  haveNewMessage: false,
};

const {
  TemplateContextProvider: ChatRoomContextProvider,
  useContextDispatch: useChatRoomContextDispatch,
  useContextSelector: useChatRoomContextSelector,
} = createTemplateContext<ChatRoomState>(initState);

ChatRoomContextProvider.displayName = 'ChatRoomContextProvider';

export { ChatRoomContextProvider, useChatRoomContextDispatch, useChatRoomContextSelector };
