import Dialog from '@/commons/Dialog';
import { memo, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import BasicCommunityModalContent, {
  BasicTitle,
  ModalCloseBtnWrapper,
} from '@/components/BasicCommunityModalContent';
import styled, { css } from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { updateModalState } from '@/store/modal';
import { motion } from 'framer-motion';
import Tooltip from 'rc-tooltip';
import { TooltipBox } from '@/components/EditAvatarPage/BagModal/ContentItem';
import useBagInventory from '@/hooks/useBagInventory';
import { IBagInventoryItem } from '@/constant/type';
import { CHAIN_TYPE_ENUM, INVENTORY_TYPE_ENUM, UserItemType } from '@/constant/enum';
import useLatest from '@/hooks/useLatest';
import { placePetBed } from '@/server';
import toast from 'react-hot-toast';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import useFetchPetShedInfo from '@/hooks/useFetchPetShedInfo';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';
gsap.registerPlugin(ScrollTrigger);

function usePlacePetBed() {
  const { bagInventoryList } = useBagInventory(false);

  const petBedDataList = useMemo(() => {
    return bagInventoryList?.filter((item) => item.type === INVENTORY_TYPE_ENUM.petResource);
  }, [bagInventoryList]);

  const [selectedItem, setSelectedItem] = useState<IBagInventoryItem | null>(null);
  const latestSelectedIteRef = useLatest(selectedItem);

  return {
    petBedDataList,
    selectedItem: selectedItem,
    updateSelected: (item: IBagInventoryItem | null) => {
      if (item?.userItemId === latestSelectedIteRef.current?.userItemId) {
        setSelectedItem(null);
      } else {
        setSelectedItem(item);
      }
    },
    latestSelectedIteRef,
  };
}

const PlacePetBedModal = () => {
  const placePetModalConfig = useAppSelector((state) => state.ModalReducer.placePetModalConfig);
  const petSubmitChainModalConfig = useAppSelector(
    (state) => state.ModalReducer.petSubmitChainModalConfig
  );
  const dispatch = useAppDispatch();

  const reset = () => {
    updateSelected(null);
    setIsOnChain(false);
  };

  const onClose = () => {
    reset();
    dispatch(
      updateModalState({
        placePetModalConfig: {
          isOpen: false,
          positionTag: '',
        },
      })
    );
  };

  const [isOnChain, setIsOnChain] = useState(false);

  const { petBedDataList, selectedItem, updateSelected, latestSelectedIteRef } = usePlacePetBed();

  const { getBagInventoryListDta, getPetListData } = useBagInventory(false);

  const [loading, setLoading] = useState(false);
  const { fetchPetShedInfo, updateReduxPetShed } = useFetchPetShedInfo();

  const petBedRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useLayoutEffect(() => {
    const el = petBedRef.current;
    let onWheel: (e: WheelEvent) => void;
    if (!placePetModalConfig.isOpen) return;
    const bindWheel = (el: HTMLDivElement | null) => {
      if (!el) {
        timerRef.current = setTimeout(() => {
          bindWheel(petBedRef.current);
        }, 100);
        return;
      }
      onWheel = (e: WheelEvent) => {
        if (Math.abs(e.deltaX) < Math.abs(e.deltaY)) {
          el.scrollLeft += e.deltaY;
          e.preventDefault();
        }
      };
      el.addEventListener('wheel', onWheel, { passive: false });
    };

    bindWheel(petBedRef.current);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (el) {
        el.removeEventListener('wheel', onWheel);
      }
    };
  }, [placePetModalConfig.isOpen, petBedRef.current]);

  useEffect(() => {
    reset();
    if (placePetModalConfig.isOpen) {
      setLoading(false);
      KeyPressUtil.setEnable(false);
    } else {
      KeyPressUtil.setEnable(true);
    }
  }, [placePetModalConfig]);

  const callbackRef = useRef<() => void>(() => false);
  callbackRef.current = onClose;

  const handleOnChainPetGenerator = () => {
    dispatch(
      updateModalState({
        petSubmitChainModalConfig: {
          isOpen: true,
          configData: {
            generateData: {
              userItemId: selectedItem?.userItemId,
              positionTag: placePetModalConfig.positionTag,
            },
          },
          confirmCallback: () => {
            fetchPetShedInfo();
            getBagInventoryListDta();
            getPetListData();

            callbackRef.current?.();
          },
        },
      })
    );
  };

  const handleUnderChainPetGenerator = async () => {
    if (loading) return;
    if (latestSelectedIteRef.current) {
      try {
        setLoading(true);
        const res = await placePetBed({
          userItemId: latestSelectedIteRef.current.userItemId,
          positionTag: placePetModalConfig.positionTag,
        });
        if (res.data.code == 1) {
          getBagInventoryListDta();
          getPetListData();
          const { slotRecordStatus, ...resData } = res.data.data;
          updateReduxPetShed({
            ...resData,
            status: slotRecordStatus,
            baseType: selectedItem?.singleType as any,
            chainType: CHAIN_TYPE_ENUM.UNDER_CHAIN,
          });

          setLoading(false);
          onClose();
        } else {
          toast.error(res.data.msg);
          setLoading(false);
        }
      } catch (error) {
        console.log(error);
        setLoading(false);
      }
    }
  };

  const disabled = isOnChain ? petSubmitChainModalConfig.isOpen : loading;

  const onConfirm = async () => {
    if (isOnChain) {
      handleOnChainPetGenerator();
    } else {
      handleUnderChainPetGenerator();
    }
  };

  if (!placePetModalConfig.isOpen) return null;

  return (
    <Dialog isOpen={placePetModalConfig.isOpen} onClose={onClose} zIndex={98}>
      <StyledBasicCommunityModalContent
        onClose={onClose}
        modalWidth="41.5rem"
        closeButton={true}
        headerStyle={{
          top: 0,
          left: 0,
          transform: 'translate(0, -50%)',
        }}
        title={<StyledBasicTitle title="Place" />}
        onCancel={onClose}
        confirmText="Confirm"
        onConfirm={onConfirm}
        confirmLoading={disabled}
        confirmDisabled={disabled || !selectedItem}>
        <ModalBodyContainer>
          <DescParagraph>
            Choose a <strong>Pet Nest / Fusion Device</strong> and place it.
          </DescParagraph>
          <StyledPetBedWrapper>
            <PetBedContainer ref={petBedRef}>
              {petBedDataList.map((item, index) => {
                const showDetail = {
                  name: item.name,
                  quality: item.quality ?? 0,
                  trait: item.description,
                };
                return (
                  <Tooltip
                    key={'petBed_' + item.userItemId}
                    zIndex={99}
                    trigger="hover"
                    overlay={<StyledToolTipBox showDetail={showDetail as any} />}
                    showArrow={false}
                    align={{
                      points: ['bl', 'bl'],
                      offset: ['20.18%', '-25.5%'],
                    }}>
                    <CommonItemBox
                      $active={item.userItemId === selectedItem?.userItemId}
                      onClick={() => {
                        updateSelected(item);
                        if (item.singleType === UserItemType.PET_ALTAR) {
                          setIsOnChain(false);
                        }
                      }}
                      initial={{ opacity: 0, scale: 0.7 }}
                      animate={
                        placePetModalConfig.isOpen
                          ? { opacity: 1, scale: 1 }
                          : { opacity: 0, scale: 0.7 }
                      }
                      exit={{ opacity: 0, scale: 0.7 }}
                      transition={{
                        delay: index * 0.2,
                        duration: 0.4,
                        ease: 'easeOut',

                        scale: {
                          type: 'spring',
                          stiffness: 400,
                          damping: 10,
                        },
                      }}>
                      <img src={item.icon} draggable={false} loading="lazy" />
                    </CommonItemBox>
                  </Tooltip>
                );
              })}
            </PetBedContainer>
          </StyledPetBedWrapper>

          {selectedItem?.singleType === UserItemType.PET_SHED && (
            <CheckboxContainer checked={isOnChain}>
              <StyledSvgWrapper>
                <SpriteSvg id={isOnChain ? 'checkBoxActive' : 'checkBox'} />
              </StyledSvgWrapper>
              <input
                type="checkbox"
                checked={isOnChain}
                onChange={() => {
                  setIsOnChain(!isOnChain);
                }}
              />
              <span>On Chain</span>
            </CheckboxContainer>
          )}
        </ModalBodyContainer>
      </StyledBasicCommunityModalContent>
    </Dialog>
  );
};

const StyledToolTipBox = styled(TooltipBox)`
  pointer-events: none;
`;

const StyledPetBedWrapper = styled.div`
  height: 11.75rem;
  width: 100%;
  overflow: hidden;
  border-radius: 2rem;
  background: #f7e7cd;
  box-shadow: 0 0 0.5rem 0 rgba(0, 0, 0, 0.25) inset;
  padding: 2rem 0.75rem 1rem;
  box-sizing: border-box;
  width: 37.5rem;
`;

const StyledSvgWrapper = styled(SvgWrapper)`
  width: 1.875rem;
  height: 1.5rem;
  justify-content: flex-start;
  --fillColor: white;
  & > svg {
    justify-content: flex-start;
  }
`;

const CheckboxContainer = styled.label<{ checked: boolean }>`
  height: 100%;
  display: flex;
  height: 1.5rem;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  input[type='checkbox'] {
    display: none;
  }

  & > span {
    color: #140f08;
    font-family: Inter;
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
    line-height: 100%;
    text-transform: capitalize;
  }
`;

export const CommonItemBox = styled(motion.div)<{ $active: boolean; $disabled?: boolean }>`
  display: flex;
  width: 7.75rem;
  height: 7.75rem;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  width: 7.75rem;
  height: 7.75rem;
  flex-shrink: 0;
  box-shadow:
    0 0.1291875rem 0.5166875rem 0 rgba(0, 0, 0, 0.15),
    0.2583125rem 0.2583125rem 0.2583125rem 0 #fff inset;
  background: #fbf4e8;
  border-radius: 1.25rem;
  border: 0.0625rem solid #cabfab;
  transition: border 0.2s ease-in;
  box-sizing: border-box;
  cursor: pointer;

  ${({ $active }) =>
    $active &&
    css`
      border: 0.25rem solid #ff8316;
    `}
  &>img {
    width: 100%;
    height: 100%;
  }
  ${({ $disabled = false }) =>
    $disabled
      ? css`
          filter: brightness(0.6);
          cursor: not-allowed;
        `
      : css``}
`;

const ModalBodyContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
`;

const DescParagraph = styled.p`
  margin: 0;
  color: #140f08;
  text-align: center;
  font-family: 'JetBrains Mono';
  font-size: 1.125rem;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 1.35rem */
  letter-spacing: -0.045rem;
`;

const PetBedContainer = styled.div`
  display: flex;
  width: 100%;
  padding: 0 0.75rem 0.5rem;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  height: 100%;
  box-sizing: border-box;
  overflow-y: hidden;
  overflow-x: scroll;
  ::-webkit-scrollbar {
    background: transparent !important;
    cursor: pointer;
  }
  ::-webkit-scrollbar-track {
    background-color: transparent !important;
    cursor: pointer;
  }
  ::-webkit-scrollbar-thumb {
    cursor: pointer;
    background: #c69f7e !important;
  }
`;

const StyledBasicCommunityModalContent = styled(BasicCommunityModalContent)`
  padding: 4.5rem 2rem 3rem;
  border-color: #140f08;
  box-shadow: inset 0 0 0 0.5rem #ff8316;
  border-radius: 3rem;
  justify-content: space-between;
  ${ModalCloseBtnWrapper} {
    right: 1.5rem;
  }
`;

const StyledBasicTitle = styled(BasicTitle)`
  & > p {
    color: #fff;
    text-align: center;
    font-family: 'Baloo 2';
    font-size: 1.875rem;
    font-style: normal;
    font-weight: 800;
    line-height: 100%;
    filter: none;
    position: relative;
    text-shadow: none;
    text-shadow: 0.046875rem 0.09375rem 0 #000b22;

    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.1875rem #664830;
      z-index: -1;
      left: 0;
    }
  }
`;

export default memo(PlacePetBedModal);
