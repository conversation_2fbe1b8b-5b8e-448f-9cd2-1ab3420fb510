import { getPizzaActivity } from '@/game/TS/Activity/PizzaActivity';
import { useEffect, useRef, useState } from 'react';
import PizzaActivityRewardWindow, {
  ActivityRewardRef,
} from '@/components/GameWindow/PizzaActivityWindow/PizzaActivityRewardWindow';
import PizzaRush from './PizzaRush';
import NextRound from './NextRound';
import TimeLeft from './TimeLeft';
import TallyingUp from './TallyingUp';

export default function PizzaActivityWindow() {
  const pizzaActivity = getPizzaActivity();
  const rewardRef = useRef<ActivityRewardRef>(null);
  const { pizzaRushSecond, timeLeftSecond, isLast, setIsLast, roundEndTime } =
    usePizzaRushTimeLeft();

  useEffect(() => {
    pizzaActivity.setShowReward(
      (rankData: { rank: number; score: number; rewards: number; tick: string } | null) => {
        setIsLast(false);
        if (rewardRef.current && rankData) {
          rewardRef.current.open(rankData);
        }
      }
    );
    return () => {
      pizzaActivity.setShowReward(() => {});
    };
  }, []);

  useEffect(() => {
    const now = Date.now();
    //因为飘带消失时间是3秒，所以持续时间 - 3000
    if (roundEndTime > 0 && now < roundEndTime - 3000) {
      // createAnimate({
      //   // @ts-ignore
      //   emojis: ["🍕"],
      //   duration: roundEndTime - 3000 - now,
      // });
    }
  }, [roundEndTime]);

  return (
    <>
      <PizzaActivityRewardWindow ref={rewardRef} onClose={() => {}} />
      <PizzaRushTimeLeft
        pizzaRushSecond={pizzaRushSecond}
        timeLeftSecond={timeLeftSecond}
        isLast={isLast}
      />
    </>
  );
}

const PizzaRushTimeLeft = ({ pizzaRushSecond, timeLeftSecond, isLast }: any) => {
  return (
    <>
      {pizzaRushSecond >= 0 && <PizzaRush pizzaRushSecond={pizzaRushSecond} />}
      {timeLeftSecond >= 0 && !isLast && <NextRound timeLeftSecond={timeLeftSecond} />}
      {timeLeftSecond >= 0 && isLast && <TimeLeft timeLeftSecond={timeLeftSecond} />}
      {timeLeftSecond >= 0 && <div className="piao dai" />}
      {timeLeftSecond < 0 && pizzaRushSecond < 0 && isLast && <TallyingUp />}
    </>
  );
};

function usePizzaRushTimeLeft() {
  const pizzaActivity = getPizzaActivity();

  const [pizzaRushSecond, setPizzaRushSecond] = useState(-1);
  const [timeLeftSecond, setTimeLeftSecond] = useState(-1);
  const [isLast, setIsLast] = useState(false);
  const [roundEndTime, setRoundEndTime] = useState(0);

  const checkActivity = () => {
    const activityData = pizzaActivity.getActivityData();
    const round = pizzaActivity.getCurrentRound();
    const currentRound = activityData.pizzaData[round] || null; // null: game ove
    const nextRound = activityData.pizzaData[round + 1] || null; // null: game not// r
    const now = Date.now();
    if (currentRound && activityData.pizzaData.length > 0) {
      setRoundEndTime(
        activityData.startTime + currentRound.second * 1000 + currentRound.duration * 1000
      );
    }
    if (
      activityData.signUpTime > 0 &&
      activityData.signUpTime < now &&
      activityData.endTime >= now
    ) {
      if (now < activityData.startTime) {
        setPizzaRushSecond(Math.floor((activityData.startTime - now) / 1000));
      } else {
        setPizzaRushSecond(-1);
      }
      const startRunningTime = now - activityData.startTime;
      if (currentRound && startRunningTime < (currentRound.second + currentRound.duration) * 1000) {
        setTimeLeftSecond(
          Math.floor(
            ((currentRound.second + currentRound.duration) * 1000 - startRunningTime) / 1000
          )
        );
      } else {
        setTimeLeftSecond(-1);
        if (!nextRound && startRunningTime > 0) {
          pizzaActivity.tryReport();
        }
      }
      if (nextRound) {
        setIsLast(false);
      } else {
        if (activityData.pizzaData.length > 0) {
          setIsLast(true);
        }
      }
    }
  };
  useEffect(() => {
    const interval = setInterval(() => {
      checkActivity();
    }, 250);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return {
    pizzaRushSecond,
    timeLeftSecond,
    isLast,
    roundEndTime,
    setIsLast,
  };
}
