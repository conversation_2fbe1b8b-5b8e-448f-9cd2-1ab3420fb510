'use client';
import { forwardRef, Ref, useImperative<PERSON><PERSON><PERSON>, useState } from 'react';
import Dialog from '@/commons/Dialog';

import Image from 'next/image';
import styled from 'styled-components';
import rewardBg from '/public/image/reward-bg.png';
import sPizza___000 from '/public/image/coin/sPizza___000.png';
import start from '/public/image/pizza/start.png';
import {
  StyledDialogBody,
  Container,
  RewardNumber,
  Items,
  StyledStarGroup,
  StyledRewardTitle,
  StyledRewardsContent,
  ShakeButton,
  StyledIcon,
} from '@/components/EasterEggReward/styles';
import { withNonSelectable } from '@/utils/hoc';
import { COMMUNITY_ICON_MAP } from '@/constant';
import { RewardType } from '@/constant/enum';
interface ModalProps {
  onClose: () => void;
}

export interface ActivityRewardRef {
  open: (data: { rank: number; score: number; rewards: number; tick: string }) => void;
}

const StyledConfetti = styled(Image)`
  position: absolute;
  top: -4%;
  left: -6%;
  z-index: 1;
  user-select: none;
  pointer-events: 'none';
`;

const StyledContextText = styled.div`
  font-family: JetBrains Mono;
  font-weight: 400;
  font-size: 1.125rem;
  line-height: 120%;
  letter-spacing: -0.04em;
  text-align: center;
  vertical-align: middle;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  margin-top: 3.375rem;
  row-gap: 1rem;

  p {
    margin: 0;
  }
  & > p:last-of-type {
    margin-top: 0.5rem;
  }
`;

const StyledPizzaInfo = styled.div`
  display: flex;
  width: 32rem;
  padding: 0.5rem 1rem 0.75rem 1rem;
  align-items: center;
  gap: 1rem;
  border-radius: 1.25rem;
  border: 0.0625rem solid #542d00;
  background: #f7e7cd;
  box-shadow: 0rem -0.375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset;
  height: 3.75rem;
  box-sizing: border-box;
`;

const StyledPizzaIconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const PizzaIcon = withNonSelectable((props: any) => {
  return (
    <StyledPizzaIconWrapper {...props}>
      <Image
        src="/image/pizza/pizza_svg.svg"
        alt="pizza"
        width={24}
        height={24}
        style={{ width: '1.5rem', height: '1.5rem' }}
      />
    </StyledPizzaIconWrapper>
  );
});

interface ITag {
  children: React.ReactNode;
  [k: string]: any;
}
const Tag = ({ children, ...props }: ITag) => {
  return (
    <div {...props}>
      <span>{children}</span>
    </div>
  );
};

const StyledTag = styled(Tag)`
  display: flex;
  height: 2.5rem;
  padding: 0.5rem 0.625rem;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 1rem;
  background: #ff8316;
  min-width: 2.5rem;
  box-sizing: border-box;
  span {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.25rem;
    letter-spacing: -0.04em;
    vertical-align: middle;
  }
`;

const StyledRewardNumberWrapper = styled.div`
  display: flex;
  width: 39.5rem;
  padding: 2rem 1.5rem;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  border-radius: 2rem;
  background: #f7e7cd;
  box-shadow: 0rem 0rem 0.5rem 0rem rgba(0, 0, 0, 0.25) inset;
  box-sizing: border-box;
`;

const StyledRewardNumber = styled(RewardNumber)`
  height: auto;
  margin: 0.5rem 0;
`;

const PizzaActivityRewardWindow = forwardRef<ActivityRewardRef, ModalProps>(
  ({ onClose }: ModalProps, ref: Ref<ActivityRewardRef>) => {
    const [isOpen, setIsOpen] = useState(false);

    const [rewardConfig, setRewardConfig] = useState({
      score: 0,
      rank: 0,
      rewards: 0,
      tick: '',
    });
    const onConfirm = async () => {
      setIsOpen(false);
      setRewardConfig({
        score: 0,
        rank: 0,
        rewards: 0,
        tick: '',
      });
    };

    useImperativeHandle(ref, () => ({
      open: (data: { rank: number; score: number; rewards: number; tick: string }) => {
        setIsOpen(true);
        setRewardConfig({
          score: data.score,
          rank: data.rank,
          rewards: data.rewards,
          tick: data.tick,
        });
      },
    }));

    return (
      <Dialog isOpen={isOpen} onClose={() => {}}>
        <StyledDialogBody
          style={{
            height: '33.75rem',
          }}>
          <StyledConfetti
            src={start.src}
            alt="star1"
            width={124}
            height={190}
            style={{
              width: '7.75rem',
              height: '11.875rem',
            }}
          />
          <StyledRewardTitle $bgSrc="/image/pizza/congratulation.png" />
          <Container>
            <StyledRewardsContent
              style={{
                gap: '1rem',
              }}>
              <StyledContextText>
                <p>In this round</p>
                <StyledPizzaInfo>
                  <PizzaIcon />
                  <p>You have obtained pizza:</p>
                  <StyledTag>{rewardConfig.score}</StyledTag>
                </StyledPizzaInfo>
                <StyledPizzaInfo>
                  <PizzaIcon />
                  <p>Ranking among all players:</p>
                  <StyledTag>{rewardConfig.rank}</StyledTag>
                </StyledPizzaInfo>

                <p>Here are your rewards</p>
              </StyledContextText>

              <StyledRewardNumberWrapper>
                <StyledRewardNumber>
                  <Items>
                    <StyledIcon>
                      <Image
                        src={COMMUNITY_ICON_MAP[rewardConfig.tick as RewardType]}
                        alt=""
                        width={64}
                        height={64}
                        className="icon-image"
                        style={{
                          border: '0.25rem solid #FF8316',
                          width: '4rem',
                          height: '4rem',
                        }}
                      />
                    </StyledIcon>
                    <div className="value">{rewardConfig.rewards || ''}</div>
                  </Items>
                </StyledRewardNumber>
              </StyledRewardNumberWrapper>

              <ShakeButton text="Claim" onClick={onConfirm} />
            </StyledRewardsContent>
          </Container>
          <StyledStarGroup $bgSrc="/image/pizza/pizza-1.png" />
        </StyledDialogBody>
      </Dialog>
    );
  }
);

PizzaActivityRewardWindow.displayName = 'PizzaActivityRewardWindow';

export default PizzaActivityRewardWindow;
