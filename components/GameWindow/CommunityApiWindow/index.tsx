import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import toast from 'react-hot-toast';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';

import DonateTokens, { DonateTokensRef } from '@/components/DonateTokens';
import { getDonationInfo, getWaterClaimInfo } from '@/server';
import BuyEnergy, { useBuyEnergy } from '@/components/BuyTools';
import Claim, { ClaimRef } from '@/components/Claim';
// import Submission, {SubmissionRef} from "@/components/Submission";
import { IAppState, SCENE_TYPE } from '@/constant/type';
import { CommunityTable, CommunityTableRef } from '@/components/CommunityTable';
import { ResourceTable, ResourceTableRef } from '@/components/ResourceTable';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import SubmissionV2, { SubmissionV2Ref } from '@/components/SubmisionV2';

const CommunityApiWindow: React.FC = () => {
  const { userBasicInfo } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  // const submissionRef = useRef<SubmissionRef>(null);
  const submissionRef = useRef<SubmissionV2Ref>(null);
  const claimRef = useRef<ClaimRef>(null);
  const donateTokensRef = useRef<DonateTokensRef>(null);

  const myPlayer = GetMyPlayer();
  // 添加显示/隐藏状态
  const communityRef = useRef<CommunityTableRef>(null);
  const fishRef = useRef<ResourceTableRef>(null);
  const stoneRef = useRef<ResourceTableRef>(null);
  const treeRef = useRef<ResourceTableRef>(null);
  const [sceneType, setSceneType] = useState<SCENE_TYPE>(SCENE_TYPE.None);
  const [sceneLoading, setSceneLoading] = useState<boolean>(true);
  const buyEnergyRef = useBuyEnergy();

  useEffect(() => {
    const loadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (value) => {
        setSceneLoading(value);
      }
    );
    const sceneTypeKey = GlobalSpaceEvent.ListenKeyDataChange(
      GlobalDataKey.SceneType,
      (_sceneType: SCENE_TYPE) => {
        setSceneType(_sceneType);
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, loadingKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, sceneTypeKey);
    };
  }, []);

  useEffect(() => {
    myPlayer.setAppApi(AppGameApiKey.refreshRank, () => {
      communityRef.current?.fetchData().then();
      fishRef.current?.fetchData().then();
      stoneRef.current?.fetchData().then();
      treeRef.current?.fetchData().then();
    });
  }, []);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.shareTweets, (tweet_id: string) => {
        const share = async () => {
          window.open(
            `https://x.com/intent/retweet?tweet_id=${tweet_id}`,
            '_blank',
            'width=600,height=600'
          );
        };
        share().then();
      });
      myPlayer.setAppApi(
        AppGameApiKey.donateTokens,
        (donationCommunity: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks') => {
          const open = async () => {
            const res = await getDonationInfo(donationCommunity);
            const { code, msg, data } = res.data;
            if (code === 1) {
              donateTokensRef.current?.open(data, donationCommunity);
            } else {
              console.error(msg);
            }
          };
          open().then();
        }
      );
      myPlayer.setAppApi(
        AppGameApiKey.submitResources,
        (submitCommunity: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks') => {
          const open = async () => {
            // const res = await getMaterialList();
            // const {code, msg, data} = res.data;
            // if (code === 1) {
            //   // data 如果是空数组，则表示没有材料可以提交
            //   if (data.length === 0 || !data) {
            //     toast.error("You don't have enough resources to submit.");
            //     return;
            //   }
            //   // 如果data 不是空数组，则表示有材料可以提交
            //   submissionRef.current?.open(data, submitCommunity);
            // } else {
            //   console.error(msg);
            // }
            // 如果data 不是空数组，则表示有材料可以提交
            submissionRef.current?.open(submitCommunity);
          };
          open().then();
        }
      );
    }
  }, []);

  useEffect(() => {
    myPlayer.setAppApi(
      AppGameApiKey.claimDrop,
      (claimCommunity: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks') => {
        const claim = async () => {
          const toolConfig = userBasicInfo?.toolConfig;
          if (toolConfig) {
            // 判断是否具有领水的条件
            const res = await getWaterClaimInfo(claimCommunity);
            const { code, msg, data } = res.data;
            if (code === 1) {
              claimRef.current?.open(data, claimCommunity);
            } else {
              toast.error(msg);
            }
          } else {
          }
        };
        claim().then();
      }
    );
  }, [userBasicInfo]);

  return (
    <>
      <DonateTokens ref={donateTokensRef} onClose={() => {}} />
      <BuyEnergy ref={buyEnergyRef} />
      <Claim ref={claimRef} onClose={() => {}} />
      {/* <Submission
        ref={submissionRef}
        onClose={() => {
        }}
      /> */}
      <SubmissionV2 ref={submissionRef} onClose={() => {}} />
      {sceneType === SCENE_TYPE.Community && !sceneLoading && (
        <div
          style={{
            position: 'absolute',
            left: '-9999px',
            top: 0,
          }}>
          <CommunityTable
            ref={communityRef}
            timerInterval={1}
            autoPolling={true}
            onDataUpdate={(element) => {
              if (element) {
                myPlayer.callAppApi(AppGameApiKey.updateCommunityRank, element);
              }
            }}
          />
          <ResourceTable
            ref={fishRef}
            timerInterval={1}
            autoPolling={true}
            resourceType={'fish'}
            onDataUpdate={(element) => {
              if (element) {
                myPlayer.callAppApi(AppGameApiKey.updateFishRank, element);
              }
            }}
          />
          <ResourceTable
            ref={treeRef}
            timerInterval={1}
            autoPolling={true}
            resourceType={'tree'}
            onDataUpdate={(element) => {
              if (element) {
                myPlayer.callAppApi(AppGameApiKey.updateTreeRank, element);
              }
            }}
          />
          <ResourceTable
            ref={stoneRef}
            timerInterval={1}
            autoPolling={true}
            resourceType={'stone'}
            onDataUpdate={(element) => {
              if (element) {
                myPlayer.callAppApi(AppGameApiKey.updateStoneRank, element);
              }
            }}
          />
        </div>
      )}
    </>
  );
};

export default CommunityApiWindow;
