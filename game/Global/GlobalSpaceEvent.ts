import { generateUUID } from 'three/src/math/MathUtils';
import * as THREE from 'three';
import { SCENE_TYPE } from '../../constant/type';

export enum CharacterType {
  None = 'None',
  Player = 'Player',
  Pet = 'Pet',
}

export interface TransformData {
  characterType: CharacterType;
  position: THREE.Vector3;
  camDirection?: THREE.Vector3;
  sceneType?: SCENE_TYPE;
}

export enum SpaceStatus {
  Avatar = 1,
  NFT = 2,
  Game = 3,
}

export interface MusicSetting {
  bgmOpen: boolean; //背景音乐开关
  bgmVolume: number; //背景音乐大小
  effectOpen: boolean; //音效开关
  effectVolume: number; //音效大小
}

export enum GlobalDataKey {
  SceneLoading = 'SceneLoading',
  SceneType = 'SceneType',
  ShowIslandPotato = 'ShowIslandPotato',
  TransformData = 'TransformData',
  EditorControls = 'EditorControls',
  UsePetInscriptionId = 'UsePetInscriptionId',
  DirectionalLight = 'DirectionalLight',
  AmbientLight = 'AmbientLight',
  NftData = 'nftData',
  FtData = 'ftData',
  NftHoverObj = 'nftHoverObj',
  FtHoverObj = 'ftHoverObj',
  LookingNftIndex = 'LookingNftIndex',
  LookingNftLeft = 'LookingNftLeft',
  SpaceStatus = 'SpaceStatus',
  PhysicsDebug = 'PhysicsDebug',
  FpsSetting = 'FpsSetting',
  MyAvatarData = 'MyAvatarData',
  ButlerAvatarData = 'ButlerAvatarData',
  OpenFreeCamera = 'OpenFreeCamera',
  IsSocketConnected = 'IsSocketConnected',
  AvatarObject = 'AvatarObject',
  IsMobileFlip = 'IsMobileFlip',
}

class GlobalData<T> {
  private _value: T | null = null;
  private listenMap: Map<string, (data: T) => void> = new Map();

  setValue(data: T | null) {
    if (data === null) {
      this._value = null;
      return;
    }
    if (this._value === data) {
      return;
    }
    this._value = data;
    this.notice();
  }

  listenChange(callback: (data: T) => void, once: boolean) {
    this.notice(callback);
    if (!once) {
      const key = generateUUID();
      this.listenMap.set(key, callback);
      return key;
    }
    return '';
  }

  removeListenChange(key: string) {
    this.listenMap.delete(key);
  }

  private notice(_callback?: (data: T) => void) {
    if (this._value !== null) {
      if (_callback) {
        _callback(this._value);
      } else {
        for (const [key, callback] of this.listenMap) {
          callback(this._value);
        }
      }
    }
  }
}

const DataMap: Map<GlobalDataKey, GlobalData<any>> = new Map();

const GetData = <T>(key: GlobalDataKey) => {
  let data = DataMap.get(key);
  if (!data) {
    data = new GlobalData<T>();
    DataMap.set(key, data);
  }
  return data as GlobalData<T>;
};

const SetDataValue = <T>(key: GlobalDataKey, _data: T | null) => {
  const data = GetData<T>(key);
  data.setValue(_data);
};

const ListenKeyDataChange = <T>(key: GlobalDataKey, callback: (data: T) => void, once = false) => {
  const data = GetData<T>(key);
  return data.listenChange(callback, once);
};

const RemoveListener = <T>(key: GlobalDataKey, callbackKey: string) => {
  const data = GetData<T>(key);
  data.removeListenChange(callbackKey);
};

export default { SetDataValue, ListenKeyDataChange, RemoveListener };
