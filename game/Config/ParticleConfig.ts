import { ConfigManager } from '@/game/Config/ConfigManager';

export type ParticleData = {
  id: number;
  position: number[];
  yawY: number;
  scale: number;
  particle_url: string;
};

export class ParticleConfig {
  private static instance: ParticleConfig;

  private particleDataMap: Map<number, { url: string; data: ParticleData }>;

  private constructor() {
    this.particleDataMap = new Map<number, { url: string; data: ParticleData }>();
    ConfigManager.getInstance().downloadConfig(
      './particles/_totals.json',
      (data: ParticleData[]) => {
        for (let i = 0; i < data.length; i++) {
          const particleData = data[i];
          this.particleDataMap.set(particleData.id, { url: '', data: particleData });
        }
      }
    );
  }

  static getInstance() {
    if (!ParticleConfig.instance) {
      ParticleConfig.instance = new ParticleConfig();
    }
    return ParticleConfig.instance;
  }

  getData(id: number, cb: (data: ParticleData) => void) {
    if (this.particleDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.particleDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      console.error('not found particle config id: ' + id);
    }
  }
}
