import * as THREE from 'three';
import { ConfigManager } from '@/game/Config/ConfigManager';

export type FishData = {
  name: string;
  id: number;
  glb_url: string;
  show_range: number[];
  bite_times: number;
  bite_range: number[];
  success_range: number[];
  bite_speed: number;
  fish_scale: number;
  fish_yOffset: number;
};

export enum FishStatus {
  Hide = 0, //隐藏中
  Show = 1, //出现
  Idle = 2, //静置
  Bite = 3, //尝试咬钩
  Catch = 4, //抓住挣扎
}

export type FishFloatObject = {
  tag: number;
  status: FishStatus;
  id: string; //for server
};

export class FishConfig {
  private static instance: FishConfig;

  private fishDataMap: Map<number, { url: string; data: FishData; object: FishFloatObject }>;
  private waterMesh: THREE.Mesh | null = null;

  private constructor() {
    this.fishDataMap = new Map<number, { url: string; data: FishData; object: FishFloatObject }>();
    ConfigManager.getInstance().downloadConfig('./fish/_totals.json', (data: FishData[]) => {
      for (let i = 0; i < data.length; i++) {
        const fishData = data[i];
        fishData.name = fishData.name || 'fish';
        fishData.show_range = fishData.show_range || [1000, 2000];
        fishData.bite_times = fishData.bite_times || 1;
        fishData.bite_range = fishData.bite_range || [500, 1000];
        fishData.success_range = fishData.success_range || [500, 700, 2000];
        fishData.bite_speed = fishData.bite_speed || 1;
        fishData.fish_scale = fishData.fish_scale || 1;
        fishData.fish_yOffset = fishData.fish_yOffset || 0;
        this.fishDataMap.set(fishData.id, {
          url: '',
          data: fishData,
          object: {
            tag: fishData.id,
            status: FishStatus.Hide,
            id: '',
          },
        });
      }
    });
  }

  static getInstance() {
    if (!FishConfig.instance) {
      FishConfig.instance = new FishConfig();
    }
    return FishConfig.instance;
  }

  getData(id: number, cb: (data: FishData) => void) {
    if (this.fishDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.fishDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      console.error('not found area config id: ' + id);
    }
  }

  getObject(id: number) {
    const item = this.fishDataMap.get(id);
    if (item) {
      return item.object;
    }
    return {
      tag: id,
      status: FishStatus.Hide,
      id: '',
    };
  }

  saveWaterMesh(mesh: THREE.Mesh) {
    this.waterMesh = mesh;
  }

  getWaterMesh() {
    return this.waterMesh;
  }

  randomFishId() {
    const ids = Array.from(this.fishDataMap.keys());
    return ids[Math.floor(Math.random() * ids.length)];
  }

  randomFishTimeObject(fishData: FishData) {
    //鱼出现动作总时长 过度到鱼静止
    const fishShowTime = 3000;

    const fishingTimeObject: {
      showTime: number;
      biteTimeList: {
        biteTime: number;
        successStartTime: number;
        successEndTime: number;
        biteEndTime: number;
      }[];
      totalTime: number;
    } = {
      showTime: Math.floor(
        Math.random() * (fishData.show_range[1] - fishData.show_range[0]) + fishData.show_range[0]
      ),
      biteTimeList: [],
      totalTime: 0,
    };
    let biteStartTime = fishingTimeObject.showTime + fishShowTime;
    for (let i = 0; i < fishData.bite_times; i++) {
      const randomBiteTime = Math.floor(
        Math.random() * (fishData.bite_range[1] - fishData.bite_range[0]) + fishData.bite_range[0]
      );
      const item = {
        biteTime: biteStartTime + randomBiteTime,
        successStartTime: biteStartTime + randomBiteTime + fishData.success_range[0],
        successEndTime: biteStartTime + randomBiteTime + fishData.success_range[1],
        biteEndTime: biteStartTime + randomBiteTime + fishData.success_range[2],
      };
      fishingTimeObject.biteTimeList.push(item);
      biteStartTime = item.biteEndTime;
    }
    fishingTimeObject.totalTime = biteStartTime + 500;

    return fishingTimeObject;
  }
}
