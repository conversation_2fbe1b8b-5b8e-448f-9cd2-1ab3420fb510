import { ConfigManager } from '@/game/Config/ConfigManager';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';

export type NpcChatData = {
  id: number;
  showRefreshTime: number;
  chatText: string;
  chatUrl: string;
  optionList: number[];
  chatAction: string;
  replayAction: number;
};

export class NpcChatConfig {
  private static instance: NpcChatConfig;

  private npcChatDataMap: Map<number, { url: string; data: NpcChatData }>;

  private constructor() {
    this.npcChatDataMap = new Map<number, { url: string; data: NpcChatData }>();
    ConfigManager.getInstance().downloadConfig('./npcChat/_totals.json', (data: NpcChatData[]) => {
      for (let i = 0; i < data.length; i++) {
        const npcChatData = data[i];
        npcChatData.showRefreshTime = npcChatData.showRefreshTime || 0;
        npcChatData.chatText = npcChatData.chatText || '';
        npcChatData.chatUrl = npcChatData.chatUrl || '';
        npcChatData.optionList = npcChatData.optionList || [0, 0, 0];
        npcChatData.chatAction = npcChatData.chatAction || 'Action_12';
        npcChatData.replayAction = npcChatData.replayAction || 0;
        this.npcChatDataMap.set(npcChatData.id, { url: '', data: npcChatData });
      }
    });
  }

  static getInstance() {
    if (!NpcChatConfig.instance) {
      NpcChatConfig.instance = new NpcChatConfig();
    }
    return NpcChatConfig.instance;
  }

  getData(id: number, cb: (data: NpcChatData) => void) {
    if (this.npcChatDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.npcChatDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      console.error('not found npc chat config id: ' + id);
    }
  }

  getWord(data: NpcChatData) {
    let content = data.chatText;
    const myPlayer = GetMyPlayer();
    if (data.showRefreshTime === 1 && myPlayer.refreshTimeStamp > 0) {
      //时间戳转本地时间
      const date = new Date(myPlayer.refreshTimeStamp);
      content += '<br><br>**Tool reset time: ' + date.toLocaleString() + '**';
    }
    return content;
  }
}
