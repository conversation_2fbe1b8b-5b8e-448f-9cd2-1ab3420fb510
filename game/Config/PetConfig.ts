import { ConfigManager } from '@/game/Config/ConfigManager';
import { Rarity } from '@/constant/enum';

export type PetData = {
  id: number;
  tag: string;
  glbUrl: string;
  iconUrl: string;
  iconColor: string;
  name: string;
  description: string;
  infoImageUrl: string;
};

export class PetConfig {
  private static instance: PetConfig;

  private petDataMap: Map<number, { url: string; data: PetData }>;
  private petDataTagMap: Map<string, PetData>;

  private constructor() {
    this.petDataMap = new Map<number, { url: string; data: PetData }>();
    this.petDataTagMap = new Map<string, PetData>();
    ConfigManager.getInstance().downloadConfig('./pet/_totals.json', (data: PetData[]) => {
      for (let i = 0; i < data.length; i++) {
        const petData = data[i];
        petData.tag = petData.tag || '';
        petData.glbUrl = petData.glbUrl || '';
        petData.iconUrl = petData.iconUrl || '';
        petData.iconColor = petData.iconColor || '';
        petData.name = petData.name || '';
        petData.description = petData.description || '';
        petData.infoImageUrl = petData.infoImageUrl || '';
        this.petDataTagMap.set(petData.tag, petData);
        this.petDataMap.set(petData.id, { url: '', data: petData });
      }
    });
  }

  static getInstance() {
    if (!PetConfig.instance) {
      PetConfig.instance = new PetConfig();
    }
    return PetConfig.instance;
  }

  getData(tag: string, cb: (data: PetData) => void) {
    if (this.petDataTagMap.size === 0) {
      setTimeout(() => {
        this.getData(tag, cb);
      }, 500);
      return;
    }

    const data = this.petDataTagMap.get(tag);
    if (data) {
      cb(data);
    } else {
      console.error('not found pet config tag: ' + tag);
    }
  }

  getHaloMeshLink(quantity: Rarity) {
    switch (quantity) {
      case Rarity.COMMON:
        return './particles/Light_pet_00.glb';
      case Rarity.UNCOMMON:
        return './particles/Light_pet_01.glb';
      case Rarity.RARE:
        return './particles/Light_pet_02.glb';
      case Rarity.EPIC:
        return './particles/Light_pet_03.glb';
      case Rarity.LEGENDARY:
        return './particles/Light_pet_04.glb';
      case Rarity.MYTHIC:
        return './particles/Light_pet_05.glb';
    }
    return './particles/Light_pet_00.glb';
  }

  // 白：#FFFFFF
  // 绿：#00A156
  // 蓝：#0184FF
  // 紫：#8E5DFF
  // 橙：#FFA600
  // 红：#FF3300
  getNameColor(quantity: Rarity) {
    switch (quantity) {
      case Rarity.COMMON:
        return '#FFFFFF';
      case Rarity.UNCOMMON:
        return '#00A156';
      case Rarity.RARE:
        return '#0184FF';
      case Rarity.EPIC:
        return '#8E5DFF';
      case Rarity.LEGENDARY:
        return '#FFA600';
      case Rarity.MYTHIC:
        return '#FF3300';
    }
    return '#FFFFFF';
  }
}
