import { ConfigManager } from '@/game/Config/ConfigManager';

export enum NpcChatOptionType {
  Twitter = 1,
  ReceiveTool,
  BuyEnergy,
  ActivityRule,
  SubmitResources,
  ShareTwitter,
  Donation,
  ClaimDrop,
  OpenSynthesis,
  JoinActivity,
  JumpLink,
  JumpChat = 1000, //跳转选项
}

export type NpcChatOptionData = {
  id: number;
  text: string;
  type: number;
  params: string[];
  clickDelay: number;
  iconUrl: string;
};

export class NpcChatOptionConfig {
  private static instance: NpcChatOptionConfig;

  private npcChatOptionDataMap: Map<number, { url: string; data: NpcChatOptionData }>;

  private constructor() {
    this.npcChatOptionDataMap = new Map<number, { url: string; data: NpcChatOptionData }>();
    ConfigManager.getInstance().downloadConfig(
      './npcChatOption/_totals.json',
      (data: NpcChatOptionData[]) => {
        for (let i = 0; i < data.length; i++) {
          const npcChatOptionData = data[i];
          npcChatOptionData.text = npcChatOptionData.text || '';
          npcChatOptionData.type = npcChatOptionData.type || NpcChatOptionType.Twitter;
          npcChatOptionData.params = npcChatOptionData.params || [];
          npcChatOptionData.clickDelay = npcChatOptionData.clickDelay || 1000;
          npcChatOptionData.iconUrl = npcChatOptionData.iconUrl || '';
          this.npcChatOptionDataMap.set(npcChatOptionData.id, { url: '', data: npcChatOptionData });
        }
      }
    );
  }

  static getInstance() {
    if (!NpcChatOptionConfig.instance) {
      NpcChatOptionConfig.instance = new NpcChatOptionConfig();
    }
    return NpcChatOptionConfig.instance;
  }

  getData(id: number, cb: (data: NpcChatOptionData) => void) {
    if (this.npcChatOptionDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.npcChatOptionDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      console.error('not found npc chat option config id: ' + id);
    }
  }

  getDataList(idList: number[], cb: (data: NpcChatOptionData[]) => void) {
    const dataMap = new Map<number, NpcChatOptionData>();
    idList.forEach((id) => {
      this.getData(id, (data) => {
        dataMap.set(id, data);
        if (dataMap.size === idList.length) {
          const dataList: NpcChatOptionData[] = [];
          idList.forEach((id) => {
            dataList.push(dataMap.get(id) as NpcChatOptionData);
          });
          cb(dataList);
        }
      });
    });
  }
}
