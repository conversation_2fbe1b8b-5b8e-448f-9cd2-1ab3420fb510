import { ConfigManager } from './ConfigManager';

export interface IPetShedConfigData {
  id: number;
  tag: string;
  affinity: string;
  //   affinity: 'forest' | 'water' | 'rock';
  position: number[];
  distance: number;
  yaw: number;
}

export class PetShedConfig {
  private static _instance: PetShedConfig;
  private _petShedConfigData: Map<number, IPetShedConfigData>;

  private constructor() {
    this._petShedConfigData = new Map<number, IPetShedConfigData>();
    ConfigManager.getInstance().downloadConfig(
      './petShed/_totals.json',
      (data: IPetShedConfigData[]) => {
        for (let i = 0; i < data.length; i++) {
          const petShedData = data[i];
          petShedData.tag = String(petShedData.id);
          petShedData.position = petShedData.position || [0, 0, 0];
          petShedData.affinity = petShedData.affinity || '';
          petShedData.distance = petShedData.distance || 0;
          this._petShedConfigData.set(petShedData.id, petShedData);
        }
      }
    );
  }

  public static getInstance(): PetShedConfig {
    if (!PetShedConfig._instance) {
      PetShedConfig._instance = new PetShedConfig();
    }
    return PetShedConfig._instance;
  }

  getData(id: number, cb: (data: IPetShedConfigData) => void) {
    if (this._petShedConfigData.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }
    const config = this._petShedConfigData.get(id);
    if (config) {
      cb(config);
    } else {
      console.error('not found pet shed config id: ' + id);
    }
  }

  getPetShedConfigList() {
    return Array.from(this._petShedConfigData.values());
  }
}
