import { ConfigManager } from '@/game/Config/ConfigManager';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';

export type IncentivesData = {
  id: number;
  tag: string;
  type: string;
  link: string;
};

export class IncentivesConfig {
  private static instance: IncentivesConfig;

  private actionDataMap: Map<number, { url: string; data: IncentivesData }>;
  private actionList: IncentivesData[] = [];

  private constructor() {
    this.actionDataMap = new Map<number, { url: string; data: IncentivesData }>();
    ConfigManager.getInstance().downloadConfig(
      './incentives/_totals.json',
      (data: IncentivesData[]) => {
        for (let i = 0; i < data.length; i++) {
          const incentivesData = data[i];
          this.actionDataMap.set(incentivesData.id, { url: '', data: incentivesData });
        }
      }
    );
  }

  static getInstance() {
    if (!IncentivesConfig.instance) {
      IncentivesConfig.instance = new IncentivesConfig();
    }
    return IncentivesConfig.instance;
  }

  getData(id: number, cb: (data: IncentivesData | null) => void) {
    if (this.actionDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.actionDataMap.get(id);
    if (config) {
      const data = config.data;
      if (data) {
        cb(data);
      } else {
        this.loadMapData(config, cb);
      }
    } else {
      if (id !== 0) {
        console.error('not found item config id: ' + id);
      }
      cb(null);
    }
  }

  getActionList(cb: (actionList: IncentivesData[]) => void) {
    if (this.actionDataMap.size === 0) {
      setTimeout(() => {
        this.getActionList(cb);
      }, 100);
      return;
    }
    if (this.actionList.length > 0) {
      cb(this.actionList);
      return;
    }
    this.actionDataMap.forEach((config) => {
      const cdnLink = getCdnLink(config.data.link);
      this.actionList.push({
        id: config.data.id,
        tag: config.data.tag,
        type: config.data.type,
        link: cdnLink,
      });
    });
    cb(this.actionList);
  }

  private loadMapData(
    config: {
      url: string;
      data: IncentivesData | undefined;
    },
    cb: (data: IncentivesData) => void
  ) {
    ConfigManager.getInstance().downloadConfig(config.url, (data) => {
      const actionData = data as IncentivesData;
      config.data = actionData;
      cb(actionData);
    });
  }
}
