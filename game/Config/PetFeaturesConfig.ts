import { ConfigManager } from '@/game/Config/ConfigManager';

export enum PetFeaturesType {
  FishingPole = 'FishingPole',
  Axe = 'Axe',
  Pickaxe = 'Pickaxe',
}

export type PetFeaturesData = {
  id: number;
  features: string;
  level: number;
  damage: number;
  interval: number;
  staminaConsume: number;
  spiritConsume: number;
};

export class PetFeaturesConfig {
  private static instance: PetFeaturesConfig;

  private petFeaturesDataMap: Map<number, { url: string; data: PetFeaturesData }>;
  private petDataTagMap: Map<string, PetFeaturesData>;

  private constructor() {
    this.petFeaturesDataMap = new Map<number, { url: string; data: PetFeaturesData }>();
    this.petDataTagMap = new Map<string, PetFeaturesData>();
    ConfigManager.getInstance().downloadConfig(
      './petFeatures/_totals.json',
      (data: PetFeaturesData[]) => {
        for (let i = 0; i < data.length; i++) {
          const petData = data[i];
          petData.features = petData.features || '';
          petData.level = petData.level || 0;
          petData.damage = petData.damage || 0;
          petData.interval = petData.interval || 0;
          petData.staminaConsume = petData.staminaConsume || 0;
          petData.spiritConsume = petData.spiritConsume || 0;
          this.petDataTagMap.set(`${petData.features}_${petData.level}`, petData);
          this.petFeaturesDataMap.set(petData.id, { url: '', data: petData });
        }
      }
    );
  }

  static getInstance() {
    if (!PetFeaturesConfig.instance) {
      PetFeaturesConfig.instance = new PetFeaturesConfig();
    }
    return PetFeaturesConfig.instance;
  }

  getData(features: string, level: number, cb: (data: PetFeaturesData) => void) {
    if (this.petDataTagMap.size === 0) {
      setTimeout(() => {
        this.getData(features, level, cb);
      }, 500);
      return;
    }
    const key = `${features}_${level}`;
    const data = this.petDataTagMap.get(key);
    if (data) {
      cb(data);
    } else {
      console.error('not found pet config key: ' + key);
    }
  }
}
