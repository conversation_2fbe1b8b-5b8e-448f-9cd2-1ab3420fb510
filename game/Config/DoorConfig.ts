import { ConfigManager } from '@/game/Config/ConfigManager';

export enum LoadingPageType {
  Default = 1,
  Machine = 2,
}

export type DoorData = {
  id: number;
  text: string;
  position: number[];
  distance: number;
  targetMapId: number;
  loadingType: LoadingPageType;
  noButton: number;
  targetPos: number[];
  targetCamPos: number[];
};

export class DoorConfig {
  private static instance: DoorConfig;

  private doorDataMap: Map<number, { url: string; data: DoorData }>;

  private constructor() {
    this.doorDataMap = new Map<number, { url: string; data: DoorData }>();
    ConfigManager.getInstance().downloadConfig('./door/_totals.json', (data: DoorData[]) => {
      for (let i = 0; i < data.length; i++) {
        const doorData = data[i];
        doorData.loadingType = doorData.loadingType || LoadingPageType.Default;
        doorData.noButton = doorData.noButton || 0;
        doorData.distance = doorData.distance || 1;
        this.doorDataMap.set(doorData.id, { url: '', data: doorData });
      }
    });
  }

  static getInstance() {
    if (!DoorConfig.instance) {
      DoorConfig.instance = new DoorConfig();
    }
    return DoorConfig.instance;
  }

  getData(id: number, cb: (data: DoorData) => void) {
    if (this.doorDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.doorDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      console.error('not found door config id: ' + id);
    }
  }
}
