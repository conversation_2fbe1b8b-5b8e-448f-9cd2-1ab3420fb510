import { ConfigManager } from '@/game/Config/ConfigManager';
import createUseGame from '@/game_lib/stores/useGame';
import { IAppState } from '@/constant/type';
import { game } from '@/game/Proto/generated/game_messages';

export type StoneData = {
  name: string;
  id: number;
  glb_url: string;
  position: number[];
  radius: number;
  range: number[];
  sector_range: number[];
  sector_range_start: number;
  sector_range_length: number;
  yawY: number;
  max_hp: number;
  disappear_time: number;
  hit_effect_scale: number;
  hit_effect_during: number;
  fall_effect_scale: number;
  fall_effect_during: number;
  fall_effect_delay: number;
};

export type StoneObject = {
  tag: number;
  hp: number;
  useGame: any;
  combo: number;
  id: string; //for server
  status: string; //for server
  score: number; //for server
  petUse: boolean;
};

export class StoneConfig {
  private static instance: StoneConfig;

  private stoneDataMap: Map<number, { url: string; data: StoneData; object: StoneObject }>;

  private constructor() {
    this.stoneDataMap = new Map<number, { url: string; data: StoneData; object: StoneObject }>();
    ConfigManager.getInstance().downloadConfig('./stone/_totals.json', (data: StoneData[]) => {
      for (let i = 0; i < data.length; i++) {
        const stoneData = data[i];
        stoneData.range = stoneData.range || [1, 2, 3, 5];
        stoneData.yawY = stoneData.yawY || 0;
        stoneData.radius = stoneData.radius || 1;
        stoneData.position = stoneData.position || [0, 0, 0];
        stoneData.disappear_time = stoneData.disappear_time || 1000;
        stoneData.hit_effect_scale = stoneData.hit_effect_scale || 0.2;
        stoneData.hit_effect_during = stoneData.hit_effect_during || 5000;
        stoneData.fall_effect_scale = stoneData.fall_effect_scale || 1;
        stoneData.fall_effect_during = stoneData.fall_effect_during || 5000;
        stoneData.fall_effect_delay = stoneData.fall_effect_delay || 500;
        stoneData.max_hp = stoneData.max_hp || 200;
        stoneData.sector_range = stoneData.sector_range || [0, 360];
        stoneData.sector_range_start = stoneData.sector_range[0] || 0;
        stoneData.sector_range_length = stoneData.sector_range[1] - stoneData.sector_range[0];
        if (stoneData.sector_range_start > 180)
          stoneData.sector_range_start = stoneData.sector_range_start - 360;
        if (stoneData.sector_range_start < -180)
          stoneData.sector_range_start = stoneData.sector_range_start + 360;
        this.stoneDataMap.set(stoneData.id, {
          url: '',
          data: stoneData,
          object: {
            tag: 0,
            id: '',
            status: 'dead',
            combo: 0,
            hp: 0,
            score: 0,
            useGame: createUseGame(),
            petUse: false,
          },
        });
      }
    });
  }

  static getInstance() {
    if (!StoneConfig.instance) {
      StoneConfig.instance = new StoneConfig();
    }
    return StoneConfig.instance;
  }

  getData(id: number, cb: (data: StoneData) => void) {
    if (this.stoneDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.stoneDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      console.error('not found stone config id: ' + id);
    }
  }

  getObject(id: number) {
    const config = this.stoneDataMap.get(id);
    if (config) {
      return config.object;
    } else {
      return {
        hp: 0,
        useGame: createUseGame(),
      } as StoneObject;
    }
  }

  updateRockList(rockList: game.IRockData[]) {
    if (this.stoneDataMap.size === 0) {
      setTimeout(() => {
        this.updateRockList(rockList);
      }, 500);
      return;
    }
    rockList.forEach((rock) => {
      const tag = Number(rock.tag);
      const object = this.getObject(tag);
      if (object) {
        this.getData(tag, (data) => {
          object.tag = tag;
          // TODO: rockList 也要打上serverId
          object.id = rock.rockServerId || '';
          object.status = rock.isAlive ? 'alive' : 'dead';
          if (object.status === 'alive') {
            object.hp = data.max_hp;
          }
        });
      }
    });
  }

  updateTreeData(rockList: IAppState['rockList']) {
    if (rockList) {
      for (let i = 0; i < rockList.length; i++) {
        const tree = rockList[i];
        const tag = Number(tree.tag);
        this.getData(tag, (data) => {
          const stoneObject = this.getObject(Number(tree.tag));
          if (stoneObject) {
            stoneObject.tag = tag;
            stoneObject.status = tree.status;
            stoneObject.id = tree.id;
            stoneObject.score = tree.score;
            if (stoneObject.status === 'alive') {
              stoneObject.hp = data.max_hp;
              stoneObject.petUse = false;
            }
          }
        });
      }
    }
  }

  getHitSound(stoneData: StoneData) {
    return './sound/chop/mine_stone_wave.mp3';
  }

  getFallSound(stoneData: StoneData) {
    switch (stoneData.max_hp) {
      case 50:
        return './sound/fall/stone_broken_small.mp3';
      case 100:
        return './sound/fall/stone_broken_medium.mp3';
      case 200:
        return './sound/fall/stone_broken_large.mp3';
      default:
        return './sound/fall/stone_broken_small.mp3';
    }
  }

  checkInSector(sector_range_start: number, sector_range_length: number, angle: number) {
    const yaw = (angle / Math.PI) * 180;
    if (yaw > sector_range_start && yaw < sector_range_start + sector_range_length) {
      return true;
    }
    if (
      yaw < sector_range_start &&
      yaw + 360 > sector_range_start &&
      yaw + 360 < sector_range_start + sector_range_length
    ) {
      return true;
    }
    return false;
  }

  getAliveStoneCount() {
    let count = 0;
    this.stoneDataMap.forEach((data) => {
      if (data.object.status === 'alive') {
        count++;
      }
    });
    return count;
  }

  getAliveStone() {
    const res = Array.from(this.stoneDataMap.values())
      .filter((item) => item.object.status === 'alive')
      .map((item) => item.object);

    return res;
  }

  getStoneDataMap() {
    return this.stoneDataMap;
  }
}
