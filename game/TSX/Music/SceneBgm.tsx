import { useThree } from '@react-three/fiber';
import { useEffect, useState } from 'react';
import { MapData } from '@/game/Config/MapConfig';
import { AudioListener } from 'three/src/audio/AudioListener';
import * as THREE from 'three';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import { useSelector } from 'react-redux';
import { ISettingState } from '@/constant/type';

let isFirst = true;

export default function SceneBgm({ mapData }: { mapData: MapData | null }) {
  const { camera } = useThree();
  const [bgmOpen, setBgmOpen] = useState<boolean>(false);
  const [bgmVolume, setBgmVolume] = useState<number>(0.2);
  const [audio, setAudio] = useState<any>(null);
  const { music, isPizzaActivity } = useSelector(
    (state: { SettingReducer: ISettingState }) => state.SettingReducer
  );

  useEffect(() => {
    // 创建音频监听器
    let listener = camera.userData.audioListener1 as AudioListener;
    if (!listener) {
      listener = new AudioListener();
      camera.userData.audioListener1 = listener;
      camera.add(listener);
    }
    const _audio = new THREE.Audio(listener);
    _audio.setLoop(true);
    _audio.setVolume(bgmVolume);
    setAudio(_audio);
  }, []);

  useEffect(() => {
    if (audio) {
      if (mapData) {
        const playBuffer = (buffer: AudioBuffer) => {
          if (audio.isPlaying) {
            audio.stop();
          }
          audio.setBuffer(buffer);
          audio.play();
        };

        const bgmUrl = isPizzaActivity ? './space/sounds/bgm_pizza_rush.mp3' : mapData.bgmUrl;
        if (isFirst) {
          const timeout = setTimeout(() => {
            LoaderUtil.loadAudio(bgmUrl, (buffer) => {
              playBuffer(buffer);
            });
          }, 10000);
          isFirst = false;
          return () => {
            clearTimeout(timeout);
            audio.stop();
          };
        }

        LoaderUtil.loadAudio(bgmUrl, (buffer) => {
          playBuffer(buffer);
        });
      }
      return () => {
        audio.stop();
      };
    }
  }, [mapData, audio, isPizzaActivity]);

  useEffect(() => {
    if (audio) {
      // 动态调整音量或其他参数
      const volume = bgmOpen ? bgmVolume : 0;
      audio.setVolume(volume);
    }
  }, [bgmOpen, bgmVolume, audio]);

  useEffect(() => {
    setBgmOpen(music);
  }, [music]);

  // useFrame(({ clock }) => {
  //   const oldMusicSwitch = localStorage.getItem('music') === 'true';
  //   setBgmOpen(oldMusicSwitch);
  //   const isPizzaActivity = localStorage.getItem('isPizzaActivity') === 'true';
  //   setIsPizzaActivity(isPizzaActivity);
  // });

  return null;
}
