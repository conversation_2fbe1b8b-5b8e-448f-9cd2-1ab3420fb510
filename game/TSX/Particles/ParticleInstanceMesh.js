import * as THREE from 'three';

const DEFAULT_TEXTURE_URL = '/particles/dot.png';
export default class ParticleInstanceMesh {
  constructor(parent, particleCount = 1000) {
    this.maxCount = particleCount;
    this.poolUseIndex = 0;
    this.camera = null; // 存储摄像机引用

    const particleGeometry = new THREE.PlaneGeometry(1, 1);
    // 尝试加载纹理，失败时使用程序化纹理
    const textureLoader = new THREE.TextureLoader();

    const texture = textureLoader.load(DEFAULT_TEXTURE_URL);
    // 自定义着色器材质以支持每个实例的透明度
    const particleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        map: { value: texture },
        globalOpacity: { value: 1 },
        cameraPosition: { value: new THREE.Vector3() },
      },
      vertexShader: `
        attribute float instanceAlpha;
        attribute vec3 instanceColor;
        varying vec3 vColor;
        varying float vAlpha;
        varying vec2 vUv;
        uniform vec3 cameraPosition;

        void main() {
          vColor = instanceColor;
          vAlpha = instanceAlpha;
          vUv = uv;

          // 获取实例的世界位置
          vec4 worldPosition = instanceMatrix * vec4(0.0, 0.0, 0.0, 1.0);

          // 计算从粒子到摄像机的方向向量
          vec3 toCameraDirection = normalize(cameraPosition - worldPosition.xyz);

          // 创建朝向摄像机的旋转矩阵
          vec3 up = vec3(0.0, 1.0, 0.0);
          vec3 right = normalize(cross(up, toCameraDirection));
          up = cross(toCameraDirection, right);

          // 构建billboard矩阵
          mat4 billboardMatrix = mat4(
            vec4(right, 0.0),
            vec4(up, 0.0),
            vec4(toCameraDirection, 0.0),
            vec4(worldPosition.xyz, 1.0)
          );

          // 应用billboard变换到顶点位置
          vec4 billboardPosition = billboardMatrix * vec4(position, 1.0);

          vec4 mvPosition = modelViewMatrix * billboardPosition;
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        uniform sampler2D map;
        uniform float globalOpacity;
        varying vec3 vColor;
        varying float vAlpha;
        varying vec2 vUv;

        void main() {
          vec4 texColor = texture2D(map, vUv);
          gl_FragColor = vec4(vColor * texColor.rgb, texColor.a * vAlpha * globalOpacity);
        }
      `,
      transparent: true,
      depthWrite: false,
      blending: THREE.AdditiveBlending,
      side: THREE.FrontSide,
    });
    this.instancedMesh = new THREE.InstancedMesh(particleGeometry, particleMaterial, particleCount);
    this.instancedMesh.frustumCulled = false;
    const colors = new Float32Array(particleCount * 3);
    // 初始化为白色而不是黑色
    for (let i = 0; i < particleCount * 3; i++) {
      colors[i] = 1.0;
    }
    this.instancedMesh.geometry.setAttribute(
      'instanceColor',
      new THREE.InstancedBufferAttribute(colors, 3)
    );

    // 添加实例化透明度属性
    const alphas = new Float32Array(particleCount);
    // 初始化透明度为1.0（完全不透明）
    for (let i = 0; i < particleCount; i++) {
      alphas[i] = 1.0;
    }
    this.instancedMesh.geometry.setAttribute(
      'instanceAlpha',
      new THREE.InstancedBufferAttribute(alphas, 1)
    );
    // this.instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
    const matrix = new THREE.Matrix4();
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();
    //初始所有实例不可见 。  scale = 0
    for (let i = 0; i < particleCount; i++) {
      matrix.compose(position, quaternion, scale);
      this.instancedMesh.setMatrixAt(i, matrix);
    }

    this.instancedMesh.instanceMatrix.needsUpdate = true;
    parent.add(this.instancedMesh);
  }

  createInstance() {
    this.poolUseIndex++;
    this.poolUseIndex = this.poolUseIndex % this.maxCount;
    return this.poolUseIndex;
  }

  updateInstance(instanceIndex, position, quaternion, scale = 1, radius = 1) {
    const matrix = new THREE.Matrix4();
    const scaleVec = new THREE.Vector3();
    scaleVec.set(scale * radius, scale * radius, scale * radius);
    matrix.compose(position, quaternion, scaleVec);
    this.instancedMesh.setMatrixAt(instanceIndex, matrix);
    this.instancedMesh.instanceMatrix.needsUpdate = true;
  }

  destroyInstance(instanceIndex) {
    //将实例变成不可见
    const matrix = new THREE.Matrix4();
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();
    matrix.compose(position, quaternion, scale);
    this.instancedMesh.setMatrixAt(instanceIndex, matrix);
    this.instancedMesh.instanceMatrix.needsUpdate = true;
  }

  // 按索引设置粒子颜色
  setParticleColorByIndex(index, color) {
    const threeColor = new THREE.Color(color.r, color.g, color.b);
    const colorAttribute = this.instancedMesh.geometry.getAttribute('instanceColor');
    if (colorAttribute) {
      colorAttribute.setXYZ(index, threeColor.r, threeColor.g, threeColor.b);
      colorAttribute.needsUpdate = true;
    }
  }

  // 按索引设置粒子透明度
  setParticleAlphaByIndex(index, alpha) {
    // 确保透明度值在0-1范围内
    const clampedAlpha = Math.max(0, Math.min(1, alpha));
    const alphaAttribute = this.instancedMesh.geometry.getAttribute('instanceAlpha');
    if (alphaAttribute) {
      alphaAttribute.setX(index, clampedAlpha);
      alphaAttribute.needsUpdate = true;
    }
  }

  changeMap(map) {
    const material = this.instancedMesh.material;
    material.uniforms.map = { value: map };
  }

  // 设置摄像机位置，用于billboard效果
  setCameraPosition(camera) {
    this.camera = camera;
    const material = this.instancedMesh.material;
    if (material.uniforms.cameraPosition) {
      material.uniforms.cameraPosition.value.copy(camera.position);
    }
  }

  // 更新摄像机位置（在渲染循环中调用）
  updateCameraPosition() {
    if (this.camera) {
      const material = this.instancedMesh.material;
      if (material.uniforms.cameraPosition) {
        material.uniforms.cameraPosition.value.copy(this.camera.position);
      }
    }
  }

  destroy() {
    this.instancedMesh.removeFromParent();
  }
}
