import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import ParticleNode from '@/game/TSX/Particles/ParticleNode';
import { ParticleEditJson } from '@/game/TSX/Particles/ParticleEditor';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';

export default function ParticleObject({ url, scale }: { url: string; scale: number }) {
  const groupRef = useRef<THREE.Group>(null);
  const clockRef = useRef<THREE.Clock>(new THREE.Clock());
  const [particleJson, setParticleJson] = React.useState<ParticleEditJson | null>(null);
  const [particleNode, setParticleNode] = React.useState<ParticleNode | null>(null);
  const { camera } = useThree();

  useEffect(() => {
    if (!url.includes('.json')) {
      console.error('url must include .json');
      return;
    }
    LoaderUtil.loadJson(url, (json) => {
      setParticleJson(json as any);
    });
    return () => {
      setParticleJson(null);
    };
  }, [url]);

  useEffect(() => {
    if (!groupRef.current || !particleJson) return;

    const group = groupRef.current;
    const isOnce = particleJson.minEmitTime === 0 && particleJson.maxEmitTime === 0;
    const defaultParticleNode = new ParticleNode(camera, isOnce);
    group.add(defaultParticleNode);
    defaultParticleNode.scale.set(scale, scale, scale);
    defaultParticleNode.name = 'particleNode';
    defaultParticleNode.initFromJson(particleJson, () => {
      setParticleNode(defaultParticleNode);
    });
    return () => {
      defaultParticleNode.destroy();
    };
  }, [particleJson, scale]);

  useFrame(() => {
    if (!particleNode) return;
    particleNode.update(clockRef.current.getDelta());
  });

  return <group ref={groupRef} />;
}
