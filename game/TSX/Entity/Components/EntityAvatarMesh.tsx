import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { GLTF } from 'three-stdlib';
import { EntityAvatarMesh } from '@/game/TS/Entity/Components/EntityAvatarMesh';
import { game } from '@/game/Proto/generated/game_messages';
import { AvatarUtil } from '@/AvatarOrdinalsBrowser/renderAvatar/AvatarUtil';
import { AvatarActionConfig } from '@/game/Config/AvatarActionConfig';
import AvatarData from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import AvatarAction from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Part/AvatarAction';
import { Entity } from '@/game/TS/Entity/Entity';

export default function EntityAvatarMeshTSX({
  children,
  avatarMesh,
  updateAnimations,
  entity,
}: {
  entity: Entity;
  avatarMesh: EntityAvatarMesh;
  updateAnimations: (group: THREE.Object3D, animations: THREE.AnimationClip[]) => void;
  children?: React.ReactNode;
}) {
  const groupRef = useRef<THREE.Group>(null);
  const [GLTF, setGLTF] = useState<GLTF | null>(null);
  const [avatarData, setAvatarData] = useState<game.IAvatarData | null>(null);
  const [delayActionList, setDelayActionList] = useState<AvatarAction[]>([]);
  const animationsRef = useRef<THREE.AnimationClip[]>([]);

  useEffect(() => {
    const updateMesh = () => {
      setAvatarData(avatarMesh.avatarData);
      if (groupRef.current) {
        groupRef.current.scale.set(avatarMesh.scale, avatarMesh.scale, avatarMesh.scale);
      }
      avatarMesh.optionMap.forEach((option) => {
        const optionType = option.messageList[0];
        switch (optionType) {
          case 'setVisible':
            const objectName = option.messageList[1];
            const visible = option.messageList[2] === '1';
            const object = GLTF && GLTF.scene.getObjectByName(objectName);
            if (object) {
              object.visible = visible;
            }
            break;
        }
      });
    };
    updateMesh();
    avatarMesh.registerUpdateCallback(updateMesh);
  }, [avatarMesh, GLTF]);

  useEffect(() => {
    delayActionList.forEach((action) => {
      action.load((clip: THREE.AnimationClip) => {
        animationsRef.current.push(clip);
      });
    });
  }, [GLTF, delayActionList]);

  useEffect(() => {
    if (!groupRef.current) {
      return;
    }
    if (!avatarData) {
      return;
    }
    const group = groupRef.current;
    let cancel = false;
    const tempAvatarData = new AvatarData();
    tempAvatarData.hatId = avatarData.hatId || '';
    tempAvatarData.glovesId = avatarData.glovesId || '';
    tempAvatarData.pantsId = avatarData.pantsId || '';
    tempAvatarData.shoesId = avatarData.shoesId || '';
    tempAvatarData.shirtId = avatarData.shirtId || '';
    tempAvatarData.shirtColor = avatarData.shirtColor || '';
    tempAvatarData.shirtTextureId = avatarData.shirtTextureId || '';

    AvatarUtil.getAvatarGLB(tempAvatarData, (gltf) => {
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          mesh.castShadow = true;
          mesh.receiveShadow = true;
        }
      });
      AvatarActionConfig.getInstance().getActionList((list, delayList) => {
        const totalAnimations: THREE.AnimationClip[] = [];
        let loadNumber = 0;
        if (list.length > 0) {
          for (let i = 0; i < list.length; i++) {
            const action = list[i];
            if (action) {
              action.load((clip: THREE.AnimationClip) => {
                loadNumber++;
                totalAnimations.push(clip);
                if (loadNumber == list.length) {
                  if (cancel) return;
                  //todo 未知问题 为了解决 头顶文字高度获取异常。 当玩家处于负坐标时
                  new THREE.Box3().setFromObject(gltf.scene);
                  //////////////////
                  group.add(gltf.scene);
                  gltf.animations.push(...totalAnimations);
                  setGLTF(gltf as any);
                  animationsRef.current = [...gltf.animations];
                  updateAnimations(gltf.scene, animationsRef.current);
                  setDelayActionList(delayList);
                  entity.childrenChange();
                }
              });
            }
          }
        }
      });
    });
    return () => {
      cancel = true;
      group.clear();
      entity.childrenChange();
    };
  }, [avatarData, updateAnimations, entity]);

  return <group ref={groupRef} name="avatar">{children}</group>;
}
