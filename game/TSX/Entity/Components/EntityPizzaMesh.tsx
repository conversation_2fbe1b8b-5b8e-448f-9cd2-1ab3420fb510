import { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { GLTF } from 'three-stdlib';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import { PizzaPointConfig } from '@/game/Config/PizzaPointConfig';
import { EntityPizzaMesh } from '@/game/TS/Entity/Components/EntityPizzaMesh';

export default function EntityPizzaMeshTSX({ pizzaMesh }: { pizzaMesh: EntityPizzaMesh }) {
  const groupRef = useRef<THREE.Group>(null);
  const [pizzaCount, setPizzaCount] = useState<number>(0);
  const [tick, setTick] = useState<string>('');
  const [gltf, setGLTF] = useState<GLTF | null>(null);
  const currentCount = useRef<number>(0);

  useEffect(() => {
    if (tick === '') {
      return;
    }

    LoaderUtil.loadGlb(PizzaPointConfig.getInstance().getPizzaBoxUrl(tick), (glb) => {
      glb.scene.traverse((child) => {
        if (child.name.includes('Effect')) {
          child.visible = false;
        }
      });
      setGLTF(glb);
    });
  }, [tick]);

  useEffect(() => {
    const addPizza = (targetCount: number) => {
      if (gltf && groupRef.current) {
        if (currentCount.current < targetCount) {
          const pizzaMesh = gltf.scene.clone();
          pizzaMesh.position.y = currentCount.current * 0.05 - 0.13;
          pizzaMesh.position.x = -0.5;
          groupRef.current.add(pizzaMesh);
          currentCount.current++;
          addPizza(targetCount);
        }
      }
    };
    if (gltf && pizzaCount > 0) {
      addPizza(pizzaCount);
    } else {
      if (groupRef.current) {
        groupRef.current.clear();
        currentCount.current = 0;
      }
    }
  }, [pizzaCount, gltf]);

  useEffect(() => {
    const update = () => {
      setPizzaCount(pizzaMesh.pizzaCount);
      setTick(pizzaMesh.tick);
    };
    pizzaMesh.registerUpdateCallback(update);
    return () => {
      pizzaMesh.registerUpdateCallback(() => undefined);
    };
  }, [pizzaMesh]);
  return <group ref={groupRef} />;
}
