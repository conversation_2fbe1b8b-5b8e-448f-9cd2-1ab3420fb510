import * as THREE from 'three';
import { useEffect, useState } from 'react';
import { EntityTopText } from '@/game/TS/Entity/Components/EntityTopText';
import TopNameUI from '@/game/TSX/SceneUI/TopNameUI';
import { POPOVER_HEIGHT } from '@/constant';

export default function EntityTopTextTSX({
  topText,
  root,
}: {
  topText: EntityTopText;
  root: THREE.Object3D;
}) {
  const [text, setText] = useState<string>('');
  const [textColor, setTextColor] = useState<string>('#FFFFFF');
  const [height, setHeight] = useState<number>(0);
  const [iconName, setIconName] = useState('');
  const [withBorder, setWithBorder] = useState(false);
  const [showPetOnlyStyle, setPetOnlyStyle] = useState(false);
  useEffect(() => {
    const update = () => {
      if (topText.hideMyself) {
        setText('');
        setTextColor('#FFFFFF');
        setIconName('');
        setWithBorder(false);
        return;
      }
      setIconName(topText.topIconName);
      setTextColor(topText.textColor);
      setText(topText.topText);
      setWithBorder(topText.withBorder);
      setPetOnlyStyle(Boolean(topText.showPetOnlyStyle));
    };
    update();
    topText.registerUpdateCallback(update);
    return () => {
      topText.registerUpdateCallback(() => undefined);
    };
  }, [topText]);

  useEffect(() => {
    const box = new THREE.Box3().setFromObject(root);
    // box 的 min 和 max 属性代表边界框的最小和最大坐标
    setHeight(Math.max(box.max.y - box.min.y + POPOVER_HEIGHT, 0));
  }, [root]);

  return (
    <>
      {text.length > 0 && (
        <TopNameUI
          height={height}
          name={text}
          textColor={textColor}
          topIconName={iconName}
          withBorder={withBorder}
          showPetOnlyStyle={showPetOnlyStyle}
        />
      )}
    </>
  );
}
