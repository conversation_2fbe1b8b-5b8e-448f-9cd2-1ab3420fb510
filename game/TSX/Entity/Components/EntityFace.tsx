import * as THREE from 'three';
import React, { useEffect, useState } from 'react';
import { EntityFace, EntityFaceType } from '@/game/TS/Entity/Components/EntityFace';
import { AvatarUtil } from '@/AvatarOrdinalsBrowser/renderAvatar/AvatarUtil';
import AvatarFace from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarFace';
import { useFrame } from '@react-three/fiber';

export default function EntityFaceTSX({
  curAnimation,
  face,
  root,
}: {
  curAnimation: string;
  root: THREE.Object3D;
  face: EntityFace;
}) {
  const [faceUrl, setFaceUrl] = React.useState<string>('');
  const [faceType, setFaceType] = React.useState<EntityFaceType>('avatar');
  const [faceMeshName, setFaceMeshName] = React.useState<string>('');
  const [faceController, setFaceController] = useState<AvatarFace | null>(null);
  let lastTime = Date.now();

  useEffect(() => {
    const updateTransform = () => {
      setFaceUrl(face.faceUrl);
      setFaceType(face.faceType);
      setFaceMeshName(face.faceMeshName);
    };
    updateTransform();
    face.registerUpdateCallback(updateTransform);
    return () => {
      face.registerUpdateCallback(() => undefined);
    };
  }, [face]);

  useEffect(() => {
    const faceMesh = root.getObjectByName(faceMeshName) as THREE.SkinnedMesh;
    if (faceMesh) {
      switch (faceType) {
        case 'avatar':
          setFaceController(AvatarUtil.createAvatarFace(faceMesh));
          break;
        case 'pet':
          setFaceController(AvatarUtil.createPetFace(faceMesh));
          break;
        case 'npc':
        default:
          setFaceController(AvatarUtil.createNpcFace(faceMesh, faceUrl));
          break;
      }
    }
  }, [root, faceUrl, faceMeshName, faceType]);
  useEffect(() => {
    if (faceController) {
      if (faceType === 'avatar') {
        switch (curAnimation) {
          case 'Action_05':
          case 'Action_06':
          case 'Action_07':
          case 'Action_12':
            faceController.setActionName('speak');
            break;
          case 'Action_33':
          case 'Action_34':
          case 'Action_35':
          case 'Action_36':
          case 'Action_37':
          case 'Action_38':
            faceController.setActionName('weary');
            break;
          default:
            faceController.setActionName('idle');
            break;
        }
      } else {
        faceController.setActionName('idle');
      }
    }
  }, [faceType, faceController, curAnimation]);

  useFrame(() => {
    const now = Date.now();
    const delta = now - lastTime;
    lastTime = now;
    if (faceController) {
      faceController.update(delta / 1000);
    }
  });
  return null;
}
