import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { EntityCollider } from '@/game/TS/Entity/Components/EntityCollider';
import { RapierRigidBody, RigidBody } from '@react-three/rapier';

export default function EntityColliderTSX({
  collider,
  root,
}: {
  collider: EntityCollider;
  root: THREE.Object3D;
}) {
  const [mesh, setMesh] = useState<THREE.Mesh | null>(null);
  const refBody = useRef<RapierRigidBody>(null);
  useEffect(() => {
    const update = () => {
      const colliderMesh = root.getObjectByName(collider.meshName) as THREE.Mesh;
      if (colliderMesh) {
        setTimeout(() => {
          setMesh(colliderMesh as THREE.Mesh);
        }, 1000);
      } else {
        setMesh(null);
      }
    };
    update();
    collider.registerUpdateCallback(update);
    return () => {
      collider.registerUpdateCallback(() => undefined);
    };
  }, [collider]);

  useEffect(() => {
    if (refBody.current && mesh) {
      const body = refBody.current;
      const target = body.translation();
      const timerList: any[] = [];
      for (let i = 0; i < 10; i++) {
        const offsetY = Math.min(0, -2 + i * 0.3);
        timerList.push(
          setTimeout(() => {
            body.setTranslation(
              new THREE.Vector3(target.x, target.y + offsetY, target.z),
              false
            );
          }, i * 50)
        );
        if (offsetY == 0) {
          break;
        }
      }
      return () => {
        timerList.forEach((timer) => {
          clearTimeout(timer);
        });
      };
    }
  }, [mesh]);

  return (
    <group dispose={null}>
      {mesh && (
        <RigidBody type="fixed" colliders="trimesh" ref={refBody} ccd>
          <mesh
            geometry={mesh.geometry}
            material={mesh.material}
            castShadow={false}
            receiveShadow={false}
            position={[mesh.position.x, mesh.position.y, mesh.position.z]}
            rotation={[mesh.rotation.x, mesh.rotation.y, mesh.rotation.z]}
            scale={mesh.scale}
            userData={{ camExcludeCollision: true }}
          />
        </RigidBody>
      )}
    </group>
  );
}
