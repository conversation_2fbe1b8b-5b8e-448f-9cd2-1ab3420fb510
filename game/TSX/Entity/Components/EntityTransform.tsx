import * as THREE from 'three';
import { Quaternion, Vector3 } from 'three';
import React, { useEffect, useRef } from 'react';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { useFrame } from '@react-three/fiber';

export default function EntityTransformTSX({
  children,
  transform,
}: {
  transform: EntityTransform;
  children: React.ReactNode;
}) {
  const groupRef = useRef<THREE.Group>(null);
  const childrenRef = useRef<THREE.Group>(null);
  const [rootObject, setRootObject] = React.useState<THREE.Object3D | null>(null);

  const targetPositionRef = useRef(new Vector3()); // 使用 useRef 代替 state
  const targetQuaternionRef = useRef(new Quaternion());
  const rotationFactor = 0.1; // 距离系数，尝试调整这个值
  const smoothFactor = 0.25; // 插值系数，尝试调整这个值

  useEffect(() => {
    if (childrenRef.current && groupRef.current && rootObject) {
      const rootGroup = groupRef.current;
      const childrenGroup = childrenRef.current;
      rootObject.add(childrenGroup);
      return () => {
        rootGroup.add(childrenGroup);
      };
    }
  }, [rootObject]);

  useEffect(() => {
    const updateTransform = () => {
      if (transform.rootObject) {
        setRootObject(transform.rootObject);
      }
      // console.log('updateTransform', transform.position);
      targetPositionRef.current.set(
        transform.position.x,
        transform.position.y,
        transform.position.z
      );
      targetQuaternionRef.current.set(
        transform.rotation.x,
        transform.rotation.y,
        transform.rotation.z,
        transform.rotation.w
      );
      if (groupRef.current) {
        groupRef.current.scale.set(transform.scale, transform.scale, transform.scale);
      }
    };
    updateTransform();
    transform.registerUpdateCallback(updateTransform);
    return () => {
      transform.registerUpdateCallback(() => undefined);
    };
  }, [transform]);

  useFrame(() => {
    if (rootObject) {
      rootObject.getWorldPosition(transform.position);
      rootObject.getWorldQuaternion(transform.rotation);
      transform.setPosition(transform.position, transform.rotation);
    }
    if (groupRef.current) {
      const currentPos = groupRef.current.position;
      const currentQuat = groupRef.current.quaternion;
      if (currentPos.distanceTo(targetPositionRef.current) > 3) {
        currentPos.copy(targetPositionRef.current);
        currentQuat.copy(targetQuaternionRef.current);
      } else {
        // 插值平滑位置
        currentPos.lerp(targetPositionRef.current, smoothFactor);

        // 使用 copy 方法复制目标四元数
        currentQuat.slerp(targetQuaternionRef.current, rotationFactor);
      }
    }
  });
  return (
    <group ref={groupRef} name={'transform'}>
      <group ref={childrenRef}>{children}</group>
    </group>
  );
}
