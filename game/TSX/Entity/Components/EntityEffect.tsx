import React, { useEffect } from 'react';
import { EntityEffect } from '@/game/TS/Entity/Components/EntityEffect';
import ParticleObject from '@/game/TSX/Particles/ParticleObject';

export default function EntityEffectTSX({ effect }: { effect: EntityEffect }) {
  const [effectUrl, setEffectUrl] = React.useState<string>('');
  const [effectScale, setEffectScale] = React.useState<number>(1);
  useEffect(() => {
    const updateTransform = () => {
      setEffectUrl(effect.effectUrl);
      setEffectScale(effect.effectScale);
    };
    updateTransform();
    effect.registerUpdateCallback(updateTransform);
    return () => {
      effect.registerUpdateCallback(() => undefined);
    };
  }, [effect]);

  return (
    <>{effectUrl.length > 0 && <ParticleObject url={effectUrl} scale={effectScale} />}</>
  );
}
