import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { Entity } from '@/game/TS/Entity/Entity';

export default function EntityFollowBoneTSX({
  children,
  followBone,
}: {
  followBone: EntityFollowBone;
  children: React.ReactNode;
}) {
  const rootRef = useRef<THREE.Group>(null);
  const offsetRef = useRef<THREE.Group>(null);
  const [followObject, setFollowObject] = useState<THREE.Object3D | null>(null);
  const [targetEntity, setTargetEntity] = useState<Entity | null>(null);
  const [targetBoneName, setTargetBoneName] = useState<string>('');

  useEffect(() => {
    if (targetEntity && targetBoneName.length > 0) {
      const childrenChange = () => {
        const bone = targetEntity.findBone(targetBoneName);
        bone && setFollowObject(bone);
      };
      targetEntity.registerRootChange(childrenChange);
      return () => {
        targetEntity.unregisterRootChange(childrenChange);
      };
    }
  }, [targetEntity, targetBoneName]);

  useEffect(() => {
    const updateTransform = () => {
      setTargetBoneName(followBone.targetBoneName);
      if (followBone.targetClientIndex > 0) {
        const clientId = `client_${followBone.targetClientIndex}`;
        const entity = EntityManager.getInstance().getClientEntity(clientId);
        setTargetEntity(entity || null);
      } else {
        const entity = EntityManager.getInstance().getEntity(followBone.targetServerId);
        setTargetEntity(entity || null);
      }
      if (offsetRef.current) {
        offsetRef.current.position.set(
          followBone.position.x,
          followBone.position.y,
          followBone.position.z
        );
        offsetRef.current.quaternion.set(
          followBone.rotation.x,
          followBone.rotation.y,
          followBone.rotation.z,
          followBone.rotation.w
        );
      }
    };
    updateTransform();
    followBone.registerUpdateCallback(updateTransform);
    return () => {
      followBone.registerUpdateCallback(() => undefined);
    };
  }, [followBone]);

  useFrame(() => {
    if (followObject && rootRef.current) {
      followObject.getWorldPosition(rootRef.current.position);
      followObject.getWorldQuaternion(rootRef.current.quaternion);
    }
  });
  return (
    <group ref={rootRef} name={'transform'}>
      <group ref={offsetRef}>{children}</group>
    </group>
  );
}
