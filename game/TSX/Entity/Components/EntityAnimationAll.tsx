import * as THREE from 'three';
import React, { useEffect } from 'react';
import { useAnimations } from '@react-three/drei';
import { EntityAnimationAll } from '@/game/TS/Entity/Components/EntityAnimationAll';

function IdleAnimation({
  animations,
  group,
}: {
  animations: THREE.AnimationClip[];
  group: THREE.Object3D;
}) {
  /**
   * Character animations setup
   */
  const { actions } = useAnimations(animations, group);

  useEffect(() => {
    for (const argumentsKey in actions) {
      const action = actions[argumentsKey];
      if (action) {
        action.reset().fadeIn(0).setLoop(THREE.LoopRepeat, Infinity).play();
      }
    }
  }, [actions]);

  return null;
}

export default function EntityAnimationAllTSX({
  animations,
  animationAll,
  root,
}: {
  root: THREE.Object3D;
  animationAll: EntityAnimationAll;
  animations: THREE.AnimationClip[];
  children?: React.ReactNode;
}) {
  useEffect(() => {
    const update = () => undefined;
    update();
    animationAll.registerUpdateCallback(update);
    return () => {
      animationAll.registerUpdateCallback(() => undefined);
    };
  }, [animationAll]);
  return (
    <>
      <IdleAnimation group={root} animations={animations} />
    </>
  );
}
