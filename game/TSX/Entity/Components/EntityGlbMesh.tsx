import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import { GLTF } from 'three-stdlib';
import { Entity } from '@/game/TS/Entity/Entity';

export default function EntityGlbMeshTSX({
  children,
  entity,
  glbMesh,
  updateAnimations,
}: {
  entity: Entity;
  glbMesh: EntityGlbMesh;
  updateAnimations: (group: THREE.Object3D, animations: THREE.AnimationClip[]) => void;
  children?: React.ReactNode;
}) {
  const groupRef = useRef<THREE.Group>(null);
  const [GLTF, setGLTF] = useState<GLTF | null>(null);
  const [glbUrl, setGlbUrl] = useState<string>('');

  useEffect(() => {
    const updateMesh = () => {
      setGlbUrl(glbMesh.glbUrl);
      if (groupRef.current) {
        groupRef.current.scale.set(glbMesh.glbScale, glbMesh.glbScale, glbMesh.glbScale);
        groupRef.current.quaternion.setFromEuler(
          new THREE.Euler(0, (Math.PI * glbMesh.yawOffset) / 180, 0)
        );
      }
      glbMesh.optionMap.forEach((option) => {
        const optionType = option.messageList[0];
        const objectName = option.messageList[1];
        const visible = option.messageList[2] === '1';
        switch (optionType) {
          case 'setVisible':
            const objectVisible = GLTF && GLTF.scene.getObjectByName(objectName);
            if (objectVisible) {
              objectVisible.visible = visible;
            }
            break;
          case 'setShadow':
            const objectShadow = GLTF && GLTF.scene.getObjectByName(objectName);
            if (objectShadow) {
              objectShadow.castShadow = visible;
              objectShadow.receiveShadow = visible;
            }
        }
      });
    };
    updateMesh();
    glbMesh.registerUpdateCallback(updateMesh);
  }, [glbMesh, GLTF]);

  useEffect(() => {
    if (!groupRef.current) {
      return;
    }
    if (glbUrl.length === 0) {
      return;
    }
    const group = groupRef.current;
    let cancel = false;
    LoaderUtil.loadGlb(glbUrl, (gltf) => {
      if (cancel) {
        return;
      }
      setGLTF(gltf);
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          mesh.castShadow = true;
          mesh.receiveShadow = true;
        }
      });
      group.add(gltf.scene);
      updateAnimations(gltf.scene, gltf.animations);
      entity.childrenChange();
    });
    return () => {
      cancel = true;
      group.clear();
      entity.childrenChange();
    };
  }, [glbUrl, updateAnimations, entity]);

  return <group ref={groupRef}>{children}</group>;
}
