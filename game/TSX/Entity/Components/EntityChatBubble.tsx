import * as THREE from 'three';
import React, { useEffect, useMemo, useState } from 'react';
import { EntityChatBubble } from '@/game/TS/Entity/Components/EntityChatBubble';
import SpeakUtil from '@/game/TSX/Util/SpeakUtil';
import { POPOVER_HEIGHT } from '@/constant';
import TopAnswerUI, { AnswerTask } from '@/game/TSX/SceneUI/TopAnswerUI';
import AudioSystemComponent from '@/game/Global/GlobalAudioSystem';

export default function EntityChatBubbleTSX({
  chatBubble,
  root,
}: {
  root: THREE.Object3D;
  chatBubble: EntityChatBubble;
}) {
  const [chatWord, setChatWord] = React.useState('');
  const [endTime, setEndTime] = React.useState(0);
  const [height, setHeight] = useState<number>(0);
  const [answerTask, setAnswerTask] = useState<AnswerTask | null>(null);
  const speakUtil = useMemo(() => {
    return chatBubble.speakUtil;
  }, [chatBubble]);
  useEffect(() => {
    const updateTransform = () => {
      setChatWord(chatBubble.chatWord);
      setEndTime(chatBubble.endTime);
      setAnswerTask(chatBubble.answerTask);
    };
    updateTransform();
    chatBubble.registerUpdateCallback(updateTransform);
    return () => {
      chatBubble.registerUpdateCallback(() => undefined);
    };
  }, [chatBubble]);

  useEffect(() => {
    const box = new THREE.Box3().setFromObject(root);
    // box 的 min 和 max 属性代表边界框的最小和最大坐标
    setHeight(Math.max(box.max.y - box.min.y + POPOVER_HEIGHT, 0));
  }, [root]);

  useEffect(() => {
    const now = Date.now();
    if (now < endTime) {
      speakUtil.wordSpeak(
        chatWord,
        () => undefined,
        () => undefined
      );
    }
  }, [speakUtil, chatWord, endTime]);

  return (
    <>
      <AudioSystemComponent _key={chatBubble.audioKey} />
      <SpeakUtil spaceUtilData={speakUtil} height={height} />
      {answerTask && <TopAnswerUI height={height} task={answerTask} />}
    </>
  );
}
