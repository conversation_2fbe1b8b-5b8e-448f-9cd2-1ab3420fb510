import * as THREE from 'three';
import React, { useEffect, useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';

const SPEED = 4;

class JumpPhysics {
  private gravity: number;
  private velocity: number;
  private start: THREE.Vector3;
  private end: THREE.Vector3;
  totalTime = 0;

  constructor(
    startPosition: THREE.Vector3,
    endPosition: THREE.Vector3,
    velocity = 5,
    gravity = -9.8
  ) {
    this.gravity = gravity;
    this.start = startPosition.clone();
    this.end = endPosition.clone();
    this.velocity = velocity;

    const times = this.calculateTimeToReachY(this.end.y);
    if (times.length > 0) {
      this.totalTime = times[times.length - 1];
    }
  }

  // 计算到达目标 y 坐标的时间
  private calculateTimeToReachY(targetY: number): number[] {
    const a = 0.5 * this.gravity;
    const b = this.velocity;
    const c = this.start.y - targetY;

    const discriminant = b * b - 4 * a * c;
    if (discriminant < 0) return [];

    const sqrtDiscriminant = Math.sqrt(discriminant);
    const times = [(-b + sqrtDiscriminant) / (2 * a), (-b - sqrtDiscriminant) / (2 * a)];

    return times.filter((t) => t >= 0).sort((t1, t2) => t1 - t2);
  }

  // 计算当前 y 坐标
  calculatePosition(time: number) {
    if (this.totalTime <= 0) {
      console.error('JumpPhysics: Total time is zero');
      return this.start.clone();
    }
    time = Math.min(time, this.totalTime);
    const x = this.start.x + (this.end.x - this.start.x) * (time / this.totalTime);
    const z = this.start.z + (this.end.z - this.start.z) * (time / this.totalTime);
    const y = this.start.y + this.velocity * time + 0.5 * this.gravity * time * time;
    return new THREE.Vector3(x, y, z);
  }
}

export default function EntityWalkPointTSX({
  children,
  walkPoint,
}: {
  walkPoint: EntityWalkPoint;
  children: React.ReactNode;
}) {
  const groupRef = useRef<THREE.Group>(null);
  const targetPositionRef = useRef(new THREE.Vector3()); // 使用 useRef 代替 state
  const jumpPhysicsRef = useRef<JumpPhysics | null>(null);
  const lastTime = useRef(0);
  const jumpTime = useRef(0);
  useEffect(() => {
    const updateTransform = () => {
      targetPositionRef.current.set(walkPoint.point.x, walkPoint.point.y, walkPoint.point.z);

      lastTime.current = Date.now();
      if (groupRef.current) {
        if (walkPoint.jumpVelocity != 0) {
          jumpTime.current = 0;
          jumpPhysicsRef.current = new JumpPhysics(
            groupRef.current.position,
            walkPoint.point,
            walkPoint.jumpVelocity
          );
          walkPoint.jumpVelocity = 0;
        } else {
          jumpPhysicsRef.current = null;
        }
        if (walkPoint.facePoint) {
          groupRef.current.lookAt(
            new THREE.Vector3(
              walkPoint.facePoint.x,
              groupRef.current.position.y,
              walkPoint.facePoint.z
            )
          );
          walkPoint.facePoint = null;
        } else {
          walkPoint.position = groupRef.current.position;
          walkPoint.quaternion = groupRef.current.quaternion;
          groupRef.current.lookAt(
            new THREE.Vector3(walkPoint.point.x, groupRef.current.position.y, walkPoint.point.z)
          );
        }
      }
    };
    updateTransform();
    walkPoint.registerUpdateCallback(updateTransform);
    return () => {
      walkPoint.registerUpdateCallback(() => undefined);
    };
  }, [walkPoint]);

  useFrame(() => {
    if (groupRef.current) {
      const now = Date.now();
      if (lastTime.current === 0) {
        lastTime.current = now;
      }
      const dt = (now - lastTime.current) / 1000;
      lastTime.current = now;
      const entityPosition = groupRef.current.position;

      if (jumpPhysicsRef.current) {
        jumpTime.current += dt;
        //一秒内完成
        const jumpPosition = jumpPhysicsRef.current.calculatePosition(jumpTime.current);
        // console.log('progress.current', progress.current / 2, dt, jumpPosition);
        entityPosition.copy(jumpPosition);
        if (jumpTime.current >= jumpPhysicsRef.current.totalTime) {
          jumpPhysicsRef.current = null;
          walkPoint.arrive(false);
        }
      } else {
        const targetPosition = targetPositionRef.current;
        // eslint-disable-next-line @react-three/no-clone-in-loop
        const velocity = targetPosition.clone().sub(entityPosition);
        // console.log('velocity', velocity, targetPosition, entityPosition, velocity.lengthSq());
        if (velocity.lengthSq() > 25 * 25) {
          entityPosition.copy(targetPosition);
          walkPoint.arrive(true);
        } else if (velocity.lengthSq() > 0.2 * 0.2) {
          // console.log('velocity.lengthSq()', velocity.lengthSq());
          velocity.normalize();
          // Move player to target
          entityPosition.add(velocity.multiplyScalar(dt * SPEED));
        } else {
          walkPoint.arrive(false);
        }
      }
    }
  });
  return (
    <group ref={groupRef} name={'transform'}>
      {children}
    </group>
  );
}
