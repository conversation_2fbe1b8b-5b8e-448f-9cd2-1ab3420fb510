import { useEffect, useState } from 'react';
import { EntityListener } from '@/game/TS/Entity/EntityListener';
import { EntityEvent } from '@/game/TS/Entity/Enum';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { EntityTSX } from '@/game/TSX/Entity/Entity';
import { useSelector } from 'react-redux';
import { ISettingState } from '@/constant/type';
import { NetPlayerManager } from '@/game/TS/NetPlayer/NetPlayerManager';

export default function EntityManagerTSX() {
  const [entityList, setEntityList] = useState<Entity[]>([]);
  const { maxPlayers } = useSelector(
    (state: { SettingReducer: ISettingState }) => state.SettingReducer
  );

  useEffect(() => {
    NetPlayerManager.getInstance().checkPlayerDistance(maxPlayers);
    const interval = setInterval(() => {
      NetPlayerManager.getInstance().checkPlayerDistance(maxPlayers);
    }, 5 * 1000);
    return () => {
      clearInterval(interval);
    };
  }, [maxPlayers]);

  useEffect(() => {
    const entityUpdate = () => {
      //update
      setEntityList(EntityManager.getInstance().getEntityList());
    };

    EntityManager.getInstance().init();
    EntityListener.getInstance().addListener(EntityEvent.AddEntity, entityUpdate);
    EntityListener.getInstance().addListener(EntityEvent.RemoveEntity, entityUpdate);
    return () => {
      EntityListener.getInstance().removeListener(EntityEvent.AddEntity, entityUpdate);
      EntityListener.getInstance().removeListener(EntityEvent.RemoveEntity, entityUpdate);
    };
  }, []);
  return (
    <>
      {entityList.map((entity) => {
        const key = entity.clientId.length > 0 ? entity.clientId : 'server_' + entity.entityId;
        return <EntityTSX key={key} entity={entity} />;
      })}
    </>
  );
}
