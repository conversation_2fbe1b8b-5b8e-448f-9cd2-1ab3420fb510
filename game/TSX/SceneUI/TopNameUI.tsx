import { Html } from '@react-three/drei';
import React from 'react';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';

import styled, { css } from 'styled-components';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';

const UIBox = styled.div`
  .bubble-container {
    position: relative;
    bottom: 0;
    pointer-events: none; /* 使鼠标事件穿透 */
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    gap: 16px;
  }
`;

const StyledBubbleText = styled.div<{ $withBorder?: boolean; $showPetOnlyStyle?: boolean }>`
  position: relative;
  max-width: 900px;
  color: #ffffff;
  font-size: 35px;
  word-wrap: break-word;
  white-space: normal;
  user-select: none;
  pointer-events: none;
  ${({ $showPetOnlyStyle = false }) =>
    $showPetOnlyStyle &&
    css`
      filter: drop-shadow(1px 3px 1px black);
    `}
  ${({ $withBorder = false }) =>
    $withBorder &&
    css`
      border: 4px solid currentColor;
      padding: 0 16px;
      border-radius: 32px;
      filter: drop-shadow(1px 3px 1px black);
    `}
`;

const StyledAffinityTag = styled(SvgWrapper)`
  width: 54px;
  height: 54px;
`;

export default function TopNameUI({
  textColor,
  height,
  name,
  topIconName,
  withBorder,
  showPetOnlyStyle,
}: {
  textColor: string;
  height: number;
  name: string;
  topIconName?: string;
  withBorder?: boolean;
  showPetOnlyStyle?: boolean;
}) {
  const ref = React.useRef<THREE.Group>(null);
  // 在每一帧更新时，让元素朝向摄像机
  useFrame(({ camera }) => {
    if (ref.current) {
      // 只计算水平方向的朝向
      const cameraWorldPos = new THREE.Vector3();
      camera.getWorldPosition(cameraWorldPos);
      const objWorldPos = new THREE.Vector3();
      ref.current.getWorldPosition(objWorldPos);
      cameraWorldPos.y = objWorldPos.y;
      ref.current.lookAt(cameraWorldPos);
    }
  });
  return (
    <group ref={ref} position={[0, height, 0]}>
      {name.length > 0 && (
        <Html
          distanceFactor={1} // 让它始终保持固定的大小，不随摄像机远近变化
          transform // 保证HTML内容的大小不随距离变化
          // occlude  // 确保 HTML 元素不会被 3D 场景中的物体遮挡
          pointerEvents="none" // 禁用鼠标事件
          position={[0, 0, 0]}
          style={{
            transformOrigin: 'bottom center',
            transform: 'translate(0,-50%)',
            transition: 'all 0.3s ease',
            left: '50%',
          }}
          center={false}>
          <UIBox>
            <div className="bubble-container">
              {topIconName && (
                <StyledAffinityTag>
                  <SpriteSvg id={topIconName} />
                </StyledAffinityTag>
              )}
              <StyledBubbleText
                className="bubble-text"
                style={{ color: textColor }}
                $withBorder={withBorder}
                $showPetOnlyStyle={showPetOnlyStyle}>
                {name}
              </StyledBubbleText>
            </div>
          </UIBox>
        </Html>
      )}
    </group>
  );
}
