/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import React from 'react';
import { ButlerData, ButlerTSX, ButlerUtil } from '@/game/Global/GlobalButlerUtil';
import { useThree } from '@react-three/fiber';

interface ButlerModelProps {
  butlerData: ButlerData | null;
}

export default function ButlerModel({ butlerData }: ButlerModelProps) {
  const { scene } = useThree();
  ButlerUtil.initButlerData(butlerData, scene);

  return <ButlerTSX />;
}
