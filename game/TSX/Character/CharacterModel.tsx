/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import AvatarObject from '../../../AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarObject';
import { useThree } from '@react-three/fiber';
import GlobalSpaceEvent, { CharacterType, GlobalDataKey, SpaceStatus } from '@/game/Global/GlobalSpaceEvent';
import { GetMyPlayer, MyPlayerElement } from '@/game/TSX/Character/MyPlayer';
import TransformTools from '@/game/TSX/Scene/TransformTools';
import { preloadAudio, preloadItemGlb } from '@/game/TSX/Util/LoaderUtil';
import CustomOrbitControls from '@/game/CustomOrbitControls';

function SetCamera() {
  const { camera, gl } = useThree();
  const [isMobileFlip, setIsMobileFlip] = useState(false);

  useEffect(() => {
    const unsubscribe = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.IsMobileFlip,
      (value) => {
        setIsMobileFlip(value);
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.IsMobileFlip, unsubscribe);
    };
  }, []);

  useEffect(() => {
    camera.position.set(2, 1.5, 2);
    // camera.up.set(0, 0, 1);
    const controls = new CustomOrbitControls(camera, gl.domElement, isMobileFlip);
    controls.target = new THREE.Vector3(0, 1.0, 0);
    controls.minDistance = 1;
    controls.maxDistance = 4;
    controls.maxPolarAngle = (Math.PI / 20) * 11;
    controls.update();
    // // scene.add(controls);
    //
    GlobalSpaceEvent.SetDataValue(GlobalDataKey.EditorControls, controls);
    return () => {
      controls.dispose();
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.EditorControls, null);
    };
  }, [camera, gl, isMobileFlip]);
  return null;
}

function PlayerModel() {
  const myPlayer = GetMyPlayer();
  preloadItemGlb();
  preloadAudio();
  return <MyPlayerElement myPlayer={myPlayer} />;
}

export default function Model({ avatarObj }: CharacterModelProps) {
  const groupRef = useRef<THREE.Group>(null);
  const [status, setStatus] = useState<SpaceStatus>(SpaceStatus.Avatar);
  const [object, setObject] = useState<THREE.Group | null>(null);
  const myPlayer = GetMyPlayer();
  const useGame = myPlayer.getUseGame();
  useEffect(() => {
    const spaceStatusKey = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (value) => {
        if (value === SpaceStatus.Avatar) {
          setObject(avatarObj.sceneGroup);
        } else {
          avatarObj.sceneGroup.removeFromParent();
          setObject(null);
        }
        setStatus(value);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, spaceStatusKey);
    };
  }, []);

  useEffect(() => {
    if (groupRef.current && object) {
      groupRef.current.add(object);
    }
  }, [object, groupRef]);

  return (
    <>
      <TransformTools characterType={CharacterType.Player} useGame={useGame} />
      {status === SpaceStatus.Game && <PlayerModel />}
      {status !== SpaceStatus.Game && (
        <group position={[0, 0, 0]}>
          <SetCamera />
          {status === SpaceStatus.Avatar && <group ref={groupRef} />}
        </group>
      )}
    </>
  );
}

export type CharacterModelProps = {
  avatarObj: AvatarObject;
};
