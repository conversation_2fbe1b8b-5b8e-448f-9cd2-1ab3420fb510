import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import { useControls } from 'leva';
import { IS_MOBILE_ENV } from '@/constant';

export default function Lights({
  debug = false,
  directionalLightColor = '#FFFFFF',
  directionalLightIntensity = 1,
  ambientLightColor1 = '#FFFFFF',
  ambientLightColor2 = '#7d8e95',
  ambientLightIntensity = 2.5,
}: {
  debug?: boolean;
  directionalLightColor?: string;
  directionalLightIntensity?: number;
  ambientLightColor1?: string;
  ambientLightColor2?: string;
  ambientLightIntensity?: number;
}) {
  const directionalLightRef = useRef<THREE.DirectionalLight>(null);
  const ambientLightRef = useRef<THREE.HemisphereLight>(null);
  useEffect(() => {
    if (directionalLightRef.current) {
      // const helper = new THREE.CameraHelper(directionalLightRef.current.shadow.camera);
      // directionalLightRef.current.parent?.add(helper);
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.DirectionalLight, directionalLightRef.current);
    }
    if (ambientLightRef.current) {
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.AmbientLight, ambientLightRef.current);
    }
  }, [directionalLightRef, ambientLightRef]);
  /**
   * Debug settings
   */
  let lightDebug = null;
  if (debug) {
    // Character Controls
    lightDebug = useControls('Light Controls', {
      directionalLightColor: {
        value: directionalLightColor,
      },
      directionalLightIntensity: {
        value: directionalLightIntensity,
        min: 0,
        max: 10,
        step: 0.1,
      },
      ambientLightColor1: {
        value: ambientLightColor1,
      },
      ambientLightColor2: {
        value: ambientLightColor2,
      },
      ambientLightIntensity: {
        value: ambientLightIntensity,
        min: 0,
        max: 10,
        step: 0.1,
      },
    });
    // Apply debug values
    directionalLightColor = lightDebug.directionalLightColor;
    directionalLightIntensity = lightDebug.directionalLightIntensity;
    ambientLightColor1 = lightDebug.ambientLightColor1;
    ambientLightColor2 = lightDebug.ambientLightColor2;
    ambientLightIntensity = lightDebug.ambientLightIntensity;
  }

  return (
    <>
      <directionalLight
        ref={directionalLightRef}
        intensity={directionalLightIntensity}
        color={directionalLightColor}
        castShadow
        shadow-bias={-0.00001}
        shadow-normalBias={0.01}
        shadow-mapSize-width={IS_MOBILE_ENV ? 512 : 2048}
        shadow-mapSize-height={IS_MOBILE_ENV ? 512 : 2048}
        // shadow-camera-top={5}
        // shadow-camera-right={5}
        // shadow-camera-bottom={-5}
        // shadow-camera-left={-5}
        target-position={[0, 0, 0]}
        position={[95, 90, 46]}
      />
      <hemisphereLight
        ref={ambientLightRef}
        args={[ambientLightColor1, ambientLightColor2, ambientLightIntensity]}
      />
      {/*<ambientLight args={[0xFFFFFF, 2.5]}/>*/}
    </>
  );
}
