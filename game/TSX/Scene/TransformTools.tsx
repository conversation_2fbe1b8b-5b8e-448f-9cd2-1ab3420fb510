/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useState } from 'react';
import { SCENE_TYPE } from '@/constant/type';
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
  SpaceStatus,
  TransformData,
} from '@/game/Global/GlobalSpaceEvent';
import { UseGameState } from '@/game_lib/stores/useGame';

function TransformPoint({
  useGame,
  position,
  camDirection,
  sceneType,
}: {
  useGame: any;
  position: THREE.Vector3;
  camDirection: THREE.Vector3;
  sceneType: SCENE_TYPE;
}) {
  const setTransformPoint = useGame((state: UseGameState) => state.setTransformPoint);
  const setFollowCamDirection = useGame((state: UseGameState) => state.setFollowCamDirection);
  useEffect(() => {
    if (sceneType == SCENE_TYPE.None) {
      setTransformPoint(position);
      if (camDirection.x != 0 || camDirection.y != 0 || camDirection.z != 0) {
        setFollowCamDirection(camDirection);
      }
    } else {
      let isLoading = false;
      const loadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
        GlobalDataKey.SceneLoading,
        (loading) => {
          //表示未开始loading
          if (isLoading == loading) return;

          if (!loading) {
            setTransformPoint(position);
            setFollowCamDirection(camDirection);
            GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, loadingKey);
          }
          isLoading = loading;
        }
      );
      GlobalSpaceEvent.SetDataValue<SCENE_TYPE>(GlobalDataKey.SceneType, sceneType);
    }
  }, []);
  return null;
}

export default function TransformTools({
  characterType,
  useGame,
}: {
  characterType: CharacterType;
  useGame: any;
}) {
  const [startTransform, setStartTransform] = useState<boolean>(false);
  const [transformPoint, setTransformPoint] = useState<THREE.Vector3>(new THREE.Vector3(0, 0, 0));
  const [camDirection, setCamDirection] = useState<THREE.Vector3>(new THREE.Vector3(0, 0, 0));
  const [targetScene, setTargetScene] = useState<SCENE_TYPE>(SCENE_TYPE.None);

  const focusGameStatus = (callback: () => void) => {
    GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (status) => {
        if (status !== SpaceStatus.Game) {
          GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Game);
          setTimeout(() => {
            focusGameStatus(callback);
          }, 100);
          return;
        }
        callback();
      },
      true
    );
  };

  useEffect(() => {
    const listenKey = GlobalSpaceEvent.ListenKeyDataChange<TransformData>(
      GlobalDataKey.TransformData,
      (data) => {
        if (data.characterType !== characterType) return;
        focusGameStatus(() => {
          GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
            GlobalDataKey.SceneType,
            (sceneType) => {
              if (data.sceneType === sceneType) {
                data.sceneType = SCENE_TYPE.None;
              }
              setTransformPoint(data.position);
              setTargetScene(data.sceneType || SCENE_TYPE.None);
              setCamDirection(data.camDirection || new THREE.Vector3(0, 0, 0));
              setStartTransform(true);
              GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
                characterType: CharacterType.None,
                position: new THREE.Vector3(0, 0, 0),
              });
              setTimeout(() => {
                setStartTransform(false);
              }, 1);
            },
            true
          );
        });
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.TransformData, listenKey);
    };
  }, []);

  return (
    <>
      {startTransform && (
        <TransformPoint
          useGame={useGame}
          position={transformPoint}
          sceneType={targetScene}
          camDirection={camDirection}
        />
      )}
    </>
  );
}
