import { GLTF } from 'three-stdlib';

import * as THREE from 'three';
import { Suspense, useEffect, useRef, useState } from 'react';
import { useAnimations } from '@react-three/drei';
import { RigidBody } from '@react-three/rapier';
import { Component, Entity } from '@/game_lib/ecs';

function IdleAnimation(props: { animations: THREE.AnimationClip[]; children: React.ReactNode }) {
  const group = useRef(null);

  /**
   * Character animations setup
   */
  const { actions } = useAnimations(props.animations, group);

  useEffect(() => {
    for (const argumentsKey in actions) {
      const action = actions[argumentsKey];
      if (action) {
        action.reset().fadeIn(0).setLoop(THREE.LoopRepeat, Infinity).play();
      }
    }
  }, [actions]);

  return (
    <Suspense fallback={null}>
      <group ref={group} dispose={null}>
        {/* Replace character model here */}
        {props.children}
      </group>
    </Suspense>
  );
}

export default function SceneLoader({
  loaded,
  gltf,
  hideList,
  noShadowList,
}: {
  loaded: (haveWall: boolean) => void;
  gltf: GLTF;
  hideList: string[];
  noShadowList: string[];
}) {
  const group = useRef<THREE.Group>(null);
  const [airWall, setAirWall] = useState<THREE.Mesh | null>(null);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [stopList, setStopList] = useState<THREE.Mesh[]>([]);
  useEffect(() => {
    if (group.current) {
      const _stopList: THREE.Mesh[] = [];
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          //临时兼容星空地图的空气墙
          if (mesh.name === 'X_ground') {
            setAirWall(mesh);
          }
          if (mesh.name === 'stop') {
            const material = mesh.material as THREE.MeshStandardMaterial;
            material.map = null;
            setAirWall(mesh);
            mesh.userData.camExcludeCollision = true;
          }
          if (mesh.name.startsWith('stop_')) {
            _stopList.push(mesh);
          }
          if (noShadowList.includes(mesh.name)) {
            mesh.castShadow = false;
            mesh.receiveShadow = false;
          } else {
            mesh.castShadow = true;
            mesh.receiveShadow = true;
          }
        }
      });
      group.current.add(gltf.scene);
      setAnimations(gltf.animations);
      setStopList(_stopList);
      loaded(false);
    }
  }, []);

  useEffect(() => {
    gltf.scene.traverse((object) => {
      const mesh = object as THREE.Mesh;
      if (mesh) {
        if (hideList.includes(mesh.name)) {
          mesh.visible = false;
        }
      }
    });
    return () => {
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          if (hideList.includes(mesh.name)) {
            mesh.visible = true;
          }
        }
      });
    };
  }, [hideList]);

  useEffect(() => {
    if (airWall) {
      const timeout = setTimeout(() => {
        loaded(true);
      }, 3000);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [airWall]);

  return (
    <IdleAnimation animations={animations}>
      <group ref={group} userData={{ camCollisionListener: true }}>
        {airWall && (
          <Entity traversable>
            <RigidBody type="fixed" colliders="trimesh" ccd>
              <Component name="three">
                <mesh
                  geometry={airWall.geometry}
                  material={airWall.material}
                  castShadow={false}
                  receiveShadow={false}
                  position={[airWall.position.x, airWall.position.y, airWall.position.z]}
                  rotation={[airWall.rotation.x, airWall.rotation.y, airWall.rotation.z]}
                  scale={airWall.scale}
                  userData={{ camExcludeCollision: true }}
                />
              </Component>
              {stopList.map((item, index) => (
                <mesh
                  key={index}
                  geometry={item.geometry}
                  material={item.material}
                  castShadow={false}
                  receiveShadow={false}
                  position={[item.position.x, item.position.y, item.position.z]}
                  rotation={[item.rotation.x, item.rotation.y, item.rotation.z]}
                  scale={item.scale}
                  userData={{ camExcludeCollision: true }}
                />
              ))}
            </RigidBody>
          </Entity>
        )}
      </group>
    </IdleAnimation>
  );
}
