/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useRef } from 'react';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';

import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { GLTF } from 'three-stdlib';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import SceneLoader from '@/game/TSX/Scene/SceneLoader';

export default function OnlineWorld({}: {}) {
  const group = useRef<THREE.Group>();
  // const {nodes, materials, animations} = useGLTF('./space/glb/Island_00.glb') as GLTFResult
  const [gltf_00, setGLTF_00] = React.useState<GLTF | null>(null);
  const [hideList, setHideList] = React.useState<string[]>(['stop']);

  useEffect(() => {
    const loader = new GLTFLoader();
    loader.load(getCdnLink('./space/glb/OnlineWorld.glb'), function (gltf) {
      setGLTF_00(gltf as any);
    });
  }, []);

  const loaded = (haveWall: boolean) => {
    if (haveWall) {
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, false);
    }
  };

  return (
    <group ref={group} dispose={null} userData={{ camCollisionListener: true }}>
      <group name="Scene" userData={{ camCollisionListener: true }}>
        {gltf_00 && (
          <SceneLoader loaded={loaded} gltf={gltf_00} noShadowList={['Sky']} hideList={hideList} />
        )}
      </group>
    </group>
  );
}
