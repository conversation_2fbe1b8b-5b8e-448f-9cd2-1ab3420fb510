/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { GLTF } from 'three-stdlib';
import { useAnimations } from '@react-three/drei';

import { reflector, texture, uv } from 'three/tsl';
//@ts-ignore
import * as WebGpg from 'three/webgpu';

export function IdleAnimation(props: {
  animations: THREE.AnimationClip[];
  children: React.ReactNode;
}) {
  const group = useRef();

  /**
   * Character animations setup
   */
  const { actions } = useAnimations(props.animations, group);

  useEffect(() => {
    for (const argumentsKey in actions) {
      const action = actions[argumentsKey];
      if (action) {
        action.reset().fadeIn(0).setLoop(THREE.LoopRepeat, Infinity).play();
      }
    }
  }, [actions]);

  return (
    <Suspense fallback={null}>
      <group ref={group} dispose={null} userData={{ camExcludeCollision: true }}>
        {/* Replace character model here */}
        {props.children}
      </group>
    </Suspense>
  );
}

export default function OnlineWorld_01({ gltf }: { gltf: GLTF }) {
  const group = useRef<THREE.Group>();
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  useEffect(() => {
    if (group.current) {
      const root = group.current;
      const textureLoader = new THREE.TextureLoader();

      const floorNormal = textureLoader.load(
        'space/textures/floors/FloorsCheckerboard_S_Normal.jpg'
      );
      floorNormal.wrapS = THREE.RepeatWrapping;
      floorNormal.wrapT = THREE.RepeatWrapping;

      // const floorGeometry = new THREE.PlaneGeometry(50, 50);
      //
      // const floor = new THREE.Mesh(floorGeometry, floorMaterial);
      // floor.receiveShadow = true;
      // //
      // floor.position.set(0, 0, 0);
      // floor.rotation.x = -Math.PI / 2;
      // group.current.add(floor);
      //

      root.add(gltf.scene);
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          mesh.castShadow = true;
          mesh.receiveShadow = true;
          //
          // // floor
          //
          //
          if (mesh.name === 'X_ground') {
            const materials = mesh.material as THREE.MeshStandardMaterial;
            const floorColor = materials.map;
            if (floorColor) {
              floorColor.wrapS = THREE.RepeatWrapping;
              floorColor.wrapT = THREE.RepeatWrapping;
            }

            const floorUV = uv().mul(1);
            const floorNormalOffset = texture(floorColor, floorUV).xy.mul(2).sub(1).mul(0.02);

            const reflection = reflector({ resolution: 0.5 }); // 0.5 is half of the rendering view
            reflection.target.rotateX(-Math.PI / 2);
            reflection.uvNode = reflection.uvNode.add(floorNormalOffset);
            root.add(reflection.target);
            //
            const floorMaterial = new WebGpg.MeshPhongNodeMaterial();
            floorMaterial.colorNode = texture(floorColor, floorUV).add(reflection);
            mesh.material = floorMaterial;
          }
        }
      });
      setAnimations(gltf.animations);
    }
  }, []);

  return (
    <IdleAnimation animations={animations}>
      <group ref={group} userData={{ camCollisionListener: true }} />
    </IdleAnimation>
  );
}
