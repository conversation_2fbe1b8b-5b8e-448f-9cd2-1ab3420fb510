import { useEffect, useMemo, useState } from 'react';
import * as THREE from 'three';
import { UseGameState } from '@/game_lib/stores/useGame';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import GlobalSpace, { GAME_OP_TYPE } from '@/game/Global/GlobalSpace';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { IPetShedConfigData, PetShedConfig } from '@/game/Config/PetShedConfig';
import { updateModalState } from '@/store/modal';
import { useDispatch } from 'react-redux';
import { IGameState } from '@/constant/type';
import { useAppSelector } from '@/hooks/useStore';
import { ItemConfig, ItemData } from '@/game/Config/ItemConfig';
import { usePickPetAltar, useRetrievePet } from '@/hooks/useRetrievePickPet';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { PetConfig, PetData } from '@/game/Config/PetConfig';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityTopText } from '@/game/TS/Entity/Components/EntityTopText';
import { CHAIN_TYPE_ENUM, PetShedRecordStatus, PetShedRecordType } from '@/constant/enum';
import { EntityCollider } from '@/game/TS/Entity/Components/EntityCollider';
import { getMillisecondsTimeInfo } from '@/utils/dayjsHelper';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';

export default function ScenePetShed({ shedId }: { shedId: number }) {
  const dispatch = useDispatch();
  const particleSystem = getParticleSystem();
  const { retrievePet } = useRetrievePet();
  const { handlePickPetAltar } = usePickPetAltar();
  const { petShedInfo } = useAppSelector((state: { GameReducer: IGameState }) => state.GameReducer);
  const myPlayer = GetMyPlayer();
  const myUseGame = myPlayer.getUseGame();
  const [shedData, setShedData] = useState<IPetShedConfigData | null>(null);
  const [itemData, setItemData] = useState<ItemData | null>(null);
  const [shedEntity, setShedEntity] = useState<Entity | null>(null);
  const [waitingEndTime, setPetShedEndTime] = useState<number>(0);
  const [petData, setPetData] = useState<PetData | null>(null);
  const setMyCurAnimation = myUseGame((state: UseGameState) => state.setCurAnimation);
  const [nearItem, setNearItem] = useState<boolean>(false);
  const [clickDelay, setClickDelay] = useState<boolean>(false);

  const [timeLeft, setTimeLeft] = useState(0);

  const curPetShedInfo = useMemo(() => {
    return petShedInfo.find((item) => item.positionTag === String(shedId));
  }, [petShedInfo, shedId]);

  const clearPetShedTopText = () => {
    if (!shedEntity) return;
    const topText = shedEntity.getComponent<EntityTopText>(ComponentType.TopText);
    if (topText) {
      topText.setText('');
      topText.setTopIconName('');
    }
  };

  //创建宠物
  useEffect(() => {
    if (petData && shedData) {
      const petEntity = EntityManager.getInstance().createClientEntity(
        [ComponentType.Transform, ComponentType.GlbMesh, ComponentType.Animation],
        true
      );
      const petMesh = petEntity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
      petMesh?.setGlbUrl(getCdnLink(petData.glbUrl));
      const petAnimation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
      //休息动作
      petAnimation?.setReplaceAnimations(['Action_04']);
      petAnimation?.setDefaultAnimation('Action_04');
      petAnimation?.playAnimation('Action_04');
      const transform = petEntity.getComponent<EntityTransform>(ComponentType.Transform);
      switch (curPetShedInfo?.baseType) {
        case PetShedRecordType.PET_SHED:
          transform?.setPosition(
            new THREE.Vector3(
              shedData.position[0],
              shedData.position[1] + 0.25,
              shedData.position[2]
            ),
            new THREE.Quaternion().setFromEuler(
              new THREE.Euler(0, (shedData.yaw * Math.PI) / 180, 0)
            )
          );
          break;
        case PetShedRecordType.PET_ALTAR:
          transform?.setPosition(
            new THREE.Vector3(
              shedData.position[0],
              shedData.position[1] + 0.65,
              shedData.position[2]
            ),
            new THREE.Quaternion().setFromEuler(
              new THREE.Euler(0, (shedData.yaw * Math.PI) / 180, 0)
            )
          );
          petMesh?.setScale(0.7);
          break;
      }
      if (transform) {
        particleSystem.addParticle(
          transform.position,
          transform.rotation,
          './particles/Effect_pet_call01.json',
          1,
          3000
        );
        particleSystem.addParticle(
          transform.position,
          transform.rotation,
          './particles/Effect_pet_call02.json',
          1,
          3000
        );
      }
      return () => {
        EntityManager.getInstance().removeEntity(petEntity);
        particleSystem.addParticle(
          new THREE.Vector3(
            shedData.position[0],
            shedData.position[1] + 0.25,
            shedData.position[2]
          ),
          new THREE.Quaternion(),
          './particles/Effect_pet_recall.json',
          1,
          3000
        );
      };
    }
  }, [petData, shedData]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (waitingEndTime) {
      const tick = () => {
        setTimeLeft(() => {
          const now = Date.now();
          const timeLeft = Math.round(waitingEndTime - now);
          if (timeLeft <= 0) {
            if (timer) clearInterval(timer);
            return 0;
          }
          return timeLeft;
        });
      };
      timer = setInterval(tick, 1000);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [waitingEndTime]);

  const { hours, minutes, seconds } = useMemo(() => {
    return getMillisecondsTimeInfo(timeLeft);
  }, [timeLeft]);

  const getPet = (mockTag: string) => {
    const petTagInfo = petShedInfo.find((item) => item.positionTag === mockTag);

    const slotRecordId = petTagInfo?.slotRecordId;
    if (slotRecordId) {
      retrievePet(slotRecordId);
    }
  };

  useEffect(() => {
    PetShedConfig.getInstance().getData(shedId, (data) => {
      setShedData(data);
    });
  }, [shedId]);

  useEffect(() => {
    if (shedData) {
      let cancel = false;
      petShedInfo.forEach((item) => {
        if (item.positionTag === shedData.tag) {
          setPetShedEndTime(item.waitingEndTime || 0);
          const itemId = Number(item.userItemTag);
          if (itemId) {
            ItemConfig.getInstance().getData(itemId, (data) => {
              if (cancel) return;
              setItemData(data);
            });
          }
          const petTag = item.petTag;
          if (petTag) {
            PetConfig.getInstance().getData(petTag, (data) => {
              if (cancel) return;
              setPetData(data);
            });
          }
        }
      });
      return () => {
        cancel = true;
        setItemData(null);
        setPetData(null);
        setPetShedEndTime(0);
      };
    }
  }, [petShedInfo, shedData]);

  useEffect(() => {
    const now = Date.now();
    if (waitingEndTime > now) {
    } else {
    }
  }, [waitingEndTime]);

  useEffect(() => {
    if (!shedEntity) return;
    const topText = shedEntity.getComponent<EntityTopText>(ComponentType.TopText);
    if (topText) {
      if (curPetShedInfo?.chainType === CHAIN_TYPE_ENUM.ON_CHAIN) {
        if (curPetShedInfo.status === PetShedRecordStatus.GENERATED) {
          topText.setText('');
          topText.setTopIconName('');
          return;
        }
        if (curPetShedInfo.status === PetShedRecordStatus.GENERATING && timeLeft <= 0) {
          topText.setText('Waiting for block confirmation', '', 1);
          topText.setTopIconName('clock');
          return;
        }
        topText.setText('Estimated Time: ' + `${hours}:${minutes}:${seconds}`, '', 1);
        topText.setTopIconName('clock');
      } else {
        if (timeLeft <= 0) {
          topText.setText('');
          topText.setTopIconName('');
          return;
        }
        topText.setText('Time Left: ' + `${hours}:${minutes}:${seconds}`, '', 1);
        topText.setTopIconName('clock');
      }
    }
  }, [hours, minutes, seconds, shedEntity, timeLeft, curPetShedInfo]);

  useEffect(() => {
    if (itemData && shedData) {
      const shedEntity = EntityManager.getInstance().createClientEntity(
        [
          ComponentType.Transform,
          ComponentType.GlbMesh,
          ComponentType.TopText,
          ComponentType.Animation,
          ComponentType.Collider,
        ],
        true
      );
      const GlbMesh = shedEntity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
      GlbMesh?.setGlbUrl(getCdnLink(itemData.glb_url));
      const collider = shedEntity.getComponent<EntityCollider>(ComponentType.Collider);
      collider?.setMeshName('stop');
      const shedPosition = new THREE.Vector3(
        shedData.position[0],
        shedData.position[1],
        shedData.position[2]
      );
      const shedQuaternion = new THREE.Quaternion().setFromEuler(
        new THREE.Euler(0, (shedData.yaw * Math.PI) / 180, 0)
      );
      particleSystem.addParticle(
        shedPosition,
        shedQuaternion,
        './particles/Effect_PetRoom_build.json',
        1,
        3000
      );
      const transform = shedEntity.getComponent<EntityTransform>(ComponentType.Transform);
      transform?.setPosition(shedPosition, shedQuaternion);
      setShedEntity(shedEntity);
      return () => {
        particleSystem.addParticle(
          shedPosition,
          shedQuaternion,
          './particles/Effect_PetRoom_broken.json',
          1,
          3000
        );
        EntityManager.getInstance().removeEntity(shedEntity);
      };
    }
  }, [itemData, shedData]);

  useEffect(() => {
    if (shedEntity && curPetShedInfo) {
      const animation = shedEntity.getComponent<EntityAnimation>(ComponentType.Animation);
      if (curPetShedInfo.baseType === PetShedRecordType.PET_ALTAR) {
        switch (curPetShedInfo.status) {
          case PetShedRecordStatus.PENDING:
            animation?.playAnimation('');
            animation?.setReplaceAnimations(['']);
            animation?.setDefaultAnimation('');
            break;
          case PetShedRecordStatus.GENERATING:
            animation?.setReplaceAnimations(['Action_a1']);
            animation?.playAnimation('Action_a1');
            break;
          case PetShedRecordStatus.GENERATED:
            animation?.playAnimation('Action_a2');
            animation?.setReplaceAnimations(['Action_a3']);
            animation?.setDefaultAnimation('Action_a3');
        }
      } else if (curPetShedInfo.baseType === PetShedRecordType.PET_SHED) {
        if (curPetShedInfo.status === PetShedRecordStatus.GENERATING) {
          const shedEffect = EntityManager.getInstance().createClientEntity(
            [ComponentType.FollowBone, ComponentType.AnimationAll, ComponentType.GlbMesh],
            true
          );
          const followBone = shedEffect.getComponent<EntityFollowBone>(ComponentType.FollowBone);
          followBone?.setTarget(shedEntity.clientIndex, 'transform');
          followBone?.setPosition(new THREE.Vector3(0, 0.9, 0), new THREE.Quaternion());
          const shedEffectMesh = shedEffect.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
          shedEffectMesh?.setGlbUrl(getCdnLink('./particles/Effect_egg.glb'));
          return () => {
            EntityManager.getInstance().removeEntity(shedEffect);
          };
        }
      }
    }
  }, [shedEntity, curPetShedInfo]);

  useEffect(() => {
    if (shedData && !clickDelay) {
      const now = Date.now();
      const opList: string[] = [];
      if (nearItem) {
        const baseType = curPetShedInfo?.baseType;

        if (waitingEndTime === 0) {
          opList.push(
            GlobalSpace.addGameOp(
              GAME_OP_TYPE.CustomOp,
              () => {
                dispatch(
                  updateModalState({
                    placePetModalConfig: {
                      isOpen: true,
                      positionTag: shedData.tag,
                    },
                  })
                );
                setClickDelay(true);
                setTimeout(() => {
                  setClickDelay(false);
                }, 3000);
              },
              0,
              'Place Pet Nest',
              getCdnLink('./icon/option/place.svg')
            )
          );
        } else {
          if (baseType === PetShedRecordType.PET_SHED) {
            if (curPetShedInfo?.status === PetShedRecordStatus.GENERATED) {
              opList.push(
                GlobalSpace.addGameOp(
                  GAME_OP_TYPE.CustomOp,
                  () => {
                    getPet(shedData.tag);
                    clearPetShedTopText();
                    setClickDelay(true);
                    setTimeout(() => {
                      setClickDelay(false);
                    }, 3000);
                  },
                  0,
                  'Awaken Pet',
                  getCdnLink('./icon/option/pet.svg')
                )
              );
            }
          } else {
            if (curPetShedInfo?.status === PetShedRecordStatus.GENERATED) {
              opList.push(
                GlobalSpace.addGameOp(
                  GAME_OP_TYPE.CustomOp,
                  () => {
                    getPet(shedData.tag);
                    clearPetShedTopText();
                    setClickDelay(true);
                    setTimeout(() => {
                      setClickDelay(false);
                    }, 3000);
                  },
                  0,
                  'Check',
                  getCdnLink('./icon/option/pet.svg')
                )
              );
            }
            if (waitingEndTime === -1 && curPetShedInfo?.status === PetShedRecordStatus.PENDING) {
              opList.push(
                GlobalSpace.addGameOp(
                  GAME_OP_TYPE.CustomOp,
                  () => {
                    dispatch(
                      updateModalState({
                        petFusionModalConfig: {
                          isOpen: true,
                          configData: {
                            slotRecordId: curPetShedInfo!.slotRecordId || '',
                          },
                        },
                      })
                    );
                    setClickDelay(true);
                    setTimeout(() => {
                      setClickDelay(false);
                    }, 3000);
                  },
                  0,
                  'Merge Pet',
                  getCdnLink('./icon/option/pet.svg')
                )
              );
              opList.push(
                GlobalSpace.addGameOp(
                  GAME_OP_TYPE.CustomOp,
                  () => {
                    handlePickPetAltar({
                      positionTag: curPetShedInfo.positionTag,
                      userItemId: curPetShedInfo.userItemId,
                    });
                    setClickDelay(true);
                    setTimeout(() => {
                      setClickDelay(false);
                    }, 1000);
                  },
                  0,
                  'Pick Up',
                  getCdnLink('./icon/option/pickUp.svg')
                )
              );
            }
          }
        }
        opList.push(
          GlobalSpace.addGameOp(
            GAME_OP_TYPE.CustomOp,
            () => {
              dispatch(updateModalState({ petBedDescModalConfig: { isOpen: true } }));
            },
            0,
            'Event Rule',
            getCdnLink('./icon/option/tip.svg')
          )
        );
      }
      return () => {
        opList.forEach((op) => {
          GlobalSpace.removeGameOp(op);
        });
      };
    }
  }, [shedData, nearItem, clickDelay, waitingEndTime, curPetShedInfo]);

  useEffect(() => {
    const pointKey = 'shed_' + shedId;
    if (shedData) {
      myPlayer.registerMapPoint(
        pointKey,
        new THREE.Vector3(shedData.position[0], shedData.position[1], shedData.position[2]),
        (distance) => {
          setNearItem(distance < shedData.distance);
        }
      );

      return () => {
        myPlayer.unregisterMapPoint(pointKey);
      };
    }
  }, [shedData, myPlayer, shedId]);

  return null;
}
