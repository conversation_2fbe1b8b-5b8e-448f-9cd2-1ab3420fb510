import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import GlobalSpace from '@/game/Global/GlobalSpace';
import AvatarDataNFT from '@/game/TS/Data/AvatarDataNFT';
import { useThree } from '@react-three/fiber';
import GlobalSpaceEvent, { GlobalDataKey, SpaceStatus } from '@/game/Global/GlobalSpaceEvent';
import gsap from 'gsap';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

const getScreenPosition = (
  point: THREE.Vector3,
  camera: THREE.Camera,
  size: {
    width: number;
    height: number;
  }
) => {
  // 将三维坐标转换为屏幕坐标

  const projected = point.clone().project(camera);
  const x = (0.5 + projected.x / 2) * size.width; // 屏幕的 X 坐标
  const y = (0.5 - projected.y / 2) * size.height; // 屏幕的 Y 坐标
  return new THREE.Vector2(x, y);
};

interface IProps2 {
  mesh: THREE.Mesh;
  nftData: AvatarDataNFT;
  isLooking: boolean;
  isShow: boolean;
}

function LookingNFT({ mesh }: { mesh: THREE.Mesh }) {
  const { camera, size } = useThree();

  useEffect(() => {
    if (!camera) {
      return;
    }

    // 获取物体的前方向（世界坐标系）
    const forward = new THREE.Vector3();
    mesh.getWorldDirection(forward);
    // 获取物体的上方向（世界坐标系）
    const up = mesh.up.clone().normalize(); // 默认是 (0, 1, 0)

    // 计算物体的左侧方向（世界坐标系）
    const left = new THREE.Vector3();
    left.crossVectors(up, forward).normalize();

    const offset = -1.2;
    const box = new THREE.Box3().setFromObject(mesh);
    const height = (box.max.y - box.min.y) * 0.6;

    let right = left.clone().multiplyScalar(offset * height);

    let isLeft = false;
    if (mesh.position.z < -3.7) {
      right = left.clone().multiplyScalar(-offset * height);
      isLeft = true;
    }
    const newTargetPos = mesh
      .getWorldPosition(new THREE.Vector3(0, 0, 0))
      .clone()
      .add(right);
    const newPos = mesh
      .getWorldPosition(new THREE.Vector3(0, 0, 0))
      .clone()
      .add(forward.multiplyScalar(3 * height)) // 前方一定距离
      .add(right);

    GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.LookingNftLeft, isLeft);

    GlobalSpaceEvent.ListenKeyDataChange<OrbitControls>(
      GlobalDataKey.EditorControls,
      (controls) => {
        if (controls) {
          controls.enableZoom = false;
          controls.rotateSpeed = 0;

          gsap.to(controls.target, {
            x: newTargetPos.x,
            y: newTargetPos.y,
            z: newTargetPos.z,
            duration: 0.5, // 1秒动画时间
            ease: 'power1.out', // 动画曲线
            onUpdate: () => {
              controls.update();
            },
          });
          gsap.to(camera.position, {
            x: newPos.x,
            y: newPos.y,
            z: newPos.z,
            duration: 0.5, // 1秒动画时间
            ease: 'power1.out', // 动画曲线
            onUpdate: () => {
              controls.update();
            },
          });
        }
      },
      true
    );
  }, [camera]);
  return null;
}

function CreateMesh({ mesh, nftData, isShow, isLooking }: IProps2) {
  const meshRef = useRef<THREE.Mesh>(null);
  const { camera, size } = useThree();

  // const texture = useTexture(nftData.content);
  useEffect(() => {
    const texture = new THREE.TextureLoader().load(nftData.content, () => {
      if (meshRef.current) {
        const material = (meshRef.current.material as THREE.MeshStandardMaterial).clone();
        // const textureClone = texture.clone();
        const aspectRatio = texture.image.width / texture.image.height;
        // 调整平面缩放比例，保持纹理不失真
        if (aspectRatio > 1) {
          // 如果纹理的高度大于宽度，平面应沿Y轴缩放
          meshRef.current.scale.set(0.01, 0.01 / aspectRatio, 0.01);
        } else {
          // 如果纹理的宽度大于高度，平面应沿X轴缩放
          meshRef.current.scale.set(aspectRatio * 0.01, 0.01, 0.01);
        }
        texture.flipY = false; // 关闭垂直翻转

        material.map = texture;
        meshRef.current.material = material;
      }
    });
  }, [nftData, meshRef]);

  useEffect(() => {
    if (meshRef.current) {
      let nowStats = SpaceStatus.Avatar;
      let curLooking = 0;
      const unListen1 = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
        GlobalDataKey.SpaceStatus,
        (status) => {
          nowStats = status;
        }
      );
      const unListen2 = GlobalSpaceEvent.ListenKeyDataChange<number>(
        GlobalDataKey.LookingNftIndex,
        (index) => {
          curLooking = index;
        }
      );

      const oldMesh = meshRef.current;

      GlobalSpace.addHoverObject(oldMesh);
      oldMesh.userData.hoverCallback = () => {
        if (nowStats == SpaceStatus.NFT) {
          return;
        }
        const obj = oldMesh;
        if (nftData && obj) {
          //计算nft右下角的坐标
          const leftBottom = obj.position.clone();
          const forward = new THREE.Vector3();
          obj.getWorldDirection(forward);
          // 获取物体的上方向（世界坐标系）
          const up = obj.up.clone().normalize(); // 默认是 (0, 1, 0)

          // 计算物体的左侧方向（世界坐标系）
          const left = new THREE.Vector3();
          left.crossVectors(up, forward).normalize();
          const box = new THREE.Box3().setFromObject(obj);
          const height = box.max.y - box.min.y;
          leftBottom.add(left.multiplyScalar(height / 2 - 0.2));
          leftBottom.add(up.multiplyScalar(-(height / 2 - 0.15)));

          const mouse = getScreenPosition(leftBottom, camera, size);
          GlobalSpaceEvent.SetDataValue(GlobalDataKey.NftHoverObj, {
            nft: nftData,
            mouse: mouse,
          });
        }
      };
      oldMesh.userData.unHoverCallback = () => {
        if (nftData) {
          GlobalSpaceEvent.SetDataValue(GlobalDataKey.NftHoverObj, {
            nft: null,
            mouse: new THREE.Vector2(0, 0),
          });
        }
      };
      GlobalSpace.addClickObject(meshRef.current);
      oldMesh.userData.clickCallback = () => {
        if (nowStats == SpaceStatus.NFT && nftData && curLooking == 0) {
          GlobalSpaceEvent.SetDataValue(GlobalDataKey.LookingNftIndex, nftData.position);
        }
      };
      return () => {
        GlobalSpace.removeHoverObject(oldMesh);
        GlobalSpace.removeClickObject(oldMesh);
        GlobalSpaceEvent.RemoveListener<SpaceStatus>(GlobalDataKey.SpaceStatus, unListen1);
        GlobalSpaceEvent.RemoveListener<number>(GlobalDataKey.LookingNftIndex, unListen2);
      };
    }
  }, []);
  return (
    <>
      {isLooking && meshRef.current && <LookingNFT mesh={meshRef.current} />}
      {
        <mesh
          ref={meshRef}
          name={mesh.name}
          position={mesh.position}
          scale={mesh.scale}
          rotation={mesh.rotation}
          material={mesh.material}
          geometry={mesh.geometry}
          visible={isShow}
          castShadow
          receiveShadow
        ></mesh>
      }
    </>
  );
}

interface IProps {
  mesh: THREE.Mesh;
  nftData?: AvatarDataNFT;
  position: number;
  lookingIndex: number;
}

export default function NFT({ mesh, nftData, position, lookingIndex }: IProps) {
  const [isNFT, setIsNFT] = useState(false);

  useEffect(() => {
    const unListen = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (status) => {
        setIsNFT(status === SpaceStatus.NFT);
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, unListen);
    };
  }, []);

  return (
    <>
      {(nftData || isNFT) && (
        <CreateMesh
          mesh={mesh}
          nftData={nftData || new AvatarDataNFT(position, './space/glb/NFT/0.jpg')}
          isLooking={lookingIndex == position}
          isShow={position == lookingIndex || lookingIndex == 0}
        />
      )}
    </>
  );
}
