/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { GLTF } from 'three-stdlib';
import { useAnimations } from '@react-three/drei';

export function IdleAnimation(props: {
  animations: THREE.AnimationClip[];
  children: React.ReactNode;
}) {
  const group = useRef();

  /**
   * Character animations setup
   */
  const { actions } = useAnimations(props.animations, group);

  useEffect(() => {
    for (const argumentsKey in actions) {
      const action = actions[argumentsKey];
      if (action) {
        action.reset().fadeIn(0).setLoop(THREE.LoopRepeat, Infinity).play();
      }
    }
  }, [actions]);

  return (
    <Suspense fallback={null}>
      <group ref={group} dispose={null} userData={{ camExcludeCollision: true }}>
        {/* Replace character model here */}
        {props.children}
      </group>
    </Suspense>
  );
}

export default function Room_01({ gltf }: { gltf: GLTF }) {
  const group = useRef<THREE.Group>();
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  useEffect(() => {
    if (group.current) {
      group.current.add(gltf.scene);
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          mesh.castShadow = false;
          mesh.receiveShadow = true;
        }
      });
      setAnimations(gltf.animations);
    }
  }, []);

  return (
    <IdleAnimation animations={animations}>
      <group ref={group} userData={{ camCollisionListener: true }} />
    </IdleAnimation>
  );
}
