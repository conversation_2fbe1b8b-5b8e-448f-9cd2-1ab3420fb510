/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { useEffect, useRef, useState } from 'react';
import { ParticleConfig, ParticleData } from '@/game/Config/ParticleConfig';
import ParticleObject from '@/game/TSX/Particles/ParticleObject';

export default function SceneParticle({ particleId }: { particleId: number }) {
  const [particleData, setParticleData] = useState<ParticleData | null>(null);
  const groupRef = useRef<THREE.Group>(null);
  useEffect(() => {
    ParticleConfig.getInstance().getData(particleId, (data) => {
      setParticleData(data);
    });
  }, []);

  useEffect(() => {
    if (groupRef.current && particleData) {
      groupRef.current.position.set(
        particleData.position[0],
        particleData.position[1],
        particleData.position[2]
      );
      groupRef.current.quaternion.setFromEuler(new THREE.Euler(0, particleData.yawY || 0, 0));
    }
  }, [particleData]);

  return (
    <>
      {particleData && (
        <group ref={groupRef}>
          <ParticleObject url={particleData.particle_url} scale={particleData.scale} />
        </group>
      )}
    </>
  );
}
