/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { useEffect, useState } from 'react';
import { SCENE_TYPE } from '@/constant/type';
import GlobalSpace, { GAME_OP_TYPE } from '@/game/Global/GlobalSpace';
import GlobalSpaceEvent, { CharacterType, GlobalDataKey, TransformData } from '@/game/Global/GlobalSpaceEvent';
import { DoorConfig, DoorData } from '@/game/Config/DoorConfig';
import FSvg from '../../../public/image/gameOpIcon/f.svg';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';

export default function Door({ doorId }: { doorId: number }) {
  const [doorData, setDoorData] = useState<DoorData | null>(null);
  const myPlayer = GetMyPlayer();
  useEffect(() => {
    DoorConfig.getInstance().getData(doorId, (data) => {
      setDoorData(data);
    });
  }, []);

  useEffect(() => {
    if (doorData === null) {
      return;
    }

    let callbackKey = '';
    const initPosition = new THREE.Vector3(
      doorData.position[0],
      doorData.position[1],
      doorData.position[2]
    );
    const targetPosition = new THREE.Vector3(
      doorData.targetPos[0],
      doorData.targetPos[1],
      doorData.targetPos[2]
    );
    const cameraPosition = new THREE.Vector3(
      doorData.targetCamPos[0],
      doorData.targetCamPos[1],
      doorData.targetCamPos[2]
    );

    const watchKey = GlobalSpace.whatCharacterPosition((position) => {
      if (position.distanceTo(initPosition) < doorData.distance) {
        if (callbackKey.length == 0) {
          const jump = () => {
            const cameraDirection = cameraPosition.clone().sub(targetPosition).normalize();
            GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
              characterType: CharacterType.Player,
              position: targetPosition,
              camDirection: cameraDirection,
              sceneType: doorData.targetMapId as SCENE_TYPE,
            });
            myPlayer.callAppApi(AppGameApiKey.setLoaderType, doorData.loadingType);
          };
          if (doorData.noButton === 1) {
            jump();
          } else {
            callbackKey = GlobalSpace.addGameOp(
              GAME_OP_TYPE.CustomOp,
              () => {
                GlobalSpace.removeGameOp(callbackKey);
                callbackKey = '';
                jump();
              },
              0,
              doorData.text,
              FSvg.src
            );
          }
        }
      } else {
        if (callbackKey.length > 0) {
          GlobalSpace.removeGameOp(callbackKey);
          callbackKey = '';
        }
      }
    });
    return () => {
      if (callbackKey.length > 0) {
        GlobalSpace.removeGameOp(callbackKey);
        callbackKey = '';
      }
      GlobalSpace.cancelCharacterPositionCallback(watchKey);
    };
  }, [doorData]);

  return null;
}
