/*
Enhanced CanvasUI with HTML5 support using CanvasTexture and Sprite
*/

import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import createUseGame, { UseGameState } from '@/game_lib/stores/useGame';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import { GLTF } from 'three-stdlib';
import { useAnimations } from '@react-three/drei';

function Animation({
  root,
  useGame,
  animations,
}: {
  animations: THREE.AnimationClip[];
  root: THREE.Object3D;
  useGame: any;
}) {
  /**
   * Character animations setup
   */
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);

  const { actions } = useAnimations(animations, root);

  const animationSet = useGame((state: UseGameState) => state.animationSet);

  useEffect(() => {
    if (animations.length === 0 || curAnimation === null || curAnimation === undefined) {
      return;
    }
    const actionList: string[] = curAnimation.split('|');

    const actionKey = actionList[0];
    const actionSpeed = Number(actionList[1] || 1);

    const finishCall = () => {
      setCurAnimation('idle');
    };
    // Play animation
    const action: THREE.AnimationAction | null = actions[actionKey || ''];
    if (action) {
      action.timeScale = actionSpeed;

      action.reset().fadeIn(0.2).play();

      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener('finished', () => finishCall());
    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);

        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener('finished', () => finishCall());
        (action as any)._mixer._listeners = [];
      }
    };
  }, [actions, curAnimation, animationSet, animations]);

  return null;
}

export default function ScenePizzaNpc({ running }: { running: boolean }) {
  const groupRef = useRef<THREE.Group>(null);
  const useGameRef = useRef(createUseGame());
  const setCurAnimation = useGameRef.current((state: UseGameState) => state.setCurAnimation);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [gltf, setGltf] = useState<GLTF | null>(null);

  useEffect(() => {
    if (running) {
      setCurAnimation('Action_1003');
    } else {
      setCurAnimation('Action_00');
    }
  }, [running]);

  useEffect(() => {
    LoaderUtil.loadGlb('./assets/Pet/NPC_pizza.glb', (gltf) => {
      if (groupRef.current) {
        groupRef.current.position.copy(new THREE.Vector3(0, 0, 0));
        gltf.scene.scale.set(15, 15, 15);
        groupRef.current.add(gltf.scene);
      }
      setGltf(gltf);
      setAnimations(gltf.animations);
    });

    return () => {
      if (groupRef.current) {
        groupRef.current.clear();
      }
    };
  }, []);

  return (
    <group ref={groupRef}>
      {gltf && <Animation animations={animations} root={gltf.scene} useGame={useGameRef.current} />}
    </group>
  );
}
