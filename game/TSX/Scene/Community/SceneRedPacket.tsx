/*
Enhanced CanvasUI with HTML5 support using CanvasTexture and Sprite
*/

import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import GlobalSpace, { GAME_OP_TYPE } from '@/game/Global/GlobalSpace';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { MapConfig } from '@/game/Config/MapConfig';
import { RedPacketData } from '@/game/Config/RedPacketConfig';
import { RedPacketListener } from '@/game/TS/RedPacket/RedPacketListener';
import { RedPacketEvent } from '@/game/TS/RedPacket/RedPacketEvent';
import { toFormatAccount } from '@/utils';
import { useNetWork } from '@/game/TS/useNetWork';
import toast from 'react-hot-toast';
import { game } from '@/game/Proto/generated/game_messages';
import { RedPacketManager } from '@/game/TS/RedPacket/RedPacketManager';

export default function SceneRedPacket({ packetData }: { packetData: RedPacketData }) {
  const { request } = useNetWork();
  const myPlayer = GetMyPlayer();
  const groupRef = useRef<THREE.Group>(null);
  const [nearItem, setNearItem] = useState<boolean>(false);
  const [packetList, setPacketList] = useState<game.IRedPacket[]>([]);
  const [pickedList, setPickedList] = useState<string[]>(myPlayer.pickedList);
  const [showEffect, setShowEffect] = useState<boolean>(false);

  const pickUpPacket = async (redPacketRecordId: string) => {
    const newPacketList = packetList.filter((item) => {
      return item.redPacketRecordId !== redPacketRecordId;
    });
    setPacketList(newPacketList);
    const resp = await request(
      game.ClientRequestTypes.PICK_UP_RED_PACKET,
      game.ReqPickUpRedPacket.create({
        redPacketRecordId: redPacketRecordId,
        configId: packetData.id,
      }).toJSON()
    );
    const { code, msg, data, dataList } = resp;
    if (code && code === 1) {
      const { tick, amount } = data;
      myPlayer.callAppApi(AppGameApiKey.showRewards, tick, amount);
      myPlayer.pickedList = [...pickedList, redPacketRecordId];
      toast.success('Pick Up Success');
    } else {
      setPacketList(dataList);
      toast.error(msg || 'Pick Up Failed');
    }
  };

  useEffect(() => {
    const pointItem = RedPacketManager.getInstance().getRedPacket(packetData.id);
    if (pointItem) {
      setPacketList(pointItem.redPacketList || []);
    }
    const handler = (data: game.IRedPacketPoint) => {
      if (data.configId === packetData.id) {
        setPacketList(data.redPacketList || []);
      }
    };
    RedPacketListener.getInstance().addListener(RedPacketEvent.redPacketUpdate, handler);
    return () => {
      RedPacketListener.getInstance().removeListener(RedPacketEvent.redPacketUpdate, handler);
    };
  }, []);
  useEffect(() => {
    let cancel = false;
    if (packetList.length > 0) {
      let glbUrl = packetData.glbUrl1;
      if (packetList.length > 1) {
        glbUrl = packetData.glbUrl2;
        if (packetList.length > 2) {
          glbUrl = packetData.glbUrl3;
        }
      }
      LoaderUtil.loadGlb(glbUrl, (gltf) => {
        if (cancel) {
          return;
        }
        if (groupRef.current) {
          const curMapData = MapConfig.getInstance().getCurMapData();
          if (curMapData) {
            groupRef.current.position.copy(
              new THREE.Vector3(
                packetData.position[0] - curMapData.offset[0],
                packetData.position[1] - curMapData.offset[1],
                packetData.position[2] - curMapData.offset[2]
              )
            );
            groupRef.current.quaternion.setFromEuler(
              new THREE.Euler(
                (packetData.rotation[0] * Math.PI) / 180,
                (packetData.rotation[1] * Math.PI) / 180,
                (packetData.rotation[2] * Math.PI) / 180
              )
            );
            gltf.scene.scale.set(packetData.glbScale, packetData.glbScale, packetData.glbScale);
            groupRef.current.add(gltf.scene);
          }
          gltf.scene.traverse((child) => {
            const mesh = child as THREE.Mesh;
            if (mesh) {
              if (mesh.name.includes('Effect_gift')) {
                mesh.visible = showEffect;
              } else {
                mesh.castShadow = true;
                mesh.receiveShadow = true;
              }
            }
          });
        }
      });
    }

    return () => {
      cancel = true;
      if (groupRef.current) {
        groupRef.current.clear();
      }
    };
  }, [packetList, showEffect]);

  useEffect(() => {
    if (packetList.length > 0) {
      let canPickUp = false;
      const opList: string[] = [];
      packetList.forEach((item) => {
        if (pickedList.includes(item.redPacketRecordId || '')) {
          return;
        }
        canPickUp = true;
        if (nearItem) {
          opList.push(
            GlobalSpace.addGameOp(
              GAME_OP_TYPE.CustomOp,
              () => {
                pickUpPacket(item.redPacketRecordId || '');
              },
              0,
              'Airdrop from ' + toFormatAccount(item.createAddress || ''),
              getCdnLink('./icon/option/pickUp.svg')
            )
          );
        }
      });
      setShowEffect(canPickUp);
      return () => {
        opList.forEach((op) => {
          GlobalSpace.removeGameOp(op);
        });
      };
    }
  }, [packetList, pickedList, nearItem]);

  const getDistance = () => {
    return new THREE.Vector3(
      myPlayer.position.x - packetData.position[0],
      0,
      myPlayer.position.z - packetData.position[2]
    ).length();
  };

  useEffect(() => {
    if (packetData) {
      const interval = setInterval(() => {
        setNearItem(getDistance() < packetData.distance);
        setPickedList(myPlayer.pickedList);
      }, 100);
      return () => {
        clearInterval(interval);
      };
    }
  }, [packetData]);

  return <group ref={groupRef}></group>;
}
