/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useRef } from 'react';
import { GLTF } from 'three-stdlib';
import Community_01 from './Community_01';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import SceneLoader from '../SceneLoader';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import { getPizzaActivity } from '@/game/TS/Activity/PizzaActivity';
import { useFrame } from '@react-three/fiber';
import { PizzaPointData } from '@/game/Config/PizzaPointConfig';
import ScenePizza from '@/game/TSX/Scene/Community/ScenePizza';
import CanvasUI from '@/game/TSX/Scene/Community/CanvasUI';
import { RedPacketConfig, RedPacketData } from '@/game/Config/RedPacketConfig';
import SceneRedPacket from '@/game/TSX/Scene/Community/SceneRedPacket';
import { useNetWork } from '@/game/TS/useNetWork';
import { IS_MOBILE_ENV } from '@/constant';
import { useDispatch } from 'react-redux';
import { setIsPizzaActivity } from '@/store/setting';

export default function Community() {
  const group = useRef<THREE.Group>(null);
  const dispatch = useDispatch();
  const { watchRoomStatus } = useNetWork();
  const [gltf_00, setGLTF_00] = React.useState<GLTF | null>(null);
  const [gltf_01, setGLTF_01] = React.useState<GLTF | null>(null);
  const [packetDataList, setPacketDataList] = React.useState<RedPacketData[]>([]);
  const [gameStart, setGameStart] = React.useState<boolean>(false);
  const [pointDataList, setPointDataList] = React.useState<PizzaPointData[]>([]);
  const [isEnterRoom, setIsEnterRoom] = React.useState<boolean>(false);
  const [hideList, setHideList] = React.useState<string[]>(['stop']);
  const [pizzaRunning, setPizzaRunning] = React.useState<boolean>(false);
  const pizzaActivity = getPizzaActivity();

  useEffect(() => {
    RedPacketConfig.getInstance().getAllData((list) => {
      setPacketDataList(list);
    });
    watchRoomStatus((status) => {
      setIsEnterRoom(status.isEnterRoom);
    });
    LoaderUtil.loadGlb('./space/glb/Community_00.glb', (gltf) => {
      setGLTF_00(gltf as any);
    });
  }, []);

  useEffect(() => {
    if (gltf_00) {
      LoaderUtil.loadGlb('./space/glb/Community_01.glb', (gltf) => {
        setGLTF_01(gltf as any);
      });
    }
  }, [gltf_00]);

  const loaded = (haveWall: boolean) => {
    if (haveWall) {
      const myPlayer = GetMyPlayer();
      myPlayer.callAppApi(AppGameApiKey.refreshRank);
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, false);
    }
  };

  useEffect(() => {
    if (gameStart) {
      const activityData = pizzaActivity.getActivityData();
      const timerList: NodeJS.Timeout[] = [];
      const now = Date.now();
      const startRunningTime = Math.max(0, now - activityData.startTime);
      activityData.pizzaData.forEach((item) => {
        if ((item.second + item.duration) * 1000 < startRunningTime) {
          return;
        }
        timerList.push(
          setTimeout(
            () => {
              setPizzaRunning(true);
              setPointDataList(item.pizzaDataList);
            },
            Math.max(0, item.second * 1000 - startRunningTime)
          )
        );
        timerList.push(
          setTimeout(
            () => {
              setPointDataList([]);
              setPizzaRunning(false);
            },
            (item.second + item.duration) * 1000 - startRunningTime
          )
        );
        dispatch(setIsPizzaActivity(true));
        setHideList([
          'stop',
          'A_display',
          'A_ditaiA',
          'A_LOGO',
          'A_logo_01',
          'A_logo_02',
          'A_logo_03',
          'A_logo_04',
          'A_p',
          'A_rotate_02',
          'A_rotate_02_1',
          'A_rotate_03',
          'A_tuya',
        ]);
      });

      return () => {
        timerList.forEach((item) => {
          clearTimeout(item);
        });
      };
    } else {
      setPointDataList([]);
      setHideList(['stop']);
      dispatch(setIsPizzaActivity(false));
    }
  }, [gameStart]);

  useFrame(() => {
    const now = Date.now();
    const activityData = pizzaActivity.getActivityData();
    setGameStart(
      activityData.startTime < now &&
        now <= activityData.endTime &&
        activityData.pizzaData.length > 0
    );
  });

  return (
    <group ref={group} dispose={null} userData={{ camCollisionListener: true }}>
      {!gameStart && !IS_MOBILE_ENV && <CanvasUI />}
      {/*{gameStart && <ScenePizzaNpc running={pizzaRunning} />}*/}
      {gltf_01 && <Community_01 gltf={gltf_01} />}
      <>
        {isEnterRoom &&
          !gameStart &&
          packetDataList.length > 0 &&
          packetDataList.map((item) => {
            return <SceneRedPacket key={item.id} packetData={item} />;
          })}
      </>
      <group name="Scene" userData={{ camCollisionListener: true }}>
        {gltf_00 && (
          <SceneLoader
            loaded={loaded}
            gltf={gltf_00}
            noShadowList={['sky', 'door', 'door001']}
            hideList={hideList}
          />
        )}
      </group>
      <>
        {pointDataList.length > 0 &&
          pointDataList.map((item, index) => {
            return <ScenePizza key={item.id} pointData={item} />;
          })}
      </>
    </group>
  );
}
