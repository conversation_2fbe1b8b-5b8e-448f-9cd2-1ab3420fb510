/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import Room from './Room/Room';
import Door from './Door';
import Island from './Island/Island';
import { ISettingState, SCENE_TYPE } from '@/constant/type';
import GlobalSpace from '@/game/Global/GlobalSpace';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import OnlineWorld from './OnlineWorld/OnlineWorld';
import { MapConfig, MapData } from '@/game/Config/MapConfig';
import Npc from '@/game/TSX/Scene/Npc';
import SceneParticle from '@/game/TSX/Scene/SceneParticle';
import SceneTree from '@/game/TSX/Scene/SceneTree';
import ParticleSystem, { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import SceneStone from '@/game/TSX/Scene/SceneStone';
import SceneFishingArea from '@/game/TSX/Scene/Fishing/SceneFishingArea';
import Community from '@/game/TSX/Scene/Community/Community';
import { useNetWork } from '@/game/TS/useNetWork';
import SceneBgm from '@/game/TSX/Music/SceneBgm';
import SceneItemDrop from '@/game/TSX/Scene/SceneItemDrop';
import { IS_MOBILE_ENV } from '@/constant';
import { useDispatch, useSelector } from 'react-redux';
import { setIsPizzaActivity } from '@/store/setting';
import ScenePetShed from '@/game/TSX/Scene/ScenePetShed';
import { useThree } from '@react-three/fiber';

function ChangeLight({
  shadowSize,
  directionalLightColor,
  directionalLightIntensity,
  ambientLightColor1,
  ambientLightColor2,
  ambientLightIntensity,
}: {
  shadowSize: number;
  directionalLightColor: THREE.Color;
  directionalLightIntensity: number;
  ambientLightColor1: THREE.Color;
  ambientLightColor2: THREE.Color;
  ambientLightIntensity: number;
}) {
  const [sceneLoading, setSceneLoading] = useState<boolean>(true);
  useEffect(() => {
    const loadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (value) => {
        setSceneLoading(value);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, loadingKey);
    };
  }, []);

  useEffect(() => {
    if (sceneLoading) {
      return;
    }
    let directionalLight: THREE.DirectionalLight;
    let camera: THREE.OrthographicCamera;
    const lightKey = GlobalSpaceEvent.ListenKeyDataChange<THREE.DirectionalLight>(
      GlobalDataKey.DirectionalLight,
      (light) => {
        light.color = new THREE.Color(directionalLightColor);
        light.intensity = directionalLightIntensity;
        directionalLight = light;
        camera = light.shadow.camera.clone();
        camera.top = 5;
        camera.bottom = -5;
        camera.left = -5;
        camera.right = 5;
        camera.updateProjectionMatrix();
        // if (shadowSize > 10) {
        //     light.shadow.mapSize.width = 1024;
        //     light.shadow.mapSize.height = 1024;
        // } else {
        //     light.shadow.mapSize.width = 2048;
        //     light.shadow.mapSize.height = 2048;
        // }
      }
    );

    const ambientLightKey = GlobalSpaceEvent.ListenKeyDataChange<THREE.HemisphereLight>(
      GlobalDataKey.AmbientLight,
      (light) => {
        light.color = new THREE.Color(ambientLightColor1);
        light.groundColor = new THREE.Color(ambientLightColor2);
        light.intensity = ambientLightIntensity;
      }
    );

    const cancelKey = GlobalSpace.whatCharacterPosition((position) => {
      if (directionalLight && camera) {
        // 将物体中心从世界坐标系转换到摄像机坐标系
        const cameraCenter = position.clone().project(camera); // 通过 .project(camera) 转换到摄像机坐标系
        directionalLight.shadow.camera.top = cameraCenter.y * 5 + shadowSize;
        directionalLight.shadow.camera.bottom = cameraCenter.y * 5 - shadowSize;
        directionalLight.shadow.camera.left = cameraCenter.x * 5 - shadowSize;
        directionalLight.shadow.camera.right = cameraCenter.x * 5 + shadowSize;
        // console.log('cameraCenter', cancelKey, cameraCenter, directionalLight.shadow.camera.top, directionalLight.shadow.camera.bottom, directionalLight.shadow.camera.left, directionalLight.shadow.camera.right)
        directionalLight.shadow.camera.updateProjectionMatrix();
      }
    });
    return () => {
      GlobalSpace.cancelCharacterPositionCallback(cancelKey);
      GlobalSpaceEvent.RemoveListener<THREE.DirectionalLight>(
        GlobalDataKey.DirectionalLight,
        lightKey
      );
      GlobalSpaceEvent.RemoveListener<THREE.HemisphereLight>(
        GlobalDataKey.AmbientLight,
        ambientLightKey
      );
    };
  }, [sceneLoading]);

  return null;
}

function LoadingScenes({ mapData }: { mapData: MapData | null }) {
  const mapDataList = useMemo(() => [] as MapData[], []);
  const dispatch = useDispatch();
  const curSceneType = mapData ? mapData.id : SCENE_TYPE.None;
  const [_, setMapCount] = useState<number>(0);
  useEffect(() => {
    if (IS_MOBILE_ENV) {
      return;
    }
    dispatch(setIsPizzaActivity(false));
    if (mapData) {
      if (mapDataList.includes(mapData)) {
        GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, false);
        return;
      }
      mapDataList.push(mapData as MapData);
      setMapCount(mapDataList.length);
    }
  }, [mapData, mapDataList]);

  if (IS_MOBILE_ENV) {
    if (mapData) {
      return <LoadingScene mapData={mapData} curSceneType={curSceneType} />;
    }
    return null;
  }
  return (
    <>
      {mapDataList.map((_mapData, _) => {
        return <LoadingScene mapData={_mapData} curSceneType={curSceneType} key={_mapData.id} />;
      })}
    </>
  );
}

function LoadingScene({ mapData, curSceneType }: { mapData: MapData; curSceneType: SCENE_TYPE }) {
  const sceneType = mapData.id as SCENE_TYPE;
  const groupRef = useRef<THREE.Group>(null);
  useEffect(() => {
    if (groupRef.current) {
      groupRef.current.visible = sceneType === curSceneType;
    }
  }, [groupRef.current, curSceneType]);
  return (
    <group
      ref={groupRef}
      key={sceneType}
      position={[mapData.offset[0], mapData.offset[1], mapData.offset[2]]}>
      {sceneType == SCENE_TYPE.Room && <Room />}
      {sceneType == SCENE_TYPE.Island && <Island />}
      {sceneType == SCENE_TYPE.Community && <Community />}
      {sceneType == SCENE_TYPE.OnlineWorld && <OnlineWorld />}
    </group>
  );
}

export default function SceneManager({ onClaimPotato }: { onClaimPotato?: () => void }) {
  const { camera } = useThree();
  const [sceneType, setSceneType] = useState<SCENE_TYPE>(SCENE_TYPE.None);
  const [sceneLoading, setSceneLoading] = useState<boolean>(true);
  const [isConnect, setIsConnect] = useState<boolean>(false);
  const { onlineMultiplayer, isPizzaActivity } = useSelector(
    (state: { SettingReducer: ISettingState }) => state.SettingReducer
  );
  const [mapData, setMapData] = useState<MapData | null>(null);
  const { enterRoom, leaveRoom } = useNetWork();
  const particleSystem = getParticleSystem();
  useEffect(() => {
    if (camera){
      particleSystem.setCamera(camera);
    }else {
      particleSystem.setCamera(null);
    }
  }, [camera]);
  useEffect(() => {
    const sceneTypeKey = GlobalSpaceEvent.ListenKeyDataChange(
      GlobalDataKey.SceneType,
      (_sceneType: SCENE_TYPE) => {
        setSceneType(_sceneType);
        GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, true);
        if (_sceneType !== SCENE_TYPE.Community) {
          leaveRoom();
        }
      }
    );

    const loadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (value) => {
        setSceneLoading(value);
      }
    );

    const connectKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.IsSocketConnected,
      (value) => {
        setIsConnect(value);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, sceneTypeKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, loadingKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.IsSocketConnected, connectKey);
    };
  }, [leaveRoom]);

  useEffect(() => {
    if (!sceneLoading && isConnect && onlineMultiplayer) {
      if (sceneType !== SCENE_TYPE.Room) {
        enterRoom(sceneType, 0);
      }
    } else {
      leaveRoom();
    }
  }, [sceneType, sceneLoading, isConnect, onlineMultiplayer, enterRoom, leaveRoom]);

  useEffect(() => {
    if (sceneType == SCENE_TYPE.None) return;
    let isCancel = false;
    MapConfig.getInstance().getMapData(sceneType, (data) => {
      if (isCancel) return;
      setMapData(null);
      setTimeout(() => {
        setMapData(data);
        MapConfig.getInstance().setCurMapData(data);
      }, 1);
    });
    return () => {
      isCancel = true;
    };
  }, [sceneType]);

  return (
    <>
      {mapData &&
        mapData.doorIds.length > 0 &&
        mapData.doorIds.map((doorId, index) => <Door doorId={doorId} key={doorId + index} />)}
      {!isPizzaActivity &&
        mapData &&
        mapData.npcIds.length > 0 &&
        mapData.npcIds.map((npcId, index) => <Npc npcId={npcId} key={npcId + index} />)}
      {mapData &&
        mapData.particleIds.length > 0 &&
        mapData.particleIds.map((particleId, index) => (
          <SceneParticle key={particleId + index} particleId={particleId} />
        ))}
      {mapData &&
        mapData.treeIds.length > 0 &&
        mapData.treeIds.map((treeId, index) => <SceneTree key={treeId + index} treeId={treeId} />)}
      {mapData &&
        mapData.stoneIds.length > 0 &&
        mapData.stoneIds.map((stoneId, index) => (
          <SceneStone key={stoneId + index} stoneId={stoneId} />
        ))}
      {mapData &&
        mapData.fishingAreaIds.length > 0 &&
        mapData.fishingAreaIds.map((areaId, index) => (
          <SceneFishingArea key={areaId + index} areaId={areaId} />
        ))}
      {mapData &&
        mapData.itemDropIds.length > 0 &&
        mapData.itemDropIds.map((dropId, index) => (
          <SceneItemDrop key={dropId + index} dropId={dropId} />
        ))}
      {mapData &&
        mapData.petShedIds.length > 0 &&
        mapData.petShedIds.map((shedId, index) => (
          <ScenePetShed key={shedId + index} shedId={shedId} />
        ))}
      {sceneType === SCENE_TYPE.Room && (
        <ChangeLight
          directionalLightColor={new THREE.Color(0xffffff)}
          directionalLightIntensity={1}
          ambientLightColor1={new THREE.Color(0xffffff)}
          ambientLightColor2={new THREE.Color(0x7d8e95)}
          ambientLightIntensity={2.5}
          shadowSize={5}
        />
      )}
      {sceneType !== SCENE_TYPE.Room && (
        <ChangeLight
          directionalLightColor={new THREE.Color(0xffffff)}
          directionalLightIntensity={1.3}
          ambientLightColor1={new THREE.Color(0xffffff)}
          ambientLightColor2={new THREE.Color(0xced3ff)}
          ambientLightIntensity={2}
          shadowSize={10}
        />
      )}
      <LoadingScenes mapData={mapData} />
      <SceneBgm mapData={mapData} />
      <ParticleSystem />
    </>
  );
}
