/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useMemo, useRef } from 'react';
import { useAppSelector } from '@/hooks/useStore';
import { IGameState } from '@/constant/type';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import PetEntity, { PetFollowData } from '@/game/TSX/Pet/PetEntity';
import { PetStatus } from '@/constant/enum';

interface PetEntityData {
  serverId: string;
  petTag: string;
  petName: string;
  petFollowData: PetFollowData;
  topIconName:string;
}

export default function PetModelList({ petId }: { petId: string }) {
  const { petList } = useAppSelector((state: { GameReducer: IGameState }) => state.GameReducer);
  const petFollowPositionList = useRef<THREE.Vector3[]>([]);
  const totalPetList = useMemo(() => {
    const myPlayer = GetMyPlayer();
    const list: PetEntityData[] = [];
    petFollowPositionList.current = [myPlayer.position];
    let followIndex = 0;
    petList
      .filter((item) => item.petStatus !== PetStatus.REST)
      .sort((a, b) => (a.followSlot < b.followSlot ? -1 : 1))
      .forEach((item) => {
        const petFollowData: PetFollowData = {
          curPosition: new THREE.Vector3(NaN, NaN, NaN),
          followSlot: 0,
          followPositions: petFollowPositionList.current,
        };
        if (item.followSlot > 0) {
          followIndex++;
          petFollowData.followSlot = followIndex;
          petFollowPositionList.current.push(petFollowData.curPosition);
        }
        list.push({
          serverId: item._id,
          petTag: item.tag,
          petName: item.petName,
          petFollowData,
          topIconName: item.affinity,
        });
      });
    return list;
  }, [petList]);

  return (
    <>
      {totalPetList.map((item) => (
        // <PetModel key={item.serverId} petFollowData={item} />
        <PetEntity
          key={item.serverId}
          serverId={item.serverId}
          petTag={item.petTag}
          petName={item.petName}
          petFollowData={item.petFollowData}
          topIconName={item.topIconName}
        />
      ))}
    </>
  );
}
