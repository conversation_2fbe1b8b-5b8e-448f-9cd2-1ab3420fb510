/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/
import * as THREE from 'three';
import { Entity } from '@/game/TS/Entity/Entity';
import { useCallback, useEffect, useState } from 'react';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import { TreeConfig, TreeData } from '@/game/Config/TreeConfig';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { AnimationType } from '@/game/TSX/Pet/PetEntity';
import { UseGameState } from '@/game_lib/stores/useGame';
import { AudioSystem } from '@/game/Global/GlobalAudioSystem';
import usePetWork from '@/hooks/usePetWork';
import { PetFeaturesData } from '@/game/Config/PetFeaturesConfig';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import useWalkPath from '@/game/useWalkPath';

enum CutTreeStatus {
  Idle,
  FindTree,
  MoveToTree,
  HitTree,
}

function FindCloseTree({
  entity,
  findSuccess,
}: {
  entity: Entity;
  findSuccess: (treeData: TreeData) => void;
}) {
  const findAliveTree = useCallback(
    (animation: EntityAnimation, position: THREE.Vector3) => {
      const treeDataMap = TreeConfig.getInstance().getTreeDataMap();
      const aliveObjectList: { data: TreeData; id: number; distance: number }[] = [];
      treeDataMap.forEach((item) => {
        const treeData = item.data;
        const object = TreeConfig.getInstance().getObject(treeData.id);
        if (object.status === 'dead') {
          return;
        }
        if (object.petUse) {
          return;
        }
        const distance = position.distanceTo(
          new THREE.Vector3(treeData.position[0], position.y, treeData.position[2])
        );
        aliveObjectList.push({ id: treeData.id, data: treeData, distance: distance });
      });
      aliveObjectList.sort((a, b) => a.distance - b.distance);
      if (aliveObjectList.length > 0) {
        animation.playAnimation(AnimationType.Idle);
        findSuccess(aliveObjectList[0].data);
        return;
      }
      animation.playAnimation(AnimationType.Idle);
    },
    [findSuccess]
  );

  useEffect(() => {
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (!animation || !walkPoint) return;
    walkPoint.position && findAliveTree(animation, walkPoint.position);
    const interval = setInterval(() => {
      walkPoint.position && findAliveTree(animation, walkPoint.position);
    }, 3000);
    return () => {
      clearInterval(interval);
    };
  }, [entity, findAliveTree]);
  return null;
}

function MoveToTree({
  entity,
  treeData,
  moveToTreeEnd,
}: {
  entity: Entity;
  treeData: TreeData;
  moveToTreeEnd: () => void;
}) {
  const { path, pathIndex, setTargetPosition } = useWalkPath({ entity });
  useEffect(() => {
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (path.length > 0 && animation) {
      animation.playAnimation(AnimationType.Walk);
      if (pathIndex === path.length) {
        animation.playAnimation(AnimationType.Idle);
        moveToTreeEnd();
      }
    }
  }, [path, pathIndex, moveToTreeEnd, entity]);

  useEffect(() => {
    const centerPoint = new THREE.Vector3(-145, treeData.position[1], 13);
    const targetPoint = new THREE.Vector3(
      treeData.position[0],
      treeData.position[1],
      treeData.position[2]
    );
    const distance = centerPoint.distanceTo(targetPoint);
    const normal = targetPoint.clone().sub(centerPoint).normalize();
    const radius = (treeData.range[1] + treeData.range[2]) / 2 - 0.3;
    const target = centerPoint.clone().add(normal.multiplyScalar(distance - radius));
    setTargetPosition(target);
  }, [entity, treeData]);

  return null;
}

function HitTree({
  features,
  serverId,
  entity,
  treeData,
  hitTreeEnd,
}: {
  features: PetFeaturesData;
  serverId: string;
  entity: Entity;
  treeData: TreeData;
  hitTreeEnd: () => void;
}) {
  const particleSystem = getParticleSystem();
  const { handleSocketPetAxe } = usePetWork();
  const treeObject = TreeConfig.getInstance().getObject(treeData.id);
  const useGame = treeObject.useGame;
  const setTreeCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const damageTree = useCallback(
    (damage: number, position: THREE.Vector3, quaternion: THREE.Quaternion) => {
      if (treeObject.hp <= 0) {
        hitTreeEnd();
        return;
      }
      particleSystem.addParticle(
        position,
        quaternion,
        './particles/Effect_wood_0.json',
        treeData.hit_effect_scale,
        treeData.hit_effect_during
      );
      particleSystem.addParticle(
        position,
        quaternion,
        './particles/Effect_wood_1.json',
        treeData.hit_effect_scale,
        treeData.hit_effect_during
      );
      treeObject.hp -= damage;
      if (treeObject.hp > 0) {
        setTreeCurAnimation('shake');
      } else {
        setTreeCurAnimation('fall');
        hitTreeEnd();
        setTimeout(() => {
          treeObject.status = 'dead';
          handleSocketPetAxe(treeObject.tag, treeObject.id, serverId);
        }, treeData.tree_disappear_time);
        AudioSystem.playAudio(
          'scene_tree_' + treeData.id,
          TreeConfig.getInstance().getFallSound(treeData),
          () => {
            return true;
          }
        );
      }
    },
    [treeObject, hitTreeEnd, setTreeCurAnimation, treeData, handleSocketPetAxe, serverId]
  );

  useEffect(() => {
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    if (!animation || !walkPoint) return;
    let cancel = false;
    const attack = () => {
      animation.playAnimation(AnimationType.Action);
      walkPoint.setFacePosition(
        new THREE.Vector3(treeData.position[0], treeData.position[1], treeData.position[2])
      );
      setTimeout(() => {
        if (cancel || !walkPoint || !walkPoint.position) return;
        const treePos = new THREE.Vector3(
          treeData.position[0],
          treeData.position[1] + 0.5,
          treeData.position[2]
        );
        const facePos = new THREE.Vector3(
          walkPoint.position.x,
          treeData.position[1] + 0.5,
          walkPoint.position.y
        );
        const hitPos = treePos.clone();
        hitPos.add(facePos.clone().sub(treePos).normalize().multiplyScalar(treeData.radius));
        // hitPos.y = treePos.y
        const object = new THREE.Object3D();
        object.position.copy(treePos);
        object.lookAt(facePos);
        damageTree(features.damage, hitPos, object.quaternion);
      }, 700);
    };
    attack();
    const interval = setInterval(() => {
      attack();
    }, features.interval);
    return () => {
      cancel = true;
      clearInterval(interval);
    };
  }, [entity, damageTree, treeData, features]);

  return null;
}

function PetAxe({
  serverId,
  petEntity,
  features,
}: {
  serverId: string;
  petEntity: Entity;
  features: PetFeaturesData;
}) {
  const [status, setStatus] = useState(CutTreeStatus.FindTree);
  const [targetTreeData, setTargetTreeData] = useState<TreeData | null>(null);

  useEffect(() => {
    if (!targetTreeData) return;
    const treeObject = TreeConfig.getInstance().getObject(targetTreeData.id);
    treeObject.petUse = true;
    return () => {
      treeObject.petUse = false;
    };
  }, [targetTreeData]);

  useEffect(() => {
    if (status === CutTreeStatus.Idle) {
      const timer = setTimeout(() => {
        setStatus(CutTreeStatus.FindTree);
      }, 2000);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [status]);

  const findCloseTreeSuccess = (treeData: TreeData) => {
    setTargetTreeData(treeData);
    setStatus(CutTreeStatus.MoveToTree);
  };

  const moveToTreeEnd = () => {
    setStatus(CutTreeStatus.HitTree);
  };

  const hitTreeEnd = () => {
    setStatus(CutTreeStatus.Idle);
  };

  return (
    <>
      {status === CutTreeStatus.FindTree && (
        <FindCloseTree entity={petEntity} findSuccess={findCloseTreeSuccess} />
      )}
      {status === CutTreeStatus.MoveToTree && targetTreeData && (
        <MoveToTree entity={petEntity} treeData={targetTreeData} moveToTreeEnd={moveToTreeEnd} />
      )}
      {status === CutTreeStatus.HitTree && targetTreeData && (
        <HitTree
          features={features}
          serverId={serverId}
          entity={petEntity}
          treeData={targetTreeData}
          hitTreeEnd={hitTreeEnd}
        />
      )}
    </>
  );
}

export default PetAxe;
