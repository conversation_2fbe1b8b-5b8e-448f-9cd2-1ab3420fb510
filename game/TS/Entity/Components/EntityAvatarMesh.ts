import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateAvatar = 1,
  UpdateScale,
  AddOption,
}

export class EntityAvatarMesh extends EntityComponent {
  avatarData: game.IAvatarData = {};
  scale = 1;
  optionMap: Map<string, game.CommonMessage> = new Map();

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateAvatar, game.AvatarData, (data: game.IAvatarData) => {
      this.avatarData = data;
      this.selfUpdate();
    });
    this.registerAction(ActionType.UpdateScale, game.CommonMessage, (data: game.CommonMessage) => {
      this.scale = Number(data.messageList[0]);
      this.selfUpdate();
    });
    this.registerAction(ActionType.AddOption, game.CommonMessage, (data: game.CommonMessage) => {
      const optionType = data.messageList[0];
      this.optionMap.set(optionType, data);
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.AvatarMesh;
  }

  override syncToServer() {
    this.sendAvatar();
    this.sendScale();
  }

  private sendAvatar() {
    this.sendAction(ActionType.UpdateAvatar, game.AvatarData.create(this.avatarData).toJSON());
  }

  private sendScale() {
    this.sendAction(
      ActionType.UpdateScale,
      game.CommonMessage.create({
        messageList: [String(this.scale)],
      }).toJSON()
    );
  }

  private addOption(option: game.CommonMessage) {
    const optionType = option.messageList[0];
    this.optionMap.set(optionType, option);
    this.selfUpdate();
    this.sendAction(ActionType.AddOption, option.toJSON());
  }

  setAvatar(avatarData: game.IAvatarData) {
    this.avatarData = avatarData;
    this.selfUpdate();
    this.sendAvatar();
  }

  setScale(scale: number) {
    this.scale = scale;
    this.selfUpdate();
    this.sendScale();
  }

  setVisible(visible: boolean, objectName: string) {
    this.addOption(
      game.CommonMessage.create({
        messageList: ['setVisible', objectName, visible ? '1' : '0'],
      })
    );
  }
}
