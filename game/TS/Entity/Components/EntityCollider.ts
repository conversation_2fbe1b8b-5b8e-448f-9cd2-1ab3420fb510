import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateMeshName = 1,
}

export class EntityCollider extends EntityComponent {
  meshName = '';

  override init() {
    super.init();

    this.registerAction(
      ActionType.UpdateMeshName,
      game.CommonMessage,
      (data: game.CommonMessage) => {
        this.meshName = data.messageList[0];
        this.selfUpdate();
      }
    );
  }

  override getType() {
    return ComponentType.Collider;
  }

  override syncToServer() {
    this.sendMeshName();
  }

  sendMeshName() {
    this.sendAction(
      ActionType.UpdateMeshName,
      game.CommonMessage.create({
        messageList: [this.meshName],
      }).toJSON()
    );
  }

  setMeshName(meshName: string) {
    this.meshName = meshName;
    this.selfUpdate();
    this.sendMeshName();
  }
}
