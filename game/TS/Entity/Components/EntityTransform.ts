import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';
import * as THREE from 'three';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';

enum ActionType {
  TransformPosition = 1,
}

function decodePosition(data: number) {
  return data / 100 - 200;
}

function encodePosition(data: number) {
  return Math.floor((data + 200) * 100);
}

function decodeRotation(data: number) {
  return data / 1000 - 1;
}

function encodeRotation(data: number) {
  return Math.floor((data + 1) * 1000);
}

export class EntityTransform extends EntityComponent {
  position: THREE.Vector3 = new THREE.Vector3();
  rotation: THREE.Quaternion = new THREE.Quaternion();
  scale = 1;

  forcePosition = false;

  rootObject: THREE.Object3D | null = null;

  private lastPosition: THREE.Vector3 | null = null;
  private lastRotation: THREE.Quaternion | null = null;
  private lastUpdateTime = 0;

  override init() {
    super.init();

    this.registerAction(ActionType.TransformPosition, game.Transform, (data: game.Transform) => {
      this.position.set(decodePosition(data.x), decodePosition(data.y), decodePosition(data.z));
      this.rotation.set(
        decodeRotation(data.quaternionX),
        decodeRotation(data.quaternionY),
        decodeRotation(data.quaternionZ),
        decodeRotation(data.quaternionW)
      );
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.Transform;
  }

  override syncToServer() {
    this.sendTransformPosition();
  }

  private sendTransformPosition() {
    const now = Date.now();
    if (now - this.lastUpdateTime < 80) {
      return;
    }
    if (!this.lastPosition) {
      this.lastPosition = new THREE.Vector3();
    }
    if (!this.lastRotation) {
      this.lastRotation = new THREE.Quaternion();
    }
    this.lastUpdateTime = now;
    this.lastPosition.copy(this.position);
    this.lastRotation.copy(this.rotation);
    this.sendAction(
      ActionType.TransformPosition,
      game.Transform.create({
        x: encodePosition(this.position.x),
        y: encodePosition(this.position.y),
        z: encodePosition(this.position.z),
        quaternionX: encodeRotation(this.rotation.x),
        quaternionY: encodeRotation(this.rotation.y),
        quaternionZ: encodeRotation(this.rotation.z),
        quaternionW: encodeRotation(this.rotation.w),
      }).toJSON()
    );
  }

  setPosition(position: THREE.Vector3, rotation: THREE.Quaternion, force = false) {
    this.forcePosition = force;
    this.rotation.copy(rotation);
    this.position.copy(position);
    this.selfUpdate();

    if (
      !this.lastPosition ||
      !this.lastRotation ||
      this.position.distanceTo(this.lastPosition) > 0.15 ||
      this.rotation.angleTo(this.lastRotation) > 0.5
    ) {
      this.sendTransformPosition();
    }
  }

  setRootObject(root: THREE.Object3D) {
    this.rootObject = root;
    this.entity.setEntityRoot(root);
    this.selfUpdate();
  }

  faceToPosition(position: THREE.Vector3) {
    const tempObject = new THREE.Object3D();
    tempObject.position.copy(this.position);
    tempObject.lookAt(new THREE.Vector3(position.x, tempObject.position.y, position.z));
    this.setPosition(tempObject.position, tempObject.quaternion);
  }
}
