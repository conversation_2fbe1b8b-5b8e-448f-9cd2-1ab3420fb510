import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateCount = 1,
  UpdateTick = 2,
}

export class EntityPizzaMesh extends EntityComponent {
  pizzaCount = 0;
  tick = '';

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateCount, game.CommonMessage, (data: game.CommonMessage) => {
      this.pizzaCount = Number(data.messageList[0]);
      this.selfUpdate();
    });
    this.registerAction(ActionType.UpdateTick, game.CommonMessage, (data: game.CommonMessage) => {
      this.tick = data.messageList[0];
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.PizzaMesh;
  }

  override syncToServer() {
    this.sendPizzaCount();
    this.sendTick();
  }

  private sendPizzaCount() {
    this.sendAction(
      ActionType.UpdateCount,
      game.CommonMessage.create({
        messageList: [String(this.pizzaCount)],
      }).toJSON()
    );
  }

  private sendTick() {
    this.sendAction(
      ActionType.UpdateTick,
      game.CommonMessage.create({
        messageList: [this.tick],
      }).toJSON()
    );
  }

  setTick(glbUrl: string) {
    this.tick = glbUrl;
    this.selfUpdate();
    this.sendTick();
  }

  setPizzaCount(count: number) {
    this.pizzaCount = count;
    this.selfUpdate();
    this.sendPizzaCount();
  }
}
