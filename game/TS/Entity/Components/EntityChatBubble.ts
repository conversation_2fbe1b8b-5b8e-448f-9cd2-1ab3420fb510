import * as THREE from 'three';
import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';
import { SpeakUtilData } from '@/game/TSX/Util/SpeakUtil';
import { AnswerTask } from '@/game/TSX/SceneUI/TopAnswerUI';

enum ActionType {
  UpdateChat = 1,
}

export class EntityChatBubble extends EntityComponent {
  chatWord = '';
  endTime = 0;

  answerTask: AnswerTask | null = null;
  speakUtil: SpeakUtilData = new SpeakUtilData();
  audioKey = THREE.MathUtils.generateUUID();

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateChat, game.CommonMessage, (data: game.CommonMessage) => {
      this.chatWord = data.messageList[0];
      this.endTime = Number(data.messageList[1]);
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.ChatBubble;
  }

  override syncToServer() {
    this.sendChat();
  }

  private sendChat() {
    this.sendAction(
      ActionType.UpdateChat,
      game.CommonMessage.create({
        messageList: [this.chatWord, String(this.endTime)],
      }).toJSON()
    );
  }

  setChat(word: string, endTime: number) {
    this.chatWord = word;
    this.endTime = endTime;
    this.selfUpdate();
    this.sendChat();
  }

  createAnswer() {
    const task = new AnswerTask(this.audioKey);
    this.answerTask = task;
    this.selfUpdate();
    return task;
  }

  stopAnswer() {
    if (this.answerTask) {
      this.answerTask.stop();
    }
  }

  clearAnswer() {
    if (this.answerTask) {
      this.stopAnswer();
      this.answerTask = null;
      this.selfUpdate();
    }
  }
}
