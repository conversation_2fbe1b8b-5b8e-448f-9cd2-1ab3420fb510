import { ComponentType } from '../Enum';
import { Entity } from '../Entity';
import { transformBuffer } from '@/game/Proto/protobuf-until';

export class EntityComponent {
  private actionTypeClassMap = new Map<
    number,
    {
      dataClass: any | undefined;
      handler: (data: any) => void;
    }
  >();
  protected entity: Entity;
  private updateCallback: (() => void) | null = null;

  constructor(entity: Entity) {
    this.entity = entity;
    this.init();
  }

  isClientComponent() {
    return this.entity.isClientEntity();
  }

  getType() {
    return ComponentType.Node;
  }

  syncToServer() {
    //提供override
  }

  protected init() {
    //提供override
  }

  protected registerAction(
    actionType: number,
    dataClass: any | undefined,
    handler: (data: any) => void
  ) {
    this.actionTypeClassMap.set(actionType, {
      dataClass,
      handler,
    });
  }

  protected sendAction(actionType: number, data: any) {
    const isClientEntity = this.entity.isClientEntity();
    if (!isClientEntity) {
      return;
    }
    const registerData = this.actionTypeClassMap.get(actionType);
    if (!registerData) {
      console.error('not found action type', actionType);
      return;
    }

    let buffer: Uint8Array = new Uint8Array(0);
    if (registerData.dataClass) {
      // 创建聊天消息包装器
      const actonData = registerData.dataClass.create(data);
      buffer = registerData.dataClass.encode(actonData).finish();
    }

    this.entity.sendComponentAction(this.getType(), actionType, buffer);
  }

  handleAction(actionType: number, uint8Array: Uint8Array) {
    const registerData = this.actionTypeClassMap.get(actionType);
    if (!registerData) {
      console.error('not found action type', this.getType(), actionType);
      return;
    }
    let data: any = {};

    if (registerData.dataClass && uint8Array) {
      const processedBuffer = transformBuffer(uint8Array);
      if (processedBuffer) {
        // 解码内部数据
        const innerMessage = registerData.dataClass.decode(processedBuffer);
        data = innerMessage.toJSON();
      }
    }
    registerData.handler(data);
  }

  registerUpdateCallback(callback: () => void) {
    this.updateCallback = callback;
  }

  selfUpdate() {
    this.updateCallback && this.updateCallback();
  }
}
