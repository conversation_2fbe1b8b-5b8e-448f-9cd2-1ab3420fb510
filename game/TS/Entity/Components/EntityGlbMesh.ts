import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateMesh = 1,
  UpdateScale,
  AddOption,
}

export class EntityGlbMesh extends EntityComponent {
  glbUrl = '';
  glbScale = 1;
  optionMap: Map<string, game.CommonMessage> = new Map();
  yawOffset = 0;

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateMesh, game.CommonMessage, (data: game.CommonMessage) => {
      this.glbUrl = data.messageList[0];
      this.selfUpdate();
    });
    this.registerAction(ActionType.UpdateScale, game.CommonMessage, (data: game.CommonMessage) => {
      this.glbScale = Number(data.messageList[0]);
      this.selfUpdate();
    });
    this.registerAction(ActionType.AddOption, game.CommonMessage, (data: game.CommonMessage) => {
      const optionType = data.messageList[0];
      this.optionMap.set(optionType, data);
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.GlbMesh;
  }

  override syncToServer() {
    this.sendGlbUrl();
    this.sendGlbScale();
    for (const option of this.optionMap.values()) {
      this.addOption(option);
    }
  }

  private sendGlbUrl() {
    this.sendAction(
      ActionType.UpdateMesh,
      game.CommonMessage.create({
        messageList: [this.glbUrl],
      }).toJSON()
    );
  }

  private sendGlbScale() {
    this.sendAction(
      ActionType.UpdateScale,
      game.CommonMessage.create({
        messageList: [String(this.glbScale)],
      }).toJSON()
    );
  }

  private addOption(option: game.CommonMessage) {
    const optionType = option.messageList[0];
    this.optionMap.set(optionType, option);
    this.selfUpdate();
    this.sendAction(ActionType.AddOption, option.toJSON());
  }

  setGlbUrl(glbUrl: string) {
    this.glbUrl = glbUrl;
    this.selfUpdate();
    this.sendGlbUrl();
  }

  setScale(scale: number) {
    this.glbScale = scale;
    this.selfUpdate();
    this.sendGlbScale();
  }

  setYawOffset(yOffset: number) {
    this.yawOffset = yOffset;
    this.selfUpdate();
  }

  setVisible(visible: boolean, objectName: string) {
    this.addOption(
      game.CommonMessage.create({
        messageList: ['setVisible', objectName, visible ? '1' : '0'],
      })
    );
  }

  setShadow(visible: boolean, objectName: string) {
    this.addOption(
      game.CommonMessage.create({
        messageList: ['setShadow', objectName, visible ? '1' : '0'],
      })
    );
  }
}
