import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateFaceData = 1,
  UpdateFaceUrl,
}

export type EntityFaceType = 'avatar' | 'pet' | 'npc';

export class EntityFace extends EntityComponent {
  faceMeshName = '';
  faceType: EntityFaceType = 'avatar';
  faceUrl = '';

  init() {
    super.init();

    this.registerAction(
      ActionType.UpdateFaceData,
      game.CommonMessage,
      (data: game.CommonMessage) => {
        this.faceMeshName = data.messageList[0];
        this.faceType = data.messageList[1] as EntityFaceType;
        this.selfUpdate();
      }
    );
    this.registerAction(
      ActionType.UpdateFaceUrl,
      game.CommonMessage,
      (data: game.CommonMessage) => {
        this.faceUrl = data.messageList[0];
        this.selfUpdate();
      }
    );
  }

  override getType() {
    return ComponentType.Face;
  }

  override syncToServer() {
    this.sendFaceData();
    this.sendFaceUrl();
  }

  private sendFaceData() {
    this.sendAction(
      ActionType.UpdateFaceData,
      game.CommonMessage.create({
        messageList: [this.faceMeshName, this.faceType],
      }).toJSON()
    );
  }

  private sendFaceUrl() {
    this.sendAction(
      ActionType.UpdateFaceUrl,
      game.CommonMessage.create({
        messageList: [this.faceUrl],
      }).toJSON()
    );
  }

  setFaceData(faceMeshName: string, faceType: EntityFaceType) {
    this.faceMeshName = faceMeshName;
    this.faceType = faceType;
    this.selfUpdate();
    this.sendFaceData();
  }

  setFaceUrl(faceUrl: string) {
    this.faceUrl = faceUrl;
    this.selfUpdate();
    this.sendFaceUrl();
  }
}
