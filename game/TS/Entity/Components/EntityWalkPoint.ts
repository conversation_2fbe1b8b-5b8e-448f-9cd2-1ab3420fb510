import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';
import * as THREE from 'three';

enum ActionType {
  TargetPoint = 1,
  FacePoint = 2,
}

function decodePosition(data: number) {
  return data / 100 - 200;
}

function encodePosition(data: number) {
  return Math.floor((data + 200) * 100);
}

export class EntityWalkPoint extends EntityComponent {
  point: THREE.Vector3 = new THREE.Vector3();
  jumpVelocity = 0;
  facePoint: THREE.Vector3 | null = null;
  //justClient
  position: THREE.Vector3 | null = null;
  quaternion: THREE.Quaternion | null = null;

  private arriveCallback: ((isTransform: boolean) => void) | null = null;

  override init() {
    super.init();

    this.registerAction(ActionType.TargetPoint, game.Transform, (data: game.Transform) => {
      this.point.set(decodePosition(data.x), decodePosition(data.y), decodePosition(data.z));
      this.jumpVelocity = (data.quaternionX || 0) / 100;
      this.selfUpdate();
    });

    this.registerAction(ActionType.FacePoint, game.Transform, (data: game.Transform) => {
      this.facePoint = new THREE.Vector3(
        decodePosition(data.x),
        decodePosition(data.y),
        decodePosition(data.z)
      );
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.WalkPoint;
  }

  override syncToServer() {
    this.sendTargetPoint();
    this.sendFacePosition();
  }

  private sendTargetPoint() {
    const quaternionX = this.jumpVelocity != 0 ? Math.floor(this.jumpVelocity * 100) : null;
    this.sendAction(
      ActionType.TargetPoint,
      game.Transform.create({
        x: encodePosition(this.point.x),
        y: encodePosition(this.point.y),
        z: encodePosition(this.point.z),
        quaternionX,
      }).toJSON()
    );
  }

  private sendFacePosition() {
    if (!this.facePoint) {
      return;
    }
    this.sendAction(
      ActionType.FacePoint,
      game.Transform.create({
        x: encodePosition(this.facePoint.x),
        y: encodePosition(this.facePoint.y),
        z: encodePosition(this.facePoint.z),
      }).toJSON()
    );
  }

  setTarget(position: THREE.Vector3, jumpVelocity = 0) {
    this.point.copy(position);
    this.jumpVelocity = jumpVelocity;
    this.sendTargetPoint();
    this.selfUpdate();
  }

  setFacePosition(position: THREE.Vector3) {
    this.facePoint = position;
    this.sendFacePosition();
    this.selfUpdate();
  }

  setArriveCallback(callback: (isTransform: boolean) => void) {
    this.arriveCallback = callback;
  }

  arrive(isTransform: boolean) {
    if (this.arriveCallback) {
      const tempCallback = this.arriveCallback;
      this.arriveCallback = null;
      tempCallback(isTransform);
    }
  }
}
