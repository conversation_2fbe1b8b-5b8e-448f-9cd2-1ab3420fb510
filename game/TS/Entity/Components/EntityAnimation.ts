import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  Current = 1,
  Default,
  Jump,
  ReplaceList,
}

export class EntityAnimation extends EntityComponent {
  curAnimation = '';
  jumpAnimation = '';
  defaultAnimation = '';
  replaceAnimations: string[] = [];
  private renameMap = new Map<string, string>();
  private lockAnimationEndTime = 0;

  override init() {
    super.init();

    this.registerAction(ActionType.Current, game.CommonMessage, (data: game.CommonMessage) => {
      this.curAnimation = data.messageList[0];
      this.selfUpdate();
    });
    this.registerAction(ActionType.Default, game.CommonMessage, (data: game.CommonMessage) => {
      this.defaultAnimation = data.messageList[0];
      this.selfUpdate();
    });
    this.registerAction(ActionType.Jump, game.CommonMessage, (data: game.CommonMessage) => {
      this.jumpAnimation = data.messageList[0];
      this.selfUpdate();
    });
    this.registerAction(ActionType.ReplaceList, game.CommonMessage, (data: game.CommonMessage) => {
      this.replaceAnimations = data.messageList;
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.Animation;
  }

  override syncToServer() {
    this.sendReplaceAnimations();
    this.sendCurrentAnimation();
    this.sendDefaultAnimation();
    this.sendJumpAnimation();
  }

  private sendCurrentAnimation() {
    this.sendAction(
      ActionType.Current,
      game.CommonMessage.create({
        messageList: [this.curAnimation],
      }).toJSON()
    );
  }

  private sendDefaultAnimation() {
    this.sendAction(
      ActionType.Default,
      game.CommonMessage.create({
        messageList: [this.defaultAnimation],
      }).toJSON()
    );
  }

  private sendJumpAnimation() {
    this.sendAction(
      ActionType.Jump,
      game.CommonMessage.create({
        messageList: [this.jumpAnimation],
      }).toJSON()
    );
  }

  private sendReplaceAnimations() {
    this.sendAction(
      ActionType.ReplaceList,
      game.CommonMessage.create({
        messageList: this.replaceAnimations,
      }).toJSON()
    );
  }

  playAnimation(curAnimation: string, speed = 1) {
    if (this.lockAnimationEndTime > Date.now()) {
      return false;
    }
    if (this.renameMap.has(curAnimation)) {
      curAnimation = this.renameMap.get(curAnimation) as string;
    }
    this.curAnimation = speed === 1 ? curAnimation : curAnimation + '|' + speed;
    this.selfUpdate();
    this.sendCurrentAnimation();
    return true;
  }

  lockAnimation(time: number) {
    this.lockAnimationEndTime = Date.now() + time;
  }

  setAnimationRename(useName: string, realName: string) {
    this.renameMap.set(useName, realName);
  }

  setDefaultAnimation(animation: string) {
    this.defaultAnimation = animation;
    this.selfUpdate();
    this.sendDefaultAnimation();
  }

  setJumpAnimation(animation: string) {
    this.jumpAnimation = animation;
    this.selfUpdate();
    this.sendJumpAnimation();
  }

  setReplaceAnimations(replaceAnimations: string[], cover = false) {
    if (cover) {
      this.replaceAnimations = [];
    }
    replaceAnimations.forEach((animationName) => {
      if (this.renameMap.has(animationName)) {
        animationName = this.renameMap.get(animationName) as string;
      }
      if (!this.replaceAnimations.includes(animationName)) {
        this.replaceAnimations.push(animationName);
      }
    });
    this.selfUpdate();
    this.sendReplaceAnimations();
  }

  isSameAnimation(animation: string) {
    if (this.renameMap.has(animation)) {
      animation = this.renameMap.get(animation) as string;
    }
    return this.curAnimation === animation;
  }
}
