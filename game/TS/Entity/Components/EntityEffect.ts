import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateEffect = 1,
}

export class EntityEffect extends EntityComponent {
  effectUrl = '';
  effectScale = 1;

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateEffect, game.CommonMessage, (data: game.CommonMessage) => {
      this.effectUrl = data.messageList[0];
      this.effectScale = Number(data.messageList[1]);
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.Effect;
  }

  override syncToServer() {
    this.sendEffect();
  }

  private sendEffect() {
    this.sendAction(
      ActionType.UpdateEffect,
      game.CommonMessage.create({
        messageList: [this.effectUrl, String(this.effectScale)],
      }).toJSON()
    );
  }

  setEffect(effectUrl: string, effectScale: number) {
    this.effectUrl = effectUrl;
    this.effectScale = effectScale;
    this.selfUpdate();
    this.sendEffect();
  }
}
