import { EntityEvent } from '@/game/TS/Entity/Enum';

export class EntityListener {
  private static instance: EntityListener;
  private listenerMap: Map<EntityEvent, ((data: any) => void)[]> = new Map();

  static getInstance() {
    if (!EntityListener.instance) {
      EntityListener.instance = new EntityListener();
    }
    return EntityListener.instance;
  }

  addListener(event: EntityEvent, listener: (data: any) => void) {
    if (!this.listenerMap.has(event)) {
      this.listenerMap.set(event, []);
    }
    this.listenerMap.get(event)?.push(listener);
  }

  removeListener(event: EntityEvent, listener: (data: any) => void) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  notifyListener(event: EntityEvent, data: any) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      listeners.forEach((listener) => {
        listener(data);
      });
    }
  }
}
