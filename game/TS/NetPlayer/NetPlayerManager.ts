import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { NetPlayerListener } from '@/game/TS/NetPlayer/NetPlayerListener';
import { NetPlayerEvent } from '@/game/TS/NetPlayer/NetPlayerEvent';
import { NetPlayer } from '@/game/TS/NetPlayer/NetPlayer';

export class NetPlayerManager {
  private static instance: NetPlayerManager;

  private netPlayers: NetPlayer[] = [];

  static getInstance() {
    if (!NetPlayerManager.instance) {
      NetPlayerManager.instance = new NetPlayerManager();
    }
    return NetPlayerManager.instance;
  }

  getPlayerList() {
    const btcAddressList: string[] = [];
    for (let i = 0; i < this.netPlayers.length; i++) {
      const player = this.netPlayers[i];
      btcAddressList.push(player.getAddress());
    }
    return btcAddressList;
  }

  getNetPlayerList() {
    return this.netPlayers;
  }

  findOtherPlayer(btcAddress: string) {
    return this.netPlayers.find((player) => player.getAddress() === btcAddress);
  }

  deleteOtherPlayer(btcAddress: string) {
    for (let i = 0; i < this.netPlayers.length; i++) {
      const player = this.netPlayers[i];
      if (player.getAddress() === btcAddress) {
        this.netPlayers.splice(i, 1);
        NetPlayerListener.getInstance().notifyListener(NetPlayerEvent.NetPlayerChange, {});
        break;
      }
    }
  }

  addOtherPlayer(btcAddress: string, entityId: number) {
    const newPlayer = new NetPlayer(btcAddress, entityId);
    this.netPlayers.push(newPlayer);
    NetPlayerListener.getInstance().notifyListener(NetPlayerEvent.NetPlayerChange, {});
    return newPlayer;
  }

  checkPlayerDistance(maxPlayers: number) {
    if (this.netPlayers.length === 0) return;

    const list: { distance: number; player: NetPlayer }[] = [];
    const myPlayer = GetMyPlayer();
    this.netPlayers.forEach((player) => {
      const distance = myPlayer.position.distanceTo(player.getPosition());
      list.push({ distance, player });
    });
    list.sort((a, b) => {
      return a.distance - b.distance;
    });

    list.forEach((item, index) => {
      item.player.setVisible(index < maxPlayers);
    });
  }

  clearAllPlayer() {
    this.netPlayers = [];
    NetPlayerListener.getInstance().notifyListener(NetPlayerEvent.NetPlayerChange, {});
  }
}
