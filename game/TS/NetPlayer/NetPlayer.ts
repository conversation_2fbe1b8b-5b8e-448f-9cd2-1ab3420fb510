import * as THREE from 'three';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '../Entity/Enum';
import { EntityTransform } from '../Entity/Components/EntityTransform';
import { EntityListenerKey } from '../Entity/Entity';

export class NetPlayer {
  private btcAddress: string;
  private entityId: number;

  constructor(btcAddress: string, entityId: number) {
    this.btcAddress = btcAddress;
    this.entityId = entityId;
  }

  getAddress() {
    return this.btcAddress;
  }

  getPosition() {
    const entity = EntityManager.getInstance().getEntity(this.entityId);
    const transform = entity?.getComponent<EntityTransform>(ComponentType.Transform);
    const pos = transform?.position || new THREE.Vector3(0, 0, 0);
    return pos;
  }

  setVisible(visible: boolean) {
    const entity = EntityManager.getInstance().getEntity(this.entityId);
    if (entity) {
      if (entity.visible !== visible) {
        entity.visible = visible;
        entity.emit(EntityListenerKey.VISIBLE_CHANGE);
      }
    }

    const entityList = EntityManager.getInstance().getEntityListByOwnerAddress(this.btcAddress)
    entityList.forEach((entity) => {

      if (entity.visible !== visible) {
        entity.visible = visible;
        entity.emit(EntityListenerKey.VISIBLE_CHANGE);
      }
    });
  }
}
