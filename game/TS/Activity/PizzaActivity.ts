import { PizzaPointConfig, PizzaPointData } from '@/game/Config/PizzaPointConfig';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { getFestivalInfo, registerFestival, reportFestival, rewardFestival } from '@/server';
import toast from 'react-hot-toast';
import { createParams, getLocalSession, rsaEncrypt } from '@/utils';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import { RewardType } from '@/constant/enum';

interface PizzaActivityData {
  festivalTag: string;
  signUpTime: number;
  startTime: number;
  endTime: number;
  pizzaData: {
    round: number;
    second: number;
    duration: number;
    pizzaDataList: PizzaPointData[];
    pickUpList: string[];
  }[];
}

interface IUpdateActivityDataProps {
  startTime: number;
  endTime: number;
  registrationTime: number;
  festivalTag: string;
  festivalType: string;
  festivalName: string;
}

class PizzaActivity {
  private activityData: PizzaActivityData = {
    festivalTag: 'test',
    signUpTime: -1,
    startTime: -1,
    endTime: -1,
    pizzaData: [],
  };

  private updateTimer: any;
  private reportTimer: any;
  private isTryReport = false;
  private isShowReward = false;
  private endTimer: any;

  setShowReward(
    showReward: (rankData: { rank: number; score: number; rewards: number; tick: string }) => void
  ) {
    this.showReward = showReward;
  }

  async refreshUpdateActivityData(test = false) {
    clearTimeout(this.endTimer);
    if (test) {
      this.reset();
      const now = Date.now();
      this.activityData.signUpTime = now;
      this.activityData.startTime = now + 5 * 1000;
      this.activityData.endTime = now + 20 * 1000;
      this.endTimer = setTimeout(() => {
        this.getActivityRank();
      }, this.activityData.endTime - now);
      return;
    }
    clearTimeout(this.updateTimer);
    try {
      const res = await getFestivalInfo();
      if (res.data.code === 1) {
        if (res.data.data) {
          const { startTime, endTime, registrationTime, festivalTag } = res.data.data;
          this.reset();
          this.activityData.festivalTag = festivalTag;
          this.activityData.signUpTime = registrationTime;
          this.activityData.startTime = startTime;
          this.activityData.endTime = endTime;

          const now = Date.now();
          if (this.activityData.endTime > now) {
            this.endTimer = setTimeout(() => {
              this.getActivityRank();
            }, this.activityData.endTime - now);
          }
        }
      } else {
        if (105 === res.data.code) {
          return;
        }
        toast.error(res.data.msg);
        this.updateTimer = setTimeout(() => {
          this.refreshUpdateActivityData();
        }, 3000);
      }
    } catch (error) {
      toast.error('Request failed, Please check your network and try again.');
      this.updateTimer = setTimeout(() => {
        this.refreshUpdateActivityData();
      }, 3000);
    }
  }

  async socketUpdateActivityData(props: IUpdateActivityDataProps, test = false) {
    clearTimeout(this.endTimer);
    if (test) {
      this.reset();
      const now = Date.now();
      this.activityData.signUpTime = now;
      this.activityData.startTime = now + 20 * 1000;
      this.activityData.endTime = now + 60 * 1000;
      this.endTimer = setTimeout(() => {
        this.getActivityRank();
      }, this.activityData.endTime - now);
      return;
    }
    clearTimeout(this.updateTimer);
    try {
      // const res = await getFestivalInfo();
      // if (res.data.code === 1) {
      //   if (res.data.data) {

      //   }
      // }

      const { startTime, endTime, registrationTime, festivalTag } = props;
      this.reset();
      this.activityData.festivalTag = festivalTag;
      this.activityData.signUpTime = registrationTime;
      this.activityData.startTime = startTime;
      this.activityData.endTime = endTime;

      const now = Date.now();
      if (this.activityData.endTime > now) {
        this.endTimer = setTimeout(() => {
          this.getActivityRank();
        }, this.activityData.endTime - now);
      }
    } catch (error) {
      toast.error('Request failed, Please check your network and try again.');
    }
  }

  getActivityData() {
    return this.activityData;
  }

  showAllPoint() {
    const now = Date.now();
    this.activityData = {
      festivalTag: 'test',
      signUpTime: now - 10 * 1000,
      startTime: now + 1000,
      endTime: now + 70 * 1000,
      pizzaData: [
        {
          round: 1,
          second: 0,
          duration: 70,
          pizzaDataList: PizzaPointConfig.getInstance().getAllData(),
          pickUpList: [],
        },
      ],
    };
  }

  getCurrentRound() {
    const now = Date.now();
    const startRunningTime = now - this.activityData.startTime;
    if (startRunningTime) {
      for (let i = 0; i < this.activityData.pizzaData.length; i++) {
        const round = this.activityData.pizzaData.length - i - 1;
        const data = this.activityData.pizzaData[round];
        if (data && data.second * 1000 < startRunningTime) {
          return round;
        }
      }
    }
    return -1;
  }

  async joinActivity(test = false) {
    const myPlayer = GetMyPlayer();
    if (test) {
      PizzaPointConfig.getInstance().setCurTick(RewardType.wangcai);
      this.activityData.pizzaData.push({
        round: 1,
        second: 0,
        duration: 5,
        pizzaDataList: PizzaPointConfig.getInstance().randomIds(),
        pickUpList: [],
      });

      this.activityData.pizzaData.push({
        round: 2,
        second: 8,
        duration: 5,
        pizzaDataList: PizzaPointConfig.getInstance().randomIds(),
        pickUpList: [],
      });
      myPlayer.startPizza(RewardType.wangcai);
      return;
    }
    try {
      const btcAddress = myPlayer.btcAddress;
      const params = createParams(btcAddress, '/festival/register');
      const encrypted = rsaEncrypt(params);
      const headers = {
        sw: encrypted,
        address: btcAddress,
        session: getLocalSession(btcAddress).sessionId,
      };
      const res = await registerFestival(params, headers);
      const { code, msg, data } = res.data;
      if (code === 1) {
        const { startTime, endTime, festivalTag, pizzas, tick } = data;
        this.activityData.startTime = startTime;
        this.activityData.endTime = endTime;
        this.activityData.festivalTag = festivalTag;
        this.activityData.pizzaData = [];
        const list = pizzas as {
          round: number;
          second: number;
          duration: number;
          positionTags: string[];
        }[];
        PizzaPointConfig.getInstance().setCurTick(tick);

        list.forEach((item) => {
          this.preload();
          const { round, second, duration, positionTags } = item;
          const pizzaList: PizzaPointData[] = [];
          positionTags.forEach((tag) => {
            PizzaPointConfig.getInstance().getData(Number(tag), (data) => {
              if (data) {
                pizzaList.push(data);
              }
            });
          });
          this.activityData.pizzaData.push({
            round: round,
            second: second,
            duration: duration,
            pizzaDataList: pizzaList,
            pickUpList: [],
          });
        });
        myPlayer.startPizza(tick);
        this.isTryReport = false;
      } else {
        toast.error('Join activity failed,' + msg);
      }
    } catch (e) {
      toast.error('Network error: Join activity failed');
    }
  }

  async pickUpPizza(pointId: number) {
    const round = this.getCurrentRound();
    const roundData = this.activityData.pizzaData[round];
    if (roundData) {
      if (roundData.pickUpList.includes(String(pointId))) {
        return;
      }
      roundData.pickUpList.push(String(pointId));
      const myPlayer = GetMyPlayer();
      myPlayer.addPizza();
    }
  }

  getActivityRank() {
    if (this.activityData.pizzaData.length === 0) {
      //未报名
      return;
    }
    if (this.isShowReward) {
      return;
    }
    this.isShowReward = true;
    if (this.activityData.festivalTag === 'test') {
      this.showReward({
        rank: 1,
        score: 100,
        rewards: 20,
        tick: 'wangcai',
      });
      const myPlayer = GetMyPlayer();
      myPlayer.removePizza();
      return;
    }

    const httpRank = async () => {
      clearTimeout(this.updateTimer);
      try {
        const res = await rewardFestival(this.activityData.festivalTag);
        const { code, msg, data } = res.data;
        if (code === 1) {
          this.showReward(data);
          const myPlayer = GetMyPlayer();
          myPlayer.removePizza();
          // this.updateActivityData().then();

          clearTimeout(this.endTimer);
        } else {
          if (code !== 105) {
            this.updateTimer = setTimeout(() => {
              httpRank();
            }, 3000);
          }
          toast.error(msg);
        }
      } catch (e) {
        this.updateTimer = setTimeout(() => {
          httpRank();
        }, 3000);
        toast.error('Request failed, Please check your network and try again.');
      }
    };
    httpRank().then();
  }

  tryReport() {
    if (this.activityData.pizzaData.length === 0) {
      //未报名
      return;
    }
    if (this.isTryReport) {
      return;
    }
    this.isTryReport = true;
    this.reportPizza().then(() => undefined);
  }

  async reportPizza() {
    const now = Date.now();
    if (this.activityData.festivalTag === 'test') {
      return;
    }
    if (now > this.activityData.endTime) {
      toast.error('Activity is over');
      return;
    }
    const tagList: { round: number; positionTagList: string[] }[] = [];
    let pizzaCount = 0;
    this.activityData.pizzaData.forEach((item) => {
      tagList.push({
        round: item.round,
        positionTagList: item.pickUpList,
      });
      pizzaCount += item.pickUpList.length;
    });
    if (pizzaCount < 10) {
      return;
    }
    // const params = {
    //   festivalTag: this.activityData.festivalTag,
    //   tagList
    // }

    clearTimeout(this.reportTimer);
    try {
      const myPlayer = GetMyPlayer();
      const btcAddress = myPlayer.btcAddress;
      const params = createParams(btcAddress, '/festival/report');
      const encrypted = rsaEncrypt(params);
      const newParams = {
        festivalTag: this.activityData.festivalTag,
        tagList,
      };
      const headers = {
        sw: encrypted,
        address: btcAddress,
        session: getLocalSession(btcAddress).sessionId,
      };
      const res = await reportFestival(newParams, headers);
      const { code, msg } = res.data;
      if (code === 1) {
        toast.success('Report success');
      } else {
        toast.error('Report failed,' + msg);
        if (code !== 1805 && code !== 105) {
          this.reportTimer = setTimeout(() => {
            this.reportPizza();
          }, 3000);
        }
      }
    } catch (e) {
      toast.error('Network error: Report failed');
      this.reportTimer = setTimeout(() => {
        this.reportPizza();
      }, 3000);
    }
  }

  private showReward: (rankData: {
    rank: number;
    score: number;
    rewards: number;
    tick: string;
  }) => void = () => undefined;

  private preload() {
    // LoaderUtil.loadGlb('./assets/Pet/NPC_pizza.glb', () => undefined);
    LoaderUtil.loadGlb(PizzaPointConfig.getInstance().getPizzaBoxUrl(), () => undefined);
  }

  private reset() {
    this.activityData = {
      festivalTag: 'test',
      signUpTime: -1,
      startTime: -1,
      endTime: -1,
      pizzaData: [],
    };
    this.isTryReport = false;
    this.isShowReward = false;
    const myPlayer = GetMyPlayer();
    myPlayer.removePizza();
  }

  testShowReward() {
    this.showReward({
      rank: 1,
      score: 100,
      rewards: 20,
      tick: 'wangcai',
    });
  }
}

let pizzaActivity: PizzaActivity | null = null;

export function getPizzaActivity() {
  if (pizzaActivity == null) {
    pizzaActivity = new PizzaActivity();
  }
  return pizzaActivity;
}
