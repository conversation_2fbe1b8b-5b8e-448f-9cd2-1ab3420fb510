import { RedPacketEvent } from '@/game/TS/RedPacket/RedPacketEvent';

export class RedPacketListener {
  private static instance: RedPacketListener;
  private listenerMap: Map<RedPacketEvent, ((data: any) => void)[]> = new Map();

  private constructor() {}

  static getInstance() {
    if (!RedPacketListener.instance) {
      RedPacketListener.instance = new RedPacketListener();
    }
    return RedPacketListener.instance;
  }

  addListener(event: RedPacketEvent, listener: (data: any) => void) {
    if (!this.listenerMap.has(event)) {
      this.listenerMap.set(event, []);
    }
    this.listenerMap.get(event)?.push(listener);
  }

  removeListener(event: RedPacketEvent, listener: (data: any) => void) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  notifyListener(event: RedPacketEvent, data: any) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      listeners.forEach((listener) => {
        listener(data);
      });
    }
  }
}
