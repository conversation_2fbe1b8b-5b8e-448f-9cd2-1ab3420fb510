import { RedPacketListener } from '@/game/TS/RedPacket/RedPacketListener';
import { RedPacketEvent } from '@/game/TS/RedPacket/RedPacketEvent';
import { game } from '@/game/Proto/generated/game_messages';

export class RedPacketManager {
  private static instance: RedPacketManager;
  private pointMap: Map<number, game.IRedPacketPoint> = new Map();

  private constructor() {}

  static getInstance() {
    if (!RedPacketManager.instance) {
      RedPacketManager.instance = new RedPacketManager();
    }
    return RedPacketManager.instance;
  }

  getRedPacket(configId: number) {
    return this.pointMap.get(configId);
  }

  receiveRedPacket(list: game.IRedPacketPoint[]) {
    list.forEach((item) => {
      if (item.configId) {
        this.pointMap.set(item.configId, item);
        RedPacketListener.getInstance().notifyListener(RedPacketEvent.redPacketUpdate, item);
      }
    });
  }
}
