import { ChatData } from '@/game/TS/Chat/ChatData';
import { ChatTabType } from '@/game/TS/Chat/ChatType';
import { ChatDataList } from '@/game/TS/Chat/ChatDataList';
import { useNetWork } from '@/game/TS/useNetWork';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { ChatListener } from '@/game/TS/Chat/ChatListener';
import { ChatEvent } from '@/game/TS/Chat/ChatEvent';
import { game } from '@/game/Proto/generated/game_messages';
import { GM_BTC_ADDRESS } from '@/hooks/usePlayerColors';
import { IS_EXP_SERVER } from '@/constant';
import { getUTC8MidnightTimestamp } from '@/utils/dayjsHelper';

export class ChatManager {
  private static instance: ChatManager;
  private dataListMap: Map<ChatTabType, ChatDataList> = new Map();
  private intervalId: any;
  private isRunning = false;

  static getInstance() {
    if (!ChatManager.instance) {
      ChatManager.instance = new ChatManager();
    }
    return ChatManager.instance;
  }

  clientStart() {
    this.intervalId = setInterval(() => {
      this.onTick();
    }, 1000);

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { watchRoomStatus, sendChatMsg } = useNetWork();
    watchRoomStatus((data) => {
      if (data.isEnterRoom) {
        sendChatMsg(ChatTabType.Room, game.C2SPacketType.C2S_CHAT_ENTER, {
          toJSON: () => ({}),
        } as any);
      } else {
        this.outChatType(ChatTabType.Room);
      }
    });
  }

  clientStop() {
    clearInterval(this.intervalId);
  }

  getChatTypes() {
    const list = Array.from(this.dataListMap.keys());
    list.sort((a, b) => a - b);
    return list;
  }

  getChatList(tabType: ChatTabType) {
    return this.dataListMap.get(tabType);
  }

  addChatMessage(tabType: ChatTabType, data: game.IChatMessage) {
    console.log('addChatMessage', tabType, data.content, new Date(Number(data.timestamp)).toUTCString());
    const chatData = new ChatData();
    chatData.decode(data);
    const dataList = this.dataListMap.get(tabType);
    if (dataList) {
      dataList.addChatMessage(chatData);
    }
    ChatListener.getInstance().notifyListener(ChatEvent.ReceiveChat, tabType);
  }

  enterChatType(tabType: ChatTabType) {
    if (this.dataListMap.has(tabType)) {
      return;
    }
    this.dataListMap.set(tabType, new ChatDataList(tabType));
    ChatListener.getInstance().notifyListener(ChatEvent.ChatTypeChange, this.getChatTypes());
    ChatListener.getInstance().notifyListener(ChatEvent.ReceiveChat, tabType);
  }

  outChatType(tabType: ChatTabType) {
    this.dataListMap.delete(tabType);
    ChatListener.getInstance().notifyListener(ChatEvent.ChatTypeChange, this.getChatTypes());
  }

  sendChatMessage(playerId: string, tabType: ChatTabType, content: string, replyTo: string) {
    const chatData = new ChatData();
    chatData.playerId = playerId;
    chatData.content = content;
    chatData.replyTo = replyTo;
    const data = chatData.encode();
    this.addChatMessage(tabType, data);
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { sendChatMsg } = useNetWork();
    sendChatMsg(tabType, game.C2SPacketType.C2S_CHAT_MESSAGE, game.ChatMessage.create(data));
    if (tabType === ChatTabType.Room) {
      const myPlayer = GetMyPlayer();
      myPlayer.callAppApi(AppGameApiKey.sendChat, content);
    }
  }

  deleteChatMessage(tabType: ChatTabType, uuid: string) {
    const dataList = this.dataListMap.get(tabType);
    if (dataList) {
      dataList.deleteChatMessage(uuid);
      ChatListener.getInstance().notifyListener(ChatEvent.ReceiveChat, tabType);
    }
  }

  sendChatMessageDelete(tabType: ChatTabType, uuid: string) {
    const myPlayer = GetMyPlayer();
    if (myPlayer.btcAddress === GM_BTC_ADDRESS) {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const { sendChatMsgDelete } = useNetWork();
      sendChatMsgDelete(
        tabType,
        game.C2SPacketType.C2S_CHAT_MESSAGE_DELETE,
        game.ChatMessageDelete.create({
          uuid,
        })
      );
    }
  }

  onTick() {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { isConnected, sendChatMsg } = useNetWork();
    if (isConnected()) {
      if (!this.isRunning) {
        this.isRunning = true;
        sendChatMsg(ChatTabType.SatWorld, game.C2SPacketType.C2S_CHAT_ENTER, {
          toJSON: () => ({}),
        } as never);

        if (!IS_EXP_SERVER) {
          sendChatMsg(ChatTabType.Fractal, game.C2SPacketType.C2S_CHAT_ENTER, {
            toJSON: () => ({}),
          } as never);
          sendChatMsg(ChatTabType.WangCai, game.C2SPacketType.C2S_CHAT_ENTER, {
            toJSON: () => ({}),
          } as never);
        }
      }
    } else {
      if (this.isRunning) {
        this.isRunning = false;
        this.dataListMap.clear();
        ChatListener.getInstance().notifyListener(ChatEvent.ChatTypeChange, this.getChatTypes());
      }
    }
  }
}
