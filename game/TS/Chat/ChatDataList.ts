import { ChatTabType } from '@/game/TS/Chat/ChatType';
import { ChatData } from '@/game/TS/Chat/ChatData';
import { ChatListener } from '@/game/TS/Chat/ChatListener';
import { ChatEvent } from '@/game/TS/Chat/ChatEvent';

export class ChatDataList {
  tabType: ChatTabType;
  private chatDataMap: Map<string, ChatData> = new Map();
  private newMessageCount = 0;
  private lookingIndex = 0;
  private lastTimeChatData: ChatData | null = null;

  constructor(type: ChatTabType) {
    this.tabType = type;
  }

  private checkTimeCheck(chatTimestamp: number) {
    const chatData = new ChatData();
    chatData.timestamp = chatTimestamp;
    //显示 时分
    chatData.content = '**' + new Date(chatTimestamp).toLocaleTimeString() + '**';
    chatData.isTime = true;
    if (!this.lastTimeChatData) {
      this.lastTimeChatData = chatData;
      this.chatDataMap.set(chatData.uuid, chatData);
      return;
    }
    if (chatTimestamp - this.lastTimeChatData.timestamp > 30 * 60 * 1000) {
      this.lastTimeChatData = chatData;
      this.chatDataMap.set(chatData.uuid, chatData);
    }
  }

  addChatMessage(chatData: ChatData) {
    this.checkTimeCheck(chatData.timestamp);
    this.chatDataMap.set(chatData.uuid, chatData);
    if (chatData.timestamp > Date.now() - 10 * 1000) {
      this.newMessageCount++;
    }
    ChatListener.getInstance().notifyListener(ChatEvent.NewMessageChange, this.tabType);
  }

  deleteChatMessage(uuid: string) {
    this.chatDataMap.delete(uuid);
  }

  findChatData(uuid: string) {
    return this.chatDataMap.get(uuid);
  }

  getNewMessageCount() {
    return this.newMessageCount;
  }

  clearNewMessageCount() {
    this.newMessageCount = 0;
    ChatListener.getInstance().notifyListener(ChatEvent.NewMessageChange, this.tabType);
  }

  getChatDataList() {
    // [...map.values()]（最快，引擎优化最好）
    // for...of 循环（稍慢，但可控）
    // Array.from(map.values())（最慢，但可读性好）
    return [...this.chatDataMap.values()];
  }
}
