import AvatarData from '../../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import { io, Socket } from 'socket.io-client';
import { AppGame<PERSON><PERSON>Key, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import toast from 'react-hot-toast';
import { ChatManager } from '@/game/TS/Chat/ChatManager';
import { RedPacketManager } from '@/game/TS/RedPacket/RedPacketManager';
import { game } from '@/game/Proto/generated/game_messages';
import ProtobufHandler from '@/game/Proto/protobuf-handler';
import {
  encodePlayerAction,
  encodePlayerChat,
  encodePlayerCommon,
  encodePlayerLogic,
  encodePlayerRequest,
} from '@/game/Proto/client-protobuf-encoder';
import { TreeConfig } from '@/game/Config/TreeConfig';
import { StoneConfig } from '@/game/Config/StoneConfig';
import { Events } from '@/utils/clientEvents';
import {
  setEasterEggReward,
  setRandomEventResult,
  setRockLeftTime,
  setTreeLeftTime,
  setWhackAMoleEasterEgg,
} from '@/store/app';
import {
  W2C_EasterEggMetaData,
  W2C_PacketTypes,
  W2C_RedPacketReward,
  W2C_UpdateMetaData,
} from '@/game/TS/W2CPacket';
import { IAvatarMetadata } from '@/AvatarOrdinalsBrowser/constant/type';
import { ItemDropConfig } from '@/game/Config/ItemDropConfig';
import { getFinallyKey, preCalculateMD5 } from '@/utils/socket';
import { NetPlayerManager } from '@/game/TS/NetPlayer/NetPlayerManager';
import { decodePlayerCommon } from '@/game/Proto/client-protobuf-handler';
import { IAppState } from '@/constant/type';
import { EasterEggType } from '@/constant/enum';
import { decodeBase64ToJSON, decodeBase64ToString } from '@/utils/base64';
import { getPizzaActivity } from '@/game/TS/Activity/PizzaActivity';
import { IEasterEggRewardOpenConfig } from '@/components/EasterEggReward';
import { playerEnergyZustandStore } from '@/contexts/playerEnergyContext/store';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { setMaterialList } from '@/store/game';

// 基础Socket事件
export enum SocketEvents {
  // 连接事件
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  PLAYER_LOGIN = '1',
  // 房间事件
  JOIN_ROOM = '2',
  LEAVE_ROOM = '3',
  PLAYER_READY = '4',
  PLAYER_ACTION = '5',
  PLAYER_LOGIC = '6',
  PLAYER_REQUEST = '7',
  PLAYER_CHAT = '8',
  PLAYER_HEARTBEAT = '9',
  PLAYER_NOTICE = '10',
  WEB_NOTICE = '11',
  PLAYER_KICK = '12',
  MATCH_FOUND = '13',

  GET_ROOM_LIST = '14',

  ERROR = '999',
}

interface GameNodeInfo {
  id: string;
  mode: number;
  modeIndex: number;
  playerCount: number;
  maxPlayerCount: number;
}

let _mapId = 0;
let _mapIndex = 0;
let disconnectTimer: any;

let _socket: Socket | null = null;
let isEnterRoom = false;
let connectTime = 0;
let lastPing = 0;
const changeRoomCall: ((status: {
  isEnterRoom: boolean;
  mapId: number;
  mapIndex: number;
}) => void)[] = [];

function isConnected() {
  return connectTime > 0;
}

function watchRoomStatus(
  callback: (status: { isEnterRoom: boolean; mapId: number; mapIndex: number }) => void
) {
  changeRoomCall.push(callback);
}

function getRoomStatus() {
  return {
    isEnterRoom: isEnterRoom,
  };
}

//心跳包
function startHeartbeat() {
  setInterval(async () => {
    if (connectTime > 0 && _socket) {
      try {
        const sendTime = Date.now();
        const response = await _socket
          .timeout(5000)
          .emitWithAck(
            getFinallyKey(SocketEvents.PLAYER_HEARTBEAT),
            encodePlayerCommon([String(lastPing)])
          );
        const serverTime = response as number;
        if (serverTime) {
          const respTime = Date.now();
          lastPing = respTime - sendTime;
        }
      } catch (err) {
        console.error('ping error', err);
      }
    }
  }, 5000);
}

function connect(socket_url: string) {
  // console.log('Connecting to server...', host, port);
  if (_socket) {
    // console.log('Already connected to the server');
    return;
  }
  const myPlayer = GetMyPlayer();
  const _btcAddress = myPlayer.btcAddress || '';
  const _sessionId = myPlayer.sessionId || '';
  if (_btcAddress.length === 0) {
    return;
  }
  // 使用传入的host和port参数
  const socket = io(socket_url, {
    withCredentials: false,
    transports: ['websocket'],
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 30,
    reconnectionDelay: 1000,
    // 确保二进制数据正确传输
    upgrade: false, // 禁用传输升级
    forceNew: true, // 避免连接共享
    rememberUpgrade: false,
  });
  _socket = socket;

  // 连接事件处理
  socket.on(SocketEvents.CONNECT, async () => {
    // console.log('Connected to the server');
    clearTimeout(disconnectTimer);
    connectTime = 0;
    GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, false);
    const binaryData: Buffer = await socket
      .timeout(60 * 1000)
      .emitWithAck(SocketEvents.PLAYER_LOGIN, encodePlayerCommon([_btcAddress, _sessionId]));
    const [_times] = decodePlayerCommon(binaryData);
    const times = Number(_times);
    if (times > 0) {
      preCalculateMD5(_btcAddress, times);
      listenSocketMessage(socket);
      socket.emit(getFinallyKey(SocketEvents.PLAYER_READY), encodePlayerCommon([]));
      connectTime = Date.now();
      GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, true);
    }
  });

  socket.on(SocketEvents.DISCONNECT, () => {
    // console.log('Disconnected from gateway server');
    connectTime = 0;
    GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, false);
    isEnterRoom = false;
    changeRoomCall.forEach((callback) => {
      callback({ isEnterRoom: false, mapId: 0, mapIndex: 0 });
    });
    disconnectTimer = setTimeout(() => {
      //由于推出钱包时，会触发断开连接，但是myPlayer.btcAddress会延迟改变
      if (myPlayer.btcAddress.length > 0) {
        toast.error("Oops! You've been disconnected. Reconnecting…");
      }
    }, 5000);
    NetPlayerManager.getInstance().clearAllPlayer();
  });
}

function listenSocketMessage(socket: Socket) {
  const myPlayer = GetMyPlayer();
  socket.removeAllListeners(getFinallyKey(SocketEvents.PLAYER_KICK));
  socket.on(getFinallyKey(SocketEvents.PLAYER_KICK), () => {
    myPlayer.callAppApi(AppGameApiKey.disconnectWallet);
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.WEB_NOTICE));
  socket.on(getFinallyKey(SocketEvents.WEB_NOTICE), (data) => {
    webNotice(data);
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.PLAYER_NOTICE));
  socket.on(
    getFinallyKey(SocketEvents.PLAYER_NOTICE),
    (binaryData: ArrayBuffer | Uint8Array | Buffer) => {
      const { pid, data } = ProtobufHandler.getInstance().decodeMessage(binaryData);
      switch (pid) {
        case game.S2CPacketType.S2C_PLAYER_ENTER:
          const enterData = data as game.PlayerEnter;
          // 有新玩家连接时的处理

          if (!NetPlayerManager.getInstance().findOtherPlayer(enterData.btcAddress)) {
            NetPlayerManager.getInstance().addOtherPlayer(enterData.btcAddress, enterData.entityId);
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_LEAVE:
          const leaveData = data as game.PlayerLeave;
          const leavePlayer = NetPlayerManager.getInstance().findOtherPlayer(leaveData.btcAddress);
          if (leavePlayer) {
            NetPlayerManager.getInstance().deleteOtherPlayer(leaveData.btcAddress);
          }
          break;
        case game.S2CPacketType.S2C_CHAT_ENTER:
          ChatManager.getInstance().enterChatType(data.chatId);
          break;
        case game.S2CPacketType.S2C_CHAT_MESSAGE:
          const chatData = data as game.ChatMessage;
          ChatManager.getInstance().addChatMessage(chatData.tabType, chatData);
          break;
        case game.S2CPacketType.S2C_CHAT_MESSAGE_DELETE:
          const chatDeleteData = data as game.ChatMessageDelete;
          ChatManager.getInstance().deleteChatMessage(chatDeleteData.tabType, chatDeleteData.uuid);
          break;
        case game.S2CPacketType.S2C_CHAT_LEAVE:
          ChatManager.getInstance().outChatType(data.chatId);
          break;
        case game.S2CPacketType.S2C_RED_PACKET_UPDATE:
          const redPacketUpdate = data as game.RedPacketUpdate;
          const { pointList } = redPacketUpdate;
          if (pointList && pointList.length > 0) {
            RedPacketManager.getInstance().receiveRedPacket(pointList);
          }
          break;
        case game.S2CPacketType.S2C_RED_PACKET_CACHE:
          const redPacketCache = data as game.RedPacketCache;
          const { pickedList } = redPacketCache;
          myPlayer.pickedList = pickedList || [];
          break;
        case game.S2CPacketType.S2C_UPDATE_TREE:
          const updateTreeList = data as game.UpdateTreeList;
          const { treeDataList } = updateTreeList;

          TreeConfig.getInstance().updateTreeList(treeDataList);
          break;
        case game.S2CPacketType.S2C_UPDATE_TREE_REFRESH:
          const updateTreeRefresh = data as game.UpdateTreeRefresh;
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(setTreeLeftTime(updateTreeRefresh.leftTime || undefined));
          });
          break;
        case game.S2CPacketType.S2C_UPDATE_ROCK:
          const updateRockList = data as game.UpdateRockList;
          const { rockDataList } = updateRockList;
          StoneConfig.getInstance().updateRockList(rockDataList);
          break;
        case game.S2CPacketType.S2C_UPDATE_ROCK_REFRESH:
          const updateRockRefresh = data as game.UpdateRockRefresh;
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(setRockLeftTime(updateRockRefresh.leftTime || undefined));
          });
          break;
        case game.S2CPacketType.S2C_UPDATE_ITEM:
          const updateItem = data as game.UpdateItem;
          myPlayer.callAppApi(
            AppGameApiKey.updateItemDurability,
            updateItem.itemId,
            updateItem.durability
          );
          break;
        case game.S2CPacketType.S2C_UPDATE_PICK_UP_POINT:
          const updatePickUpPoint = data as game.UpdatePickUpPoint;
          ItemDropConfig.getInstance().updateData([
            {
              dropItemTag: 0,
              isPickedUp: updatePickUpPoint.isPickedUp,
              tag: Number(updatePickUpPoint.tag),
              quantity: 1,
              coolDown: updatePickUpPoint.coolDown as number,
            },
          ]);
          break;
        case game.S2CPacketType.S2C_REWARD_MATERIAL:
          const rewardMaterial = data as game.UpdateMaterial;
          const { materialTag } = rewardMaterial;
          if (materialTag.startsWith('101')) {
            Events.emitItemCollected('/image/t2-1.png', 1);
          } else if (materialTag.startsWith('102')) {
            Events.emitItemCollected('/image/t2-2.png', 1);
          } else if (materialTag.startsWith('103')) {
            Events.emitItemCollected('/image/t2-3.png', 1);
          }
          break;
        case game.S2CPacketType.S2C_PET_GET_MATERIAL:
          const petRewardMaterial = data as game.UpdateMaterialViaPet;
          const { materialTag: _materialTag } = petRewardMaterial;
          if (_materialTag.startsWith('101')) {
            Events.emitItemCollected('/image/t2-1.png', 1);
          } else if (_materialTag.startsWith('102')) {
            Events.emitItemCollected('/image/t2-2.png', 1);
          } else if (_materialTag.startsWith('103')) {
            Events.emitItemCollected('/image/t2-3.png', 1);
          }
          myPlayer.callAppApi(AppGameApiKey.petUpdateMaterial, petRewardMaterial);
          break;
        case game.S2CPacketType.S2C_REWARD_RANDOM_EVENT:
          const rewardRandomEvent = data as game.UpdateRandomEvent;
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(
              setRandomEventResult({
                quantity: Number(rewardRandomEvent.quantity) || 0,
                tag: rewardRandomEvent.tag,
                eventType: rewardRandomEvent.eventType,
              })
            );
          });
          break;
        case game.S2CPacketType.S2C_NOTICE_NEW_DAY:
          myPlayer.callAppApi(AppGameApiKey.nextDay);
          break;
        case game.S2CPacketType.S2C_ENTITY_ENTER:
          const entityEnterData = data as game.EntityEnter;
          EntityManager.getInstance().serverEntityEnter(entityEnterData);
          break;
        case game.S2CPacketType.S2C_ENTITY_LEAVE:
          const entityLeaveData = data as game.EntityLeave;
          EntityManager.getInstance().serverEntityLeave(entityLeaveData);
          break;
        case game.S2CPacketType.S2C_COMPONENT_ACTION:
          const entityComponentActionData = data as game.EntityComponentAction;
          EntityManager.getInstance().serverComponentAction(entityComponentActionData);
          break;
        case game.S2CPacketType.S2C_PET_UPDATE_CUT_TREE:
          const { id: _id, ...petData1 } = data as game.UpdatePetActionData;
          myPlayer.callAppApi(AppGameApiKey.petCutTree, { ...petData1, _id: _id });
          break;
        case game.S2CPacketType.S2C_PET_UPDATE_MINING_ROCK:
          const { id: __id, ...petData2 } = data as game.UpdatePetActionData;
          myPlayer.callAppApi(AppGameApiKey.petMining, { ...petData2, _id: __id });
          break;
        case game.S2CPacketType.S2C_PET_UPDATE_FISHING_SUCCESS:
          const { id: ___id, ...petData3 } = data as game.UpdatePetActionData;
          myPlayer.callAppApi(AppGameApiKey.petFish, { ...petData3, _id: ___id });
          break;
      }
    }
  );

  socket.removeAllListeners(getFinallyKey(SocketEvents.MATCH_FOUND));
  socket.on(getFinallyKey(SocketEvents.MATCH_FOUND), (data) => {
    try {
      const { mode, modeIndex } = data;
      _mapId = mode.split('_')[1] as unknown as number;
      _mapIndex = (modeIndex as number) || 0;
      NetPlayerManager.getInstance().clearAllPlayer();
      socket.emit(
        getFinallyKey(SocketEvents.PLAYER_ACTION),
        encodePlayerAction(
          game.C2SPacketType.C2S_PLAYER_ENTER,
          game.CommonMessage.create({
            messageList: [String(myPlayer.clientEntityIndex)],
          })
        )
      );
      isEnterRoom = true;
      changeRoomCall.forEach((callback) => {
        callback({ isEnterRoom: true, mapId: _mapId, mapIndex: _mapIndex });
      });
    } catch (e) {
      toast.error('enter room error');
      leaveRoom();
    }
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.LEAVE_ROOM));
  socket.on(getFinallyKey(SocketEvents.LEAVE_ROOM), () => {
    try {
      leaveRoom();
    } catch (e) {}
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.ERROR));
  socket.on(getFinallyKey(SocketEvents.ERROR), (message) => {
    toast.error(message);
  });
}

function webNotice(notice: any) {
  const { pid, data } = notice;
  const myPlayer = GetMyPlayer();
  switch (pid) {
    case W2C_PacketTypes.RedPacketReward:
      const { tick, amount } = data as W2C_RedPacketReward;
      myPlayer.callAppApi(AppGameApiKey.showRewards, tick, amount);
      break;
    case W2C_PacketTypes.UpdateMetaData:
      const metaData = data as W2C_UpdateMetaData;
      myPlayer.callAppApi(AppGameApiKey.setMetaData, metaData as IAvatarMetadata);
      break;
    case W2C_PacketTypes.TRIGGER_EASTER_EGG_MEG:
      const originData = data as W2C_EasterEggMetaData;
      const { easterEggType, rule } = originData;

      if (easterEggType === EasterEggType.ORDER_TREE) {
        const easterRule = decodeBase64ToString(rule) as any;

        if (easterRule && Date.now() < data.endTime) {
          TreeConfig.getInstance().updateOrderTreeData(
            {
              rule: easterRule,
            } as any,
            false
          );
          myPlayer.callAppApi(AppGameApiKey.activityRule, 4);
          myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, data.endTime, 1);
        }

        break;
      }

      if (easterEggType === EasterEggType.WHACK_A_MOLE) {
        const base64EasterEgg = decodeBase64ToJSON(rule) as IAppState['whackAMoleEasterEgg'];
        myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
          dispatch(setWhackAMoleEasterEgg(base64EasterEgg));
        });
        myPlayer.callAppApi(AppGameApiKey.activityRule, 3);

        break;
      }

      break;

    case W2C_PacketTypes.EASTER_EGG_COMPLETE_MSG:
      const { rewardQuantity, rewardTag, rewardType, easterEggType: easterType } = data;
      if (easterType === EasterEggType.WHACK_A_MOLE) {
        myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
          dispatch(
            setEasterEggReward({
              quantity: rewardQuantity,
              tag: rewardTag,
              eventType: rewardType,
            })
          );
        });
      } else {
        myPlayer.callAppApi(AppGameApiKey.showEasterEggRewards, {
          modalStyleType: 'axe',
          quantity: rewardQuantity,
          tag: rewardTag,
          eventType: rewardType,
          subTitle: 'Chop Master',
        } as IEasterEggRewardOpenConfig);

        TreeConfig.getInstance().clearEasterEggTree();
        myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, 0, 2);
      }
      break;

    case W2C_PacketTypes.TREE_UPDATE_BASIC_INFO_MSG:
      const easterRule = decodeBase64ToString(data.rule);
      if (easterRule && Date.now() < data.endTime) {
        TreeConfig.getInstance().updateOrderTreeData(
          {
            rule: easterRule,
          } as any,
          true
        );

        myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, data.endTime, 1);
      }
      break;

    case W2C_PacketTypes.EASTER_EGG_FAILED_MSG:
      if (data.easterEggType === EasterEggType.ORDER_TREE) {
        myPlayer.callAppApi(AppGameApiKey.showEasterEggFailed, data.rewardTag);
        TreeConfig.getInstance().clearEasterEggTree();
        myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, 0, 3);
      }
      break;

    case W2C_PacketTypes.PIZZA_RUSH_EASTER_EGG:
      const pizzaActivity = getPizzaActivity();
      pizzaActivity.socketUpdateActivityData(data);

      // 只有触发人才打开弹窗
      if (myPlayer.btcAddress === data.address) {
        myPlayer.callAppApi(AppGameApiKey.activityRule, 2);
      }

      break;

    case W2C_PacketTypes.PLAYER_BASIC_INFO_ENERGY:
      playerEnergyZustandStore.getState().updatePlayerInfo(data);

      break;
    case W2C_PacketTypes.PETSHED_NOTICE:
      // TODO: 根据petTag 播放对应类型的宠物动画
      // {
      //   currentDurability: 0;
      //   petTag: 'cat_4';
      //   positionTag: '11';
      //  slotRecordStatus: 'generated'
      //   userItemTag: '404';
      // }
      const { slotRecordStatus, ...resData } = data;
      myPlayer.callAppApi(AppGameApiKey.updatePetShedInfo, {
        ...resData,
        status: slotRecordStatus,
      });
      break;
  }
}

function disConnect() {
  if (_socket && _socket.active) {
    _socket.close();
  }
  isEnterRoom = false;
  changeRoomCall.forEach((callback) => {
    callback({ isEnterRoom: false, mapId: 0, mapIndex: 0 });
  });
  connectTime = 0;
  GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, false);
  _socket = null;
  NetPlayerManager.getInstance().clearAllPlayer();
}

function sendMapId(mapIdKey: string) {
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PLAYER_MAP_UPDATE,
        game.CommonMessage.create({
          messageList: [mapIdKey],
        }).toJSON()
      )
    );
  }
}

function sendMapPosition(positionList: string[]) {
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PLAYER_POSITION_UPDATE,
        game.CommonMessage.create({
          messageList: positionList,
        }).toJSON()
      )
    );
  }
}

function sendChatMsg(chatId: number, pid: game.C2SPacketType, data: game.ChatMessage) {
  const socket = _socket;
  if (socket && socket.active) {
    const binaryData = encodePlayerChat(chatId, pid, data.toJSON());
    socket.emit(getFinallyKey(SocketEvents.PLAYER_CHAT), binaryData);
  }
}

function sendChatMsgDelete(chatId: number, pid: game.C2SPacketType, data: game.ChatMessageDelete) {
  const socket = _socket;
  if (socket && socket.active) {
    const binaryData = encodePlayerChat(chatId, pid, data.toJSON());
    socket.emit(getFinallyKey(SocketEvents.PLAYER_CHAT), binaryData);
  }
}

function sendCutTree(treeTag: number, treeServerId: string, useItemId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_CUT_TREE,
        game.ClientCutTree.create({
          treeTag,
          treeServerId,
          useItemId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendMiningRock(rockTag: number, useItemId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_MINING_ROCK,
        game.ClientMiningRock.create({
          rockTag,
          useItemId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendFishingSuccess(fishRecordId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_FISHING_SUCCESS,
        game.ClientFishingSuccess.create({
          fishRecordId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendPickUpDrop(pickUpId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PICK_UP_DROP,
        game.ClientPickUpDrop.create({
          dropTag: pickUpId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendEntityEnter(clientIndex: number, componentList: number[]) {
  const socket = _socket;
  if (socket && socket.active) {
    if (!isEnterRoom) {
      return;
    }
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_ENTITY_ENTER,
        game.EntityEnter.create({
          clientIndex,
          componentList,
        }).toJSON()
      )
    );
  }
}

function sendEntityLeave(clientIndex: number) {
  const socket = _socket;
  if (socket && socket.active) {
    if (!isEnterRoom) {
      return;
    }
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_ENTITY_LEAVE,
        game.EntityLeave.create({
          clientIndex,
        }).toJSON()
      )
    );
  }
}

function sendEntityComponentAction(
  clientIndex: number,
  componentType: number,
  actionType: number,
  actionData: Uint8Array
) {
  const socket = _socket;
  if (socket && socket.active) {
    if (!isEnterRoom) {
      return;
    }
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_COMPONENT_ACTION,
        game.EntityComponentAction.create({
          clientIndex,
          componentType,
          actionType,
          actionData,
        }).toJSON()
      )
    );
  }
}

function sendLarkMessage(title: string, connect: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_LARK_MESSAGE,
        game.LarkMessage.create({
          title,
          connect,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

async function getRoomList() {
  const socket = _socket;
  if (socket && socket.active) {
    const response: GameNodeInfo[] = await socket
      .timeout(10000)
      .emitWithAck(getFinallyKey(SocketEvents.GET_ROOM_LIST), encodePlayerCommon([]));
    return response;
  }
  return [];
}

async function request(pid: game.ClientRequestTypes, body: any) {
  const socket = _socket;
  if (socket && socket.active) {
    const binaryData = encodePlayerRequest(pid, body);
    return socket.emitWithAck(getFinallyKey(SocketEvents.PLAYER_REQUEST), binaryData);
  }
  return null;
}

function enterRoom(mapId: number, index: number) {
  if (!_socket) {
    return;
  }
  const socket = _socket;
  const myPlayer = GetMyPlayer();
  GlobalSpaceEvent.ListenKeyDataChange<AvatarData>(
    GlobalDataKey.MyAvatarData,
    (avatarData) => {
      socket.emit(
        getFinallyKey(SocketEvents.JOIN_ROOM),
        encodePlayerCommon([String(mapId), String(index)])
      );
    },
    true
  );
}

function leaveRoom() {
  if (!_socket) {
    return;
  }
  if (!isEnterRoom) {
    return;
  }
  _mapId = 0;
  _mapIndex = 0;
  const socket = _socket;
  isEnterRoom = false;
  changeRoomCall.forEach((callback) => {
    callback({ isEnterRoom: false, mapId: 0, mapIndex: 0 });
  });
  NetPlayerManager.getInstance().clearAllPlayer();
  socket.emit(getFinallyKey(SocketEvents.LEAVE_ROOM), encodePlayerCommon([]));
}

function sendPetCutTree(treeTag: number, treeServerId: string, petId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PET_CUT_TREE,
        game.ClientPetCutTree.create({
          treeTag,
          treeServerId,
          petId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendPetMining(rockTag: number, rockServerId: string, petId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PET_MINING_ROCK,
        game.ClientPetMiningRock.create({
          rockTag,
          rockServerId,
          petId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendPetFishing(petId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PET_FISHING_SUCCESS,
        game.ClientPetFishingSuccess.create({
          petId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

startHeartbeat();

export function useNetWork() {
  return {
    connect,
    disConnect,
    isConnected,
    watchRoomStatus,
    getRoomStatus,
    sendMapId,
    sendMapPosition,
    sendChatMsg,
    sendChatMsgDelete,
    sendCutTree,
    sendMiningRock,
    sendFishingSuccess,
    sendPickUpDrop,
    sendLarkMessage,
    sendEntityEnter,
    sendEntityLeave,
    sendEntityComponentAction,
    getRoomList,
    request,
    enterRoom,
    leaveRoom,
    sendPetCutTree,
    sendPetMining,
    sendPetFishing,
  };
}
