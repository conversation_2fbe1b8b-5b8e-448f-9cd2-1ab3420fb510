import { game } from './generated/game_messages';
import { c2sMessageMap, clientRequestMap } from './client-protobuf-handler';
import { sign } from './protobuf-until';

/**
 * 编码玩家动作消息
 * @param pid 动作类型
 * @param data 动作数据
 * @returns protobuf二进制数据
 */
export function encodePlayerAction(pid: game.C2SPacketType, data: any): Buffer | undefined {
  try {

    const MessageClass = c2sMessageMap.get(pid);
    if (!MessageClass) {
      console.error(`Unsupported packet type: ${pid}`);
      return;
    }
    const messageData = MessageClass.create(data);
    const messageBuffer = MessageClass.encode(messageData).finish();
    // 创建动作消息包装器
    const actionMessage = game.PlayerActionMessage.create({
      pid: pid,
      actionData: messageBuffer,
      // timestamp: Date.now(),
    });

    return Buffer.from(game.PlayerActionMessage.encode(actionMessage).finish());
  } catch (error) {
    console.error('Error encoding player action:', error);
    throw error;
  }
}

//
// /**
//  * 编码玩家请求消息
//  * @param pid 请求类型
//  * @param data 请求数据
//  * @returns protobuf二进制数据
//  */
export function encodePlayerRequest(pid: game.ClientRequestTypes, data: any): Buffer | undefined {
  try {
    const requestClass = clientRequestMap.get(pid);
    if (!requestClass) {
      console.error(`Unsupported packet type: ${pid}`);
      return;
    }
    const requestData = requestClass.create(data);
    const requestBuffer = requestClass.encode(requestData).finish();
    // 创建动作消息包装器
    const reqMessage = game.PlayerRequestMessage.create({
      pid: pid,
      requestData: requestBuffer,
      timestamp: Date.now(),
    });

    return Buffer.from(game.PlayerRequestMessage.encode(reqMessage).finish());
  } catch (error) {
    console.error('Error encoding player action:', error);
    throw error;
  }
}

/**
 * 编码玩家聊天消息
 * @param chatId 聊天房间ID
 * @param pid 聊天消息类型
 * @param data 聊天数据
 * @returns protobuf二进制数据
 */
export function encodePlayerChat(chatId: number, pid: game.C2SPacketType, data: any): Buffer | undefined {
  try {
    let chatBuffer: Uint8Array = new Uint8Array(0);

    const chatClass = c2sMessageMap.get(pid);
    if (chatClass) {
      // 创建聊天消息包装器
      const chatData = chatClass.create(data);
      chatBuffer = chatClass.encode(chatData).finish();
    }

    const chatMessage = game.PlayerChatMessage.create({
      chatId: chatId,
      pid: pid,
      chatData: chatBuffer,
      timestamp: Date.now(),
    });
    return Buffer.from(game.PlayerChatMessage.encode(chatMessage).finish());
  } catch (error) {
    console.error('Error encoding player chat:', error);
    throw error;
  }
}

/**
 * 编码玩家动作消息
 * @param pid 动作类型
 * @param data 动作数据
 * @returns protobuf二进制数据
 */
export function encodePlayerLogic(pid: game.C2SPacketType, data: any): Buffer | undefined {
  try {

    const MessageClass = c2sMessageMap.get(pid);
    if (!MessageClass) {
      console.error(`Unsupported packet type: ${pid}`);
      return;
    }
    const messageData = MessageClass.create(data);
    const messageBuffer = MessageClass.encode(messageData).finish();
    const timestamp = Date.now();
    const signStr = sign(pid, data, timestamp);
    // 创建动作消息包装器
    const logicMessage = game.PlayerLogicMessage.create({
      pid: pid,
      logicData: messageBuffer,
      timestamp,
      sign: signStr,
    });

    return Buffer.from(game.PlayerLogicMessage.encode(logicMessage).finish());
  } catch (error) {
    console.error('Error encoding player action:', error);
    throw error;
  }
}

export function encodePlayerCommon(messageList: string[]): Buffer | undefined {
  try {
    const commonMessage = game.CommonMessage.create({
      messageList,
    });
    return Buffer.from(game.CommonMessage.encode(commonMessage).finish());
  } catch (error) {
    console.error('Error encoding common message:', error);
    throw error;
  }
}
