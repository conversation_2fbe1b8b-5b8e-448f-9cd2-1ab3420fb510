import { game } from './generated/game_messages';
import { sign, transformBuffer } from './protobuf-until';

export const c2sMessageMap = new Map<game.C2SPacketType, any>([
  [game.C2SPacketType.C2S_PLAYER_ENTER, game.CommonMessage],
  [game.C2SPacketType.C2S_PLAYER_LEAVE, null], // 无需额外数据
  [game.C2SPacketType.C2S_CHAT_ENTER, null], // 无需额外数据
  [game.C2SPacketType.C2S_CHAT_LEAVE, null], // 无需额外数据
  [game.C2SPacketType.C2S_CHAT_MESSAGE, game.ChatMessage],
  [game.C2SPacketType.C2S_CHAT_MESSAGE_DELETE, game.ChatMessageDelete],
  [game.C2SPacketType.C2S_CUT_TREE, game.ClientCutTree],
  [game.C2SPacketType.C2S_MINING_ROCK, game.ClientMiningRock],
  [game.C2SPacketType.C2S_FISHING_SUCCESS, game.ClientFishingSuccess],
  [game.C2SPacketType.C2S_PICK_UP_DROP, game.ClientPickUpDrop],
  [game.C2SPacketType.C2S_PLAYER_POSITION_UPDATE, game.CommonMessage],
  [game.C2SPacketType.C2S_PLAYER_MAP_UPDATE, game.CommonMessage],
  [game.C2SPacketType.C2S_LARK_MESSAGE, game.LarkMessage],
  [game.C2SPacketType.C2S_ENTITY_ENTER, game.EntityEnter],
  [game.C2SPacketType.C2S_ENTITY_LEAVE, game.EntityLeave],
  [game.C2SPacketType.C2S_COMPONENT_ACTION, game.EntityComponentAction],
  [game.C2SPacketType.C2S_PET_CUT_TREE, game.ClientPetCutTree],
  [game.C2SPacketType.C2S_PET_MINING_ROCK, game.ClientPetMiningRock],
  [game.C2SPacketType.C2S_PET_FISHING_SUCCESS, game.ClientPetFishingSuccess],
]);
// 客户端请求类型映射
export const clientRequestMap = new Map<game.ClientRequestTypes, any>([
  [game.ClientRequestTypes.PICK_UP_RED_PACKET, game.ReqPickUpRedPacket], // 使用通用数据结构
]);

/**
 * 解析客户端动作消息
 * @param buffer protobuf二进制数据
 * @returns 解析后的动作数据
 */
export function decodePlayerAction(buffer: Buffer | ArrayBuffer | Uint8Array): { pid: game.C2SPacketType; data: any } {
  try {
    // 确保buffer是正确的格式，转换为Uint8Array
    const processedBuffer = transformBuffer(buffer);
    if (!processedBuffer) {
      throw new Error(`Invalid buffer type: ${typeof buffer}, constructor:`);
    }

    // 解码动作消息包装器
    const actionMessage = game.PlayerActionMessage.decode(processedBuffer);
    const pid = actionMessage.pid as game.C2SPacketType;

    // 获取对应的消息类型
    const MessageClass = c2sMessageMap.get(pid);

    let data: any = {};

    if (MessageClass && actionMessage.actionData && actionMessage.actionData.length > 0) {
      // 解码内部数据
      const innerMessage = MessageClass.decode(actionMessage.actionData);
      data = innerMessage.toJSON();
    }

    return {
      pid,
      data,
    };
  } catch (error) {
    console.error('Error decoding player action:', error);
    throw error;
  }
}


/**
 * 解析客户端请求消息
 * @param buffer protobuf二进制数据
 * @returns 解析后的请求数据
 */
export function decodePlayerRequest(buffer: Buffer): { pid: game.ClientRequestTypes; data: any } {
  try {

    // 确保buffer是正确的格式，转换为Uint8Array
    const processedBuffer = transformBuffer(buffer);
    if (!processedBuffer) {
      throw new Error(`Invalid buffer type: ${typeof buffer}, constructor:`);
    }

    // 解码请求消息包装器
    const requestMessage = game.PlayerRequestMessage.decode(processedBuffer);

    const pid = requestMessage.pid as game.ClientRequestTypes;

    // 获取对应的消息类型
    const requestClass = clientRequestMap.get(pid);


    let data: any = {};
    if (requestClass && requestMessage.requestData && requestMessage.requestData.length > 0) {
      // 解码内部数据
      const innerMessage = requestClass.decode(requestMessage.requestData);
      data = innerMessage.toJSON();
    }

    return {
      pid,
      data,
    };
  } catch (error) {
    console.error('Error decoding player request:', error);
    throw error;
  }
}

/**
 * 解析客户端聊天消息
 * @param buffer protobuf二进制数据
 * @returns 解析后的聊天数据
 */
export function decodePlayerChat(buffer: Buffer | ArrayBuffer | Uint8Array): {
  chatId: number;
  pid: game.C2SPacketType;
  data: any
} {
  try {
    const processedBuffer = transformBuffer(buffer);
    if (!processedBuffer) {
      throw new Error(`Invalid buffer type: ${typeof buffer}, constructor:`);
    }
    // 解码聊天消息包装器
    const chatMessage = game.PlayerChatMessage.decode(processedBuffer);
    const chatId = chatMessage.chatId;
    const pid = chatMessage.pid as game.C2SPacketType;

    let data: any = {};

    const messageClass = c2sMessageMap.get(pid);
    if (messageClass && chatMessage.chatData && chatMessage.chatData.length > 0) {
      // 解码内部数据
      const innerMessage = messageClass.decode(chatMessage.chatData);
      data = innerMessage.toJSON();
    }


    return {
      chatId,
      pid,
      data,
    };
  } catch (error) {
    console.error('Error decoding player chat:', error);
    throw error;
  }
}

/**
 * 解析客户端动作消息
 * @param buffer protobuf二进制数据
 * @returns 解析后的动作数据
 */
export function decodePlayerLogic(buffer: Buffer | ArrayBuffer | Uint8Array): {
  pid: game.C2SPacketType;
  data: any,
  timestamp: number
} {
  try {
    // 确保buffer是正确的格式，转换为Uint8Array
    const processedBuffer = transformBuffer(buffer);
    if (!processedBuffer) {
      throw new Error(`Invalid buffer type: ${typeof buffer}, constructor:`);
    }

    // 解码动作消息包装器
    const logicMessage = game.PlayerLogicMessage.decode(processedBuffer);
    const pid = logicMessage.pid as game.C2SPacketType;
    const now = Date.now();
    const timestamp = Number(logicMessage.timestamp);
    // if (now - logicTime > 5 * 1000 || logicTime > now) {
    //   console.error('PLAYER_LOGIC', now - Number(logicMessage.timestamp));
    //   throw new Error(`decodePlayerLogic Invalid timestamp`);
    // }

    // 获取对应的消息类型
    const MessageClass = c2sMessageMap.get(pid);

    let data: any = {};

    if (MessageClass && logicMessage.logicData && logicMessage.logicData.length > 0) {
      // 解码内部数据
      const innerMessage = MessageClass.decode(logicMessage.logicData);
      data = innerMessage.toJSON();
    }
    const signStr = sign(pid, data, timestamp);
    if (signStr !== logicMessage.sign) {
      console.error('PLAYER_LOGIC sign error', pid, data, timestamp, signStr, logicMessage.sign);
      throw new Error(`decodePlayerLogic Invalid sign`);
    }
    return {
      pid,
      data,
      timestamp,
    };
  } catch (error) {
    console.error('Error decoding player action:', error);
    throw error;
  }
}


/**
 * 解析客户端动作消息
 * @param buffer protobuf二进制数据
 * @returns 解析后的动作数据
 */
export function decodePlayerCommon(buffer: Buffer | ArrayBuffer | Uint8Array): string[] {
  try {
    // 确保buffer是正确的格式，转换为Uint8Array
    const processedBuffer = transformBuffer(buffer);
    if (!processedBuffer) {
      throw new Error(`Invalid buffer type: ${typeof buffer}, constructor:`);
    }

    // 解码动作消息包装器
    const commonMessage = game.CommonMessage.decode(processedBuffer);
    return commonMessage.messageList;
  } catch (error) {
    console.error('Error decoding player action:', error);
    throw error;
  }
}

/**
 * 检查是否支持指定的消息类型
 */
export function isActionSupported(packetType: game.C2SPacketType): boolean {
  return c2sMessageMap.has(packetType);
}

/**
 * 检查是否支持指定的请求类型
 */
// public isRequestSupported(requestType: ClientRequestTypes): boolean {
//   return this.clientRequestMap.has(requestType);
// }

/**
 * 获取支持的动作类型列表
 */
export function getSupportedActionTypes(): game.C2SPacketType[] {
  return Array.from(c2sMessageMap.keys());
}

/**
 * 获取支持的请求类型列表
 */
// public getSupportedRequestTypes(): ClientRequestTypes[] {
//   return Array.from(this.clientRequestMap.keys());
// }

/**
 * 尝试检测数据是否为protobuf格式
 * @param data 接收到的数据
 * @returns 是否为protobuf格式
 */
export function isProtobufData(data: any): boolean {
  // 如果数据是Buffer或Uint8Array，可能是protobuf
  if (Buffer.isBuffer(data) || data instanceof Uint8Array) {
    return true;
  }

  // 如果数据是对象且包含特定的protobuf标识，也可能是protobuf
  if (typeof data === 'object' && data !== null) {
    // 检查是否有protobuf的特征字段
    return false; // 对于JSON对象，返回false
  }

  return false;
}
