import { game } from './generated/game_messages';
import ProtobufHandler from './protobuf-handler';

/**
 * Protobuf消息处理器
 * 负责在JSON和Protobuf之间进行转换
 */
export class ProtobufEncoder {
  private static instance: ProtobufEncoder;

  public static getInstance(): ProtobufEncoder {
    if (!ProtobufEncoder.instance) {
      ProtobufEncoder.instance = new ProtobufEncoder();
    }
    return ProtobufEncoder.instance;
  }

  /**
   * 将JSON数据编码为protobuf二进制数据
   * @param pid 消息类型
   * @param data JSON数据
   * @returns protobuf二进制数据
   */
  public encodeMessage(pid: game.S2CPacketType, data: any): Buffer | undefined {
    try {

      let messageData: Uint8Array = new Uint8Array(0);
      const MessageClass = ProtobufHandler.s2cMessageMap.get(pid);
      if (MessageClass) {
        // 创建消息实例
        const message = MessageClass.create(data);
        // 编码为二进制
        messageData = MessageClass.encode(message).finish();
      }
      // 创建包装消息
      // const gameMessage = game.GameMessage.create({
      //   pid: pid,
      //   data: messageData,
      //   timestamp: Date.now()
      // });
      const gameMessage = game.GameMessage.create({
        pid: pid,
        data: messageData,
        // timestamp: Date.now(),
      });
      return Buffer.from(game.GameMessage.encode(gameMessage).finish());
    } catch (error) {
      console.error('Error encoding protobuf message:', error);
      throw error;
    }
  }

  /**
   * 检查是否支持指定的消息类型
   */
  public isSupported(packetType: game.S2CPacketType): boolean {
    return ProtobufHandler.s2cMessageMap.has(packetType);
  }
}

export default ProtobufEncoder;
