import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

export default class CustomOrbitControls extends OrbitControls {
  constructor(object, domElement, flip = false) {
    super(object, domElement);

    if (flip) {
      const oldMouseMove = this._onMouseMove;
      this._onMouseMove = function (event) {
        const newEvent = {
          ...event,
          pageX: event.pageY,
          pageY: -event.pageX,
        };
        oldMouseMove.call(this, newEvent);
      };
      const oldTouchMove = this._onTouchMove;
      this._onTouchMove = function (event) {
        const newEvent = {
          ...event,
          pageX: event.pageY,
          pageY: -event.pageX,
        };
        oldTouchMove.call(this, newEvent);
      };
      const oldTouchStart = this._onTouchStart;
      this._onTouchStart = function (event) {
        const newEvent = {
          ...event,
          pageX: event.pageY,
          pageY: -event.pageX,
        };
        oldTouchStart.call(this, newEvent);
      };
    }
  }
}
