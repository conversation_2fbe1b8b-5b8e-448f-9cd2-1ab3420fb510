import Image from 'next/image';
import styled from 'styled-components';

interface CloseProps {
  onClick: () => void;
}
const StyledImage = styled(Image)`
  width: 3.5rem;
  height: 3.5rem;
`;

const Close = ({ onClick }: CloseProps) => {
  return (
    <div onClick={onClick}>
      <StyledImage src="/image/close.png" alt="close" width={56} height={56} draggable={false} />
    </div>
  );
};

export default Close;
