import styled from 'styled-components';
import React, { CSSProperties, ReactNode } from 'react';
import Close from './Close';
import ButtonLoading from './ButtonLoading';

type ModalProps = {
  title: ReactNode; // 修改为ReactNode类型，可以接受任何React元素
  children: ReactNode;
  onClose: () => void;
  onCancel?: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
  closeButton?: boolean;
  confirmDisabled?: boolean;
  confirmLoading?: boolean;
  modalBodyPadding?: string;
  maxHeight?: string;
  modalWidth?: string;
  footerStyle?: React.CSSProperties;
  buttonStyle?: React.CSSProperties;
  modalHeight?: string;
  className?: string;
  modalHeaderStyle?: CSSProperties;
  modalCloseBtnStyle?: CSSProperties;
};

const ModalContentContainer = styled.div<{
  modalWidth?: string;
  modalHeight?: string;
}>`
  width: ${({ modalWidth }) => modalWidth || '41.25rem'};
  height: ${({ modalHeight }) => modalHeight || 'auto'};
  background: #fef1df;
  border: 0.125rem solid #000; /* 外层黑色边框 */
  box-shadow: inset 0 0 0 0.375rem #ff9f1c; /* 内层黄色边框 */
  border-radius: 2.25rem;
  position: relative;
  padding: 0.25rem; /* 确保内层边框有足够空间显示 */
`;

const ModalHeader = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 50%;
  top: -2.1875rem;
  transform: translate(-50%, 0rem);
  z-index: 999;
`;

const CloseButton = styled.div`
  position: absolute;
  right: 1rem;
  top: 30%;
  cursor: pointer;
`;

const ModalBody = styled.div<{ padding?: string; _maxHeight?: string }>`
  padding: ${({ padding }) => padding || '1.25rem 1.25rem 1.25rem'};
  max-height: ${({ _maxHeight }) => _maxHeight || '25rem'};
  overflow-y: auto;
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 0.375rem 2.625rem 1.5rem;
`;

const Button = styled.button<{ disabled?: boolean }>`
  padding: 0.75rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  font-weight: bold;
  cursor: pointer;
  min-width: 11.25rem;
  border: none;
  color: white;
  &.confirm {
    background: #fc7922;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
    border-bottom: ${({ disabled }) =>
      disabled ? '0.25rem solid #b8a692' : '0.25rem solid #b5581a'};
    position: relative;
    transition:
      transform 0.1s,
      box-shadow 0.1s,
      border-bottom 0.1s;

    &:hover {
      background: #ff8a3c; /* 稍亮的颜色 */
      cursor: pointer;
    }

    &:not(:disabled):active {
      transform: translateY(0.1875rem);
      box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.2);
      border-bottom: 0.0625rem solid #b5581a;
    }

    &:disabled {
      background: #c1af9c;
      cursor: not-allowed;
    }
  }
  &.cancel {
    background: #c1af9c;
  }
`;

const ModalContent: React.FC<ModalProps> = ({
  title,
  children,
  onClose,
  onCancel,
  onConfirm,
  confirmText = 'Drop',
  cancelText = 'Cancel',
  closeButton = true,
  confirmDisabled = false,
  confirmLoading = false,
  modalBodyPadding,
  maxHeight,
  modalWidth,
  footerStyle,
  buttonStyle,
  modalHeight,
  modalHeaderStyle = {},
  modalCloseBtnStyle = {},
  className = '',
}) => {
  return (
    <ModalContentContainer modalWidth={modalWidth} modalHeight={modalHeight} className={className}>
      <ModalHeader style={modalHeaderStyle}>
        {title}
        {closeButton && (
          <CloseButton style={modalCloseBtnStyle}>
            <Close onClick={onClose} />
          </CloseButton>
        )}
      </ModalHeader>

      <ModalBody padding={modalBodyPadding} _maxHeight={maxHeight}>
        {children}
      </ModalBody>

      {(onCancel || onConfirm) && (
        <ModalFooter
          style={{
            ...footerStyle,
          }}>
          {onCancel && (
            <Button
              className="cancel"
              style={{
                ...buttonStyle,
              }}
              onClick={onCancel}>
              {cancelText}
            </Button>
          )}
          {onConfirm && (
            <Button
              className="confirm"
              onClick={() => {
                onConfirm();
              }}
              disabled={confirmDisabled}
              style={{
                ...buttonStyle,
              }}>
              {confirmLoading ? (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ButtonLoading />
                  Loading...
                </div>
              ) : (
                confirmText
              )}
            </Button>
          )}
        </ModalFooter>
      )}
    </ModalContentContainer>
  );
};

export default ModalContent;
