import * as THREE from 'three';
import { type ThreeElements } from '@react-three/fiber';
import React, { forwardRef, type ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useJoystickControls } from './stores/useJoystickControls';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import styled from 'styled-components';

const JoystickComponents = (props: EcctrlJoystickProps) => {
  /**
   * Preset values/components
   */
  let joystickCenterX: number = 0;
  let joystickCenterY: number = 0;
  let joystickHalfWidth: number = 0;
  let joystickHalfHeight: number = 0;
  let joystickMaxDis: number = 0;
  let joystickDis: number = 0;
  let joystickAng: number = 0;
  const touch1MovementVec2 = useMemo(() => new THREE.Vector2(), []);
  const joystickMovementVec2 = useMemo(() => new THREE.Vector2(), []);

  const [windowSize, setWindowSize] = useState({ innerHeight, innerWidth });
  const joystickDivRef = useRef<HTMLDivElement>(null);
  const [buttonPos, setButtonPos] = useState({ x: 0, y: 0 });
  const [joystickState, setJoystickState] = useState({ dis: 0, ang: 0, maxDis: 0 });

  const [isMobileFlip, setIsMobileFlip] = useState(false);

  useEffect(() => {
    const unsubscribe = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.IsMobileFlip,
      (value) => {
        setIsMobileFlip(value);
      },
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.IsMobileFlip, unsubscribe);
    };
  }, []);
  /**
   * Joystick store setup
   */
  const setJoystick = useJoystickControls((state) => state.setJoystick);
  const resetJoystick = useJoystickControls((state) => state.resetJoystick);

  // Touch move function
  const onTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault();
    e.stopImmediatePropagation();
    const touch1 = e.targetTouches[0];

    const touch1MovementX = isMobileFlip ? touch1.pageY - joystickCenterY : touch1.pageX - joystickCenterX;
    const touch1MovementY = isMobileFlip ? touch1.pageX - joystickCenterX : -(touch1.pageY - joystickCenterY);
    touch1MovementVec2.set(touch1MovementX, touch1MovementY);

    joystickDis = Math.min(Math.sqrt(Math.pow(touch1MovementX, 2) + Math.pow(touch1MovementY, 2)), joystickMaxDis);
    joystickAng = touch1MovementVec2.angle();
    joystickMovementVec2.set(joystickDis * Math.cos(joystickAng), joystickDis * Math.sin(joystickAng));
    const runState = joystickDis > joystickMaxDis * (props.joystickRunSensitivity ?? 0.9);

    // Apply animations
    const maxMovement = joystickMaxDis * 0.65; // 最大移动距离
    const normalizedDis = Math.min(joystickDis / joystickMaxDis, 1); // 标准化距离
    const buttonMoveX = Math.cos(joystickAng) * normalizedDis * maxMovement;
    const buttonMoveY = -Math.sin(joystickAng) * normalizedDis * maxMovement;

    setButtonPos({
      x: buttonMoveX,
      y: buttonMoveY,
    });

    // 更新摇杆状态供箭头使用
    setJoystickState({
      dis: joystickDis,
      ang: joystickAng,
      maxDis: joystickMaxDis,
    });

    // Pass valus to joystick store
    setJoystick(joystickDis, joystickAng, runState);
  }, [windowSize, isMobileFlip]);

  // Touch end function
  const onTouchEnd = (e: TouchEvent) => {
    setButtonPos({
      x: 0,
      y: 0,
    });
    // 重置摇杆状态
    setJoystickState({
      dis: 0,
      ang: 0,
      maxDis: joystickMaxDis,
    });
    // Reset joystick store values
    resetJoystick();
  };

  // Reset window size function
  const onWindowResize = () => {
    setWindowSize({ innerHeight: window.innerHeight, innerWidth: window.innerWidth });
  };

  useEffect(() => {
    if (!joystickDivRef.current) return;
    const joystickPositionX = joystickDivRef.current.getBoundingClientRect().x;
    const joystickPositionY = joystickDivRef.current.getBoundingClientRect().y;
    joystickHalfWidth = joystickDivRef.current.getBoundingClientRect().width / 2;
    joystickHalfHeight = joystickDivRef.current.getBoundingClientRect().height / 2;

    joystickMaxDis = joystickHalfWidth;

    joystickCenterX = joystickPositionX + joystickHalfWidth;
    joystickCenterY = joystickPositionY + joystickHalfHeight;

    joystickDivRef.current.addEventListener('touchmove', onTouchMove, { passive: false });
    joystickDivRef.current.addEventListener('touchend', onTouchEnd);

    window.visualViewport.addEventListener('resize', onWindowResize);

    return () => {
      if (joystickDivRef.current) {
        joystickDivRef.current.removeEventListener('touchmove', onTouchMove);
        joystickDivRef.current.removeEventListener('touchend', onTouchEnd);
      }
      window.visualViewport.removeEventListener('resize', onWindowResize);
    };
  });

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }} ref={joystickDivRef}>
      {/* 摇杆背景 */}
      <img
        src="/image/joystick/stick_bg.png"
        style={{
          width: '100%',
          height: '100%',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1,
        }}
        alt="joystick background"
      />

      {/* 摇杆按钮 - 使用 springs 动画值 */}
      <img
        src="/image/joystick/stick_button.png"
        style={{
          width: '40%',
          height: '40%',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: `translate(-50%, -50%) translate(${buttonPos.x}px, ${buttonPos.y}px)`,
          zIndex: 2,
        }}
        alt="joystick button"
      />

      {/* 摇杆方向箭头 - 显示在外边缘 */}
      {/*{joystickState.dis > 0 && (*/}
      <div
        style={{
          top: '50%',
          left: '50%',
          position: 'absolute',
          // 将箭头放置在背景圆的外边缘
          transform: `rotate(${-joystickState.ang + Math.PI / 2}rad)`,
          zIndex: 3,
          transition: 'opacity 0.1s ease-out',
        }}>
        <img
          src="/image/joystick/stick_arrow.png"
          style={{
            width: '2rem',
            height: '0.8rem',
            position: 'absolute',
            top: '-7.2rem',
            // 将箭头放置在背景圆的外边缘
            transform: `translate(-50%, -50%)`,
            zIndex: 3,
            opacity: joystickState.maxDis > 0 ? Math.min(joystickState.dis / joystickState.maxDis, 1) : 0,
            transition: 'opacity 0.1s ease-out',
          }}
          alt="joystick direction"
        />
      </div>

      {/*)}*/}
    </div>
  );
};

const ButtonComponents = ({ buttonNumber = 1, ...props }: EcctrlJoystickProps) => {
  /**
   * 2D UI 按钮状态管理
   */
  const [buttonStates, setButtonStates] = useState({
    button1Pressed: false,
    button2Pressed: false,
    button3Pressed: false,
    button4Pressed: false,
    button5Pressed: false,
  });

  // 移除动画配置，使用简单的状态管理

  /**
   * Button store setup
   */
  const pressButton1 = useJoystickControls((state) => state.pressButton1);
  const pressButton2 = useJoystickControls((state) => state.pressButton2);
  const pressButton3 = useJoystickControls((state) => state.pressButton3);
  const pressButton4 = useJoystickControls((state) => state.pressButton4);
  const pressButton5 = useJoystickControls((state) => state.pressButton5);
  const releaseAllButtons = useJoystickControls((state) => state.releaseAllButtons);

  // 按钮按下事件处理
  const onButtonDown = (number: number) => {
    // 更新按钮状态
    setButtonStates(prev => ({
      ...prev,
      [`button${number}Pressed`]: true,
    }));

    // 触发按钮事件
    switch (number) {
      case 1:
        pressButton1();
        break;
      case 2:
        pressButton2();
        break;
      case 3:
        pressButton3();
        break;
      case 4:
        pressButton4();
        break;
      case 5:
        pressButton5();
        break;
      default:
        break;
    }
  };

  // 按钮释放事件处理
  const onButtonUp = (number?: number) => {
    if (number) {
      // 单个按钮释放
      setButtonStates(prev => ({
        ...prev,
        [`button${number}Pressed`]: false,
      }));
    } else {
      // 所有按钮释放
      setButtonStates({
        button1Pressed: false,
        button2Pressed: false,
        button3Pressed: false,
        button4Pressed: false,
        button5Pressed: false,
      });
    }

    // 释放所有按钮状态
    releaseAllButtons();
  };

  // 全局按钮释放事件监听
  useEffect(() => {
    const handleGlobalPointerUp = () => {
      onButtonUp();
    };

    document.addEventListener('pointerup', handleGlobalPointerUp);
    document.addEventListener('touchend', handleGlobalPointerUp);

    return () => {
      document.removeEventListener('pointerup', handleGlobalPointerUp);
      document.removeEventListener('touchend', handleGlobalPointerUp);
    };
  }, []);

  const positionList = [
    { top: '70%', left: '75%' },
    { top: '40%', left: '60%' },
    { top: '25%', left: '25%' },
    { top: '70%', left: '15%' },
    { top: '5%', left: '55%' },
  ];

  const getButtonSize = (buttonNum: number) => {
    // 按钮1和2使用大尺寸，其他使用小尺寸
    return buttonNum <= 2 ? '30%' : '20%';
  };

  const isButtonPress = (buttonNum: number) => {
    return buttonStates[`button${buttonNum}Pressed`];
  };

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      {
        positionList.map((item, index) => (
          <div
            key={index + 1}
            style={{
              position: 'absolute',
              ...item,
              width: getButtonSize(index + 1),
              height: getButtonSize(index + 1),
              transform: 'translate(-50%, -50%)',
              cursor: 'pointer',
              userSelect: 'none',
              zIndex: 10,
              //index 小于 buttonNumber 不显示
              display: index < buttonNumber ? 'block' : 'none',
            }}
            onPointerDown={(e) => {
              onButtonDown(index + 1);
            }}
            onTouchStart={(e) => {
              onButtonDown(index + 1);
            }}
          >
            <img
              src={isButtonPress(index + 1) ? '/image/joystick/jump_press.png' : '/image/joystick/jump_normal.png'}
              style={{
                width: '91.5%',
                height: '100%',
              }
              }
              alt="按钮"
              draggable={false}
            />
          </div>
        ))
      }
    </div>
  );
};

const EcctrlButtonWrapper = styled.div`
  user-select: none;
  touch-action: none;
  overscroll-behavior: none;
  position: fixed;
  z-index: 9999;
  height: 25rem;
  width: 25rem;
  right: 8rem;
  bottom: 4rem;
  pointer-events: none;
  & > div {
    pointer-events: none;
    & > div {
      pointer-events: all;
    }
  }
`;


export const EcctrlJoystick = forwardRef<HTMLDivElement, EcctrlJoystickProps>((props, ref) => {
  const joystickWrapperStyle: React.CSSProperties = {
    userSelect: 'none',
    MozUserSelect: 'none',
    WebkitUserSelect: 'none',
    msUserSelect: 'none',
    touchAction: 'none',
    // pointerEvents: 'none', // 移除鼠标事件拦截，允许直接点击
    overscrollBehavior: 'none',
    position: 'fixed',
    zIndex: '9999',
    height: props.joystickHeightAndWidth || '12.5rem',
    width: props.joystickHeightAndWidth || '12.5rem',
    left: props.joystickPositionLeft || '10rem',
    bottom: props.joystickPositionBottom || '6rem',
  };

  const buttonWrapperStyle: React.CSSProperties = {
    userSelect: 'none',
    MozUserSelect: 'none',
    WebkitUserSelect: 'none',
    msUserSelect: 'none',
    touchAction: 'none',
    // pointerEvents: 'none', // 移除，允许2D按钮交互
    overscrollBehavior: 'none',
    position: 'fixed',
    zIndex: '9999',
    height: props.joystickHeightAndWidth || '25rem',
    width: props.joystickHeightAndWidth || '25rem',
    right: props.buttonPositionRight || '8rem',
    bottom: props.buttonPositionBottom || '4rem',
  };

  return (
    <div ref={ref}>
      <div id="ecctrl-joystick" style={joystickWrapperStyle} onContextMenu={(e) => e.preventDefault()}>
        <JoystickComponents {...props} />
      </div>
      {
        props.buttonNumber !== 0 &&
        <EcctrlButtonWrapper id="ecctrl-button"  onContextMenu={(e) => e.preventDefault()}>
          <ButtonComponents {...props} />
        </EcctrlButtonWrapper>
      }
    </div>
  );
});

export type EcctrlJoystickProps = {
  // Joystick props
  children?: ReactNode;
  joystickRunSensitivity?: number;
  joystickPositionLeft?: number;
  joystickPositionBottom?: number;
  joystickHeightAndWidth?: number;
  joystickCamZoom?: number;
  joystickCamPosition?: [x: number, y: number, z: number];
  joystickBaseProps?: ThreeElements['mesh'];
  joystickStickProps?: ThreeElements['mesh'];
  joystickHandleProps?: ThreeElements['mesh'];

  // Touch buttons props
  buttonNumber?: number;
  buttonPositionRight?: number;
  buttonPositionBottom?: number;
  buttonHeightAndWidth?: number;
  buttonCamZoom?: number;
  buttonCamPosition?: [x: number, y: number, z: number];
  buttonGroup1Position?: [x: number, y: number, z: number];
  buttonGroup2Position?: [x: number, y: number, z: number];
  buttonGroup3Position?: [x: number, y: number, z: number];
  buttonGroup4Position?: [x: number, y: number, z: number];
  buttonGroup5Position?: [x: number, y: number, z: number];
  buttonLargeBaseProps?: ThreeElements['mesh'];
  buttonSmallBaseProps?: ThreeElements['mesh'];
  buttonTop1Props?: ThreeElements['mesh'];
  buttonTop2Props?: ThreeElements['mesh'];
  buttonTop3Props?: ThreeElements['mesh'];
  buttonTop4Props?: ThreeElements['mesh'];
  buttonTop5Props?: ThreeElements['mesh'];
};